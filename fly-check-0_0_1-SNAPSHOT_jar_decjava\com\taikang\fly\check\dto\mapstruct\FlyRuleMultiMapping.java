package com.taikang.fly.check.dto.mapstruct.FlyRuleMultiMapping;
import com.taikang.fly.check.dto.flyRule.FlyRuleAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleMulti;
import com.taikang.fly.check.dto.flyRule.FlyRuleMultiAddTemplateDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleAuditConfirmationDto;
import com.taikang.fly.check.mybatis.domain.FlyRule;
import com.taikang.fly.check.dto.flyRule.FlyRuleRespDto;
import java.util.List;
import com.taikang.fly.check.dto.flyRule.FlyRuleEditDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleWorkOrderEditDto;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleRespDTO;
import com.taikang.fly.check.dto.flyRule.TemplateFlyRuleEditDto;

public interface abstract FlyRuleMultiMapping	// class@000158 from classes.dex
{

    FlyRuleMulti addDtoToDomain(FlyRuleAddDto p0);
    FlyRuleMulti addTemplateDtoToDomain(FlyRuleMultiAddTemplateDto p0);
    FlyRule confirmationDtoToDomain(FlyRuleAuditConfirmationDto p0);
    FlyRuleRespDto domainToInfoDto(FlyRule p0);
    FlyRuleAddDto domainToaddDto(FlyRule p0);
    FlyRule dtoToDomain(FlyRuleRespDto p0);
    List dtoToDomains(List p0);
    FlyRule editDtoToDomain(FlyRuleEditDto p0,FlyRule p1);
    FlyRule editWorkOrderDtoToDomain(FlyRuleWorkOrderEditDto p0,FlyRule p1);
    List entityToDtos(List p0);
    FlyRuleMulti getFlyRuleMulti(FlyRuleRespDTO p0);
    FlyRule templateEditDtoToDomain(TemplateFlyRuleEditDto p0,FlyRule p1);
}
