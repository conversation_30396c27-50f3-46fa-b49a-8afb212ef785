package com.taikang.fly.check.mybatis.dao.ClickhouseFlyRuleMultiMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRuleMulti;
import java.util.List;

public interface abstract ClickhouseFlyRuleMultiMapper	// class@0001da from classes.dex
{

    void deleteAll();
    int deleteByPrimaryKey(String p0);
    int insert(ClickhouseFlyRuleMulti p0);
    List selectAll();
    ClickhouseFlyRuleMulti selectByPrimaryKey(String p0);
    int updateByPrimaryKey(ClickhouseFlyRuleMulti p0);
}
