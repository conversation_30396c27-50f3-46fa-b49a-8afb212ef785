package com.taikang.fly.check.utils.ListUtils$1;
import java.util.Comparator;
import java.lang.String;
import java.lang.Object;
import com.taikang.fly.check.utils.ListUtils;
import org.slf4j.Logger;
import java.lang.Exception;
import java.lang.Throwable;

final class ListUtils$1 implements Comparator	// class@000340 from classes.dex
{
    final boolean val$isAsc;
    final String[] val$sortnameArr;

    void ListUtils$1(String[] p0,boolean p1){
       this.val$sortnameArr = p0;
       this.val$isAsc = p1;
       super();
    }
    public int compare(Object a,Object b){
       int ret = 0;
       int i = 0;
       try{
          while (i < this.val$sortnameArr.length && !(ret = ListUtils.access$000(this.val$sortnameArr[i], this.val$isAsc, a, b))) {
             i++;
          }
       }catch(java.lang.Exception e0){
          ListUtils.access$100().error(e0.getMessage(), e0);
       }
       return ret;
    }
}
