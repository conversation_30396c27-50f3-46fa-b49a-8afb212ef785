package com.taikang.fly.check.mybatis.dao.FileInfoMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.FileInfo;

public interface abstract FileInfoMapper implements BaseMapper	// class@0001f0 from classes.dex
{

    int deleteById(String p0);
    int deleteByRuleIds(List p0);
    List getCategoryList();
    List getFileNameList();
    List getHospNameList();
    int selectCount(String p0);
    void updateByFileName(FileInfo p0);
}
