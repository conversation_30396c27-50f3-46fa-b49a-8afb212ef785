package com.taikang.fly.check.utils.DBUtils.DBTableProperties;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import java.sql.ResultSet;
import com.taikang.fly.check.utils.DBUtils.DbHelper;
import java.util.LinkedHashMap;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.text.MessageFormat;
import java.lang.CharSequence;
import com.taikang.fly.check.utils.DBUtils.ColumnAttribute;
import com.taikang.fly.check.utils.DBUtils.ColumnDetailsAttribute;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import org.apache.commons.lang3.StringUtils;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;

public class DBTableProperties	// class@000333 from classes.dex
{
    private static final String COL_META_SQL = "select COLUMN_NAME,DATA_TYPE from all_tab_columns where owner = \'%s\' and table_name = \'%s\'";
    private static final String METASQL_MYSQL = "SELECT\n\tCOLUMN_NAME,\n\tCOLUMN_TYPE,\n\tIS_NULLABLE,\n\tCOLUMN_COMMENT\nFROM\n\tINFORMATION_SCHEMA. COLUMNS \nWHERE TABLE_NAME =";
    private static final String METASQL_ORACLE = "select a.COLUMN_NAME,\n       a.DATA_TYPE || \'\'\(\'\' || a.DATA_LENGTH || \'\'\)\'\' AS COLUMN_TYPE,\n       a.NULLABLE,\n       b.COMMENTS as COLUMN_COMMENT\n  from all_tab_columns a\n  left join all_col_comments b\n    on a.TABLE_NAME = b.TABLE_NAME\n   AND a.column_name = b.column_name\n   and a.OWNER = b.OWNER\n where a.OWNER = \'\'{0}\'\'\n   and a.table_name =\'\'{1}\'\'";
    private static final String METASQL_ORACLEDETAILS = "select a.COLUMN_NAME,\n       a.DATA_TYPE || \'\'\(\'\' || a.DATA_LENGTH || \'\'\)\'\' AS COLUMN_TYPE,\n       a.NULLABLE,\n       a.DATA_SCALE,\n       b.COMMENTS as COLUMN_COMMENT\n  from all_tab_columns a\n  left join all_col_comments b\n    on a.TABLE_NAME = b.TABLE_NAME\n   AND a.column_name = b.column_name\n   and a.OWNER = b.OWNER\n where a.OWNER = \'\'{0}\'\'\n   and a.table_name =\'\'{1}\'\'";
    private static final String QUERYSYNONYMSSQL_ORACLE = "SELECT SYNONYM_NAME from SYS.ALL_SYNONYMS s where s.owner = \'\'{0}\'\'";
    private static final String QUERYUERSQL_ORACLE = "select username from all_users";
    private static final String QUERY_INDEX_COLUMNS_ORACLE = "SELECT ind.TABLE_NAME,col.COLUMN_NAME FROM user_indexes ind JOIN user_ind_COLUMNS col ON ind.INDEX_name = col.INDEX_name AND table_owner = \'%s\' WHERE ind.TABLE_NAME = \'%s\' and col.COLUMN_NAME = \'%s\'";
    private static final String QUERY_NORMAL_INDEXES_SQL_ORACLE = "select index_name,index_type from user_indexes where index_type =\'\'NORMAL\'\' and table_name = \'\'{0}\'\'";
    private static final String QUERY_SELF_SESSION = "select SID,SERIAL# as SERIAL from v$session where audsid = userenv\(\'sessionid\'\)";
    private static final String QUERY_USER_ALL_SESSION = "select SID,SERIAL# as SERIAL from v$session where username is not null and type = \'USER\'";
    private static final String TABLESSQL_BY_USERNAME_ORACLE = "SELECT TABLE_NAME from all_tables where owner = \'\'{0}\'\'";
    private static final String TABLESSQL_MYSQL = "SELECT\n\tTABLE_NAME,\n\tTABLE_COMMENT\nFROM\n\tinformation_schema. TABLES\nWHERE\n\ttable_schema =\'\'{0}\'\'";
    private static final String TABLESSQL_ORACLE = "SELECT TABLE_NAME from user_tables";
    private static final String TRUNTABLE_ORACLE = "truncate table ";

    private void DBTableProperties(){
       super();
    }
    public static Map fieldInfo(String oraUserName,String tableName){
       Object[] objArray = new Object[]{oraUserName,tableName};
       ResultSet resultSet = DbHelper.executeSQL(String.format("select COLUMN_NAME,DATA_TYPE from all_tab_columns where owner = \'%s\' and table_name = \'%s\'", objArray));
       HashMap fieldInfoMap = new LinkedHashMap();
       while (resultSet.next()) {
          fieldInfoMap.put(resultSet.getString("COLUMN_NAME"), resultSet.getString("DATA_TYPE"));
       }
       resultSet.close();
       DbHelper.close();
       return fieldInfoMap;
    }
    public static Map getAllUserSession(){
       return DBTableProperties.getUserSession("select SID,SERIAL# as SERIAL from v$session where username is not null and type = \'USER\'");
    }
    public static List getFieldFromOraTable(String owner,String tableName){
       List columnAttributes = new ArrayList();
       Object[] objArray = new Object[]{owner,tableName};
       String sql = MessageFormat.format("select a.COLUMN_NAME,\n       a.DATA_TYPE || \'\'\(\'\' || a.DATA_LENGTH || \'\'\)\'\' AS COLUMN_TYPE,\n       a.NULLABLE,\n       b.COMMENTS as COLUMN_COMMENT\n  from all_tab_columns a\n  left join all_col_comments b\n    on a.TABLE_NAME = b.TABLE_NAME\n   AND a.column_name = b.column_name\n   and a.OWNER = b.OWNER\n where a.OWNER = \'\'{0}\'\'\n   and a.table_name =\'\'{1}\'\'", objArray);
       ResultSet resultSet = DbHelper.executeSQL(sql);
       while (resultSet.next()) {
          ColumnAttribute uColumnAttri = new ColumnAttribute();
          uColumnAttri.setColumnName(resultSet.getString("COLUMN_NAME").replace("\n", ""));
          uColumnAttri.setColumnType(resultSet.getString("COLUMN_TYPE"));
          uColumnAttri.setIsNullable(resultSet.getString("NULLABLE"));
          uColumnAttri.setColumnComment(resultSet.getString("COLUMN_COMMENT"));
          columnAttributes.add(uColumnAttri);
       }
       resultSet.close();
       DbHelper.close();
       return columnAttributes;
    }
    public static List getFieldFromOraTableDetails(String owner,String tableName){
       List columnAttributes = new ArrayList();
       Object[] objArray = new Object[]{owner,tableName};
       String sql = MessageFormat.format("select a.COLUMN_NAME,\n       a.DATA_TYPE || \'\'\(\'\' || a.DATA_LENGTH || \'\'\)\'\' AS COLUMN_TYPE,\n       a.NULLABLE,\n       a.DATA_SCALE,\n       b.COMMENTS as COLUMN_COMMENT\n  from all_tab_columns a\n  left join all_col_comments b\n    on a.TABLE_NAME = b.TABLE_NAME\n   AND a.column_name = b.column_name\n   and a.OWNER = b.OWNER\n where a.OWNER = \'\'{0}\'\'\n   and a.table_name =\'\'{1}\'\'", objArray);
       ResultSet resultSet = DbHelper.executeSQL(sql);
       while (resultSet.next()) {
          String columnName = resultSet.getString("COLUMN_NAME");
          columnName = columnName.replace("\n", "");
          String columnType = resultSet.getString("COLUMN_TYPE");
          String isNullable = resultSet.getString("NULLABLE");
          String columnComment = resultSet.getString("COLUMN_COMMENT");
          String datascale = resultSet.getString("DATA_SCALE");
          ColumnDetailsAttribute columnAttribute = new ColumnDetailsAttribute();
          columnAttribute.setColumnName(columnName);
          columnAttribute.setColumnType(columnType);
          columnAttribute.setIsNullable(isNullable);
          columnAttribute.setColumnComment(columnComment);
          columnAttribute.setDatascale(datascale);
          columnAttributes.add(columnAttribute);
       }
       resultSet.close();
       return columnAttributes;
    }
    public static List getFieldFromTable(String tableName){
       List columnAttributes = new ArrayList();
       Object[] objArray = new Object[]{tableName};
       String sql = MessageFormat.format("SELECT\n\tCOLUMN_NAME,\n\tCOLUMN_TYPE,\n\tIS_NULLABLE,\n\tCOLUMN_COMMENT\nFROM\n\tINFORMATION_SCHEMA. COLUMNS \nWHERE TABLE_NAME =", objArray);
       ResultSet resultSet = DbHelper.executeSQL(sql);
       while (resultSet.next()) {
          String columnName = resultSet.getString("COLUMN_NAME");
          String columnType = resultSet.getString("COLUMN_TYPE");
          String isNullable = resultSet.getString("IS_NULLABLE");
          String columnComment = resultSet.getString("COLUMN_COMMENT");
          ColumnAttribute columnAttribute = new ColumnAttribute();
          columnAttribute.setColumnName(columnName);
          columnAttribute.setColumnType(columnType);
          columnAttribute.setIsNullable(isNullable);
          columnAttribute.setColumnComment(columnComment);
          columnAttributes.add(columnAttribute);
       }
       resultSet.close();
       DbHelper.close();
       return columnAttributes;
    }
    public static Map getIndexTableNameAndColumnName(String owner,Map tableNameMap){
       Map result = new HashMap();
       Iterator iterator = tableNameMap.entrySet().iterator();
       while (iterator.hasNext()) {
          Map$Entry uEntry = iterator.next();
          Object[] objArray = new Object[]{owner,uEntry.getKey(),uEntry.getValue()};
          ResultSet resultSet = DbHelper.executeSQL(String.format("SELECT ind.TABLE_NAME,col.COLUMN_NAME FROM user_indexes ind JOIN user_ind_COLUMNS col ON ind.INDEX_name = col.INDEX_name AND table_owner = \'%s\' WHERE ind.TABLE_NAME = \'%s\' and col.COLUMN_NAME = \'%s\'", objArray));
          while (resultSet.next()) {
             String str = resultSet.getString("TABLE_NAME");
             String str1 = resultSet.getString("COLUMN_NAME");
             if (StringUtils.isNotBlank(str) && StringUtils.isNotBlank(str1)) {
                result.put(str, str1);
             }
          }
          resultSet.close();
          DbHelper.close();
       }
       return result;
    }
    public static List getNormalIndexesFromOraDB(String tableName){
       List indexNameList = new ArrayList();
       Object[] objArray = new Object[]{tableName};
       String sql = MessageFormat.format("select index_name,index_type from user_indexes where index_type =\'\'NORMAL\'\' and table_name = \'\'{0}\'\'", objArray);
       ResultSet resultSet = DbHelper.executeSQL(sql);
       while (resultSet.next()) {
          String indexName = resultSet.getString("INDEX_NAME");
          indexNameList.add(indexName);
       }
       resultSet.close();
       DbHelper.close();
       return indexNameList;
    }
    public static Map getOtherUserSession(){
       Map allUserSession = DBTableProperties.getAllUserSession();
       Map selfSession = DBTableProperties.getSelfSession();
       MapDifference difference = Maps.difference(allUserSession, selfSession);
       return difference.entriesOnlyOnLeft();
    }
    public static Map getSelfSession(){
       return DBTableProperties.getUserSession("select SID,SERIAL# as SERIAL from v$session where audsid = userenv\(\'sessionid\'\)");
    }
    public static List getSynonymsFromOra(String userName){
       List synonymList = new ArrayList();
       Object[] objArray = new Object[]{userName};
       String sql = MessageFormat.format("SELECT SYNONYM_NAME from SYS.ALL_SYNONYMS s where s.owner = \'\'{0}\'\'", objArray);
       ResultSet resultSet = DbHelper.executeSQL(sql);
       while (resultSet.next()) {
          String synonymName = resultSet.getString("SYNONYM_NAME");
          synonymList.add(synonymName);
       }
       resultSet.close();
       DbHelper.close();
       return synonymList;
    }
    public static List getTablesFromDB(String dbName){
       List tables = new ArrayList();
       Object[] objArray = new Object[]{dbName};
       String sql = MessageFormat.format("SELECT TABLE_NAME from user_tables", objArray);
       ResultSet resultSet = DbHelper.executeSQL(sql);
       while (resultSet.next()) {
          String tableName = resultSet.getString("TABLE_NAME");
          tables.add(tableName);
       }
       resultSet.close();
       DbHelper.close();
       return tables;
    }
    public static List getTablesFromOraDB(){
       List tables = new ArrayList();
       ResultSet resultSet = DbHelper.executeSQL("SELECT TABLE_NAME from user_tables");
       while (resultSet.next()) {
          tables.add(resultSet.getString("TABLE_NAME"));
       }
       resultSet.close();
       DbHelper.close();
       return tables;
    }
    public static List getTablesFromOraDB(String oraUserName){
       List tables = new ArrayList();
       Object[] objArray = new Object[]{oraUserName.toUpperCase()};
       String querySql = MessageFormat.format("SELECT TABLE_NAME from all_tables where owner = \'\'{0}\'\'", objArray);
       ResultSet resultSet = DbHelper.executeSQL(querySql);
       while (resultSet.next()) {
          String tableName = resultSet.getString("TABLE_NAME");
          tables.add(tableName);
       }
       resultSet.close();
       DbHelper.close();
       return tables;
    }
    public static Map getUserSession(String querySessionSql){
       Map result = new LinkedHashMap();
       ResultSet resultSet = DbHelper.executeSQL(querySessionSql);
       while (resultSet.next()) {
          result.put(resultSet.getString("SID"), resultSet.getString("SERIAL"));
       }
       resultSet.close();
       DbHelper.close();
       return result;
    }
    public static List getUsersFromOraDB(){
       List userList = new ArrayList();
       ResultSet resultSet = DbHelper.executeSQL("select username from all_users");
       while (resultSet.next()) {
          userList.add(resultSet.getString("username"));
       }
       resultSet.close();
       DbHelper.close();
       return userList;
    }
    public static List indexExistNames(String tableName){
       return DBTableProperties.getNormalIndexesFromOraDB(tableName);
    }
    public static void setParamaterForJDBC(String url,String userName,String password){
       DbHelper.init(url, userName, password);
    }
    public static boolean tableIsExist(String tableName){
       List tablesFromOraDB = DBTableProperties.getTablesFromOraDB();
       return tablesFromOraDB.contains(tableName);
    }
    public static void trunTable(String tableName){
       Object[] objArray = new Object[]{tableName};
       String sql = MessageFormat.format("truncate table ", objArray);
       DbHelper.executeSQL(sql);
       DbHelper.close();
    }
    public static boolean userIsExist(String userName){
       List userList = DBTableProperties.getUsersFromOraDB();
       return userList.contains(userName.toUpperCase());
    }
}
