package com.taikang.fly.check.dto.dictEntry.DictEntryDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictEntryDto implements Serializable	// class@0000e6 from classes.dex
{
    private String createTime;
    private String creator;
    private String dictCode;
    private String dictName;
    private String dictTypeCode;
    private String id;
    private String modby;
    private String modifyTime;
    private String originalTime;
    private String signature;
    private String sortNo;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a1;

    public void DictEntryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictEntryDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DictEntryDto){
          b = false;
       }else {
          DictEntryDto uDictEntryDt = o;
          if (!uDictEntryDt.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDictEntryDt.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String dictTypeCode = this.getDictTypeCode();
             String dictTypeCode1 = uDictEntryDt.getDictTypeCode();
             if (dictTypeCode == null) {
                if (dictTypeCode1 != null) {
                   b = false;
                }
             }else if(dictTypeCode.equals(dictTypeCode1)){
             }
             String dictCode = this.getDictCode();
             String dictCode1 = uDictEntryDt.getDictCode();
             if (dictCode == null) {
                if (dictCode1 != null) {
                   b = false;
                }
             }else if(dictCode.equals(dictCode1)){
             }
             String dictName = this.getDictName();
             String dictName1 = uDictEntryDt.getDictName();
             if (dictName == null) {
                if (dictName1 != null) {
                   b = false;
                }
             }else if(dictName.equals(dictName1)){
             }
             String sortNo = this.getSortNo();
             String sortNo1 = uDictEntryDt.getSortNo();
             if (sortNo == null) {
                if (sortNo1 != null) {
                   b = false;
                }
             }else if(sortNo.equals(sortNo1)){
             }
             String creator = this.getCreator();
             String creator1 = uDictEntryDt.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = uDictEntryDt.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = uDictEntryDt.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_00cf :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = uDictEntryDt.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = uDictEntryDt.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00ff :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String originalTime = this.getOriginalTime();
             String originalTime1 = uDictEntryDt.getOriginalTime();
             if (originalTime == null) {
                if (originalTime1 != null) {
                   b = false;
                }
             }else if(originalTime.equals(originalTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDictCode(){
       return this.dictCode;
    }
    public String getDictName(){
       return this.dictName;
    }
    public String getDictTypeCode(){
       return this.dictTypeCode;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getOriginalTime(){
       return this.originalTime;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getSortNo(){
       return this.sortNo;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $dictTypeCode = this.getDictTypeCode();
       int i1 = result * 59;
       i = ($dictTypeCode == null)? 43: $dictTypeCode.hashCode();
       result = i1 + i;
       String $dictCode = this.getDictCode();
       i1 = result * 59;
       i = ($dictCode == null)? 43: $dictCode.hashCode();
       result = i1 + i;
       String $dictName = this.getDictName();
       i1 = result * 59;
       i = ($dictName == null)? 43: $dictName.hashCode();
       result = i1 + i;
       String $sortNo = this.getSortNo();
       i1 = result * 59;
       i = ($sortNo == null)? 43: $sortNo.hashCode();
       result = i1 + i;
       String creator = this.getCreator();
       i1 = result * 59;
       i = (creator == null)? 43: creator.hashCode();
       String createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i1 + i) * 59;
       i = (signature == null)? 43: signature.hashCode();
       String originalTime = this.getOriginalTime();
       i1 = (i1 + i) * 59;
       i = (originalTime == null)? 43: originalTime.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDictCode(String dictCode){
       this.dictCode = dictCode;
    }
    public void setDictName(String dictName){
       this.dictName = dictName;
    }
    public void setDictTypeCode(String dictTypeCode){
       this.dictTypeCode = dictTypeCode;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setOriginalTime(String originalTime){
       this.originalTime = originalTime;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setSortNo(String sortNo){
       this.sortNo = sortNo;
    }
    public String toString(){
       return "DictEntryDto\(id="+this.getId()+", dictTypeCode="+this.getDictTypeCode()+", dictCode="+this.getDictCode()+", dictName="+this.getDictName()+", sortNo="+this.getSortNo()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", originalTime="+this.getOriginalTime()+"\)";
    }
}
