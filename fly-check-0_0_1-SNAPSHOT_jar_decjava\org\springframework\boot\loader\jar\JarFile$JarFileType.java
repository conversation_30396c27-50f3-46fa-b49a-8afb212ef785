package org.springframework.boot.loader.jar.JarFile$JarFileType;
import java.lang.Enum;
import java.lang.String;
import java.lang.Class;
import java.lang.Object;

final class JarFile$JarFileType extends Enum	// class@000555 from classes.dex
{
    private static final JarFile$JarFileType[] $VALUES;
    public static final JarFile$JarFileType DIRECT;
    public static final JarFile$JarFileType NESTED_DIRECTORY;
    public static final JarFile$JarFileType NESTED_JAR;

    static {
       JarFile$JarFileType.DIRECT = new JarFile$JarFileType("DIRECT", 0);
       JarFile$JarFileType.NESTED_DIRECTORY = new JarFile$JarFileType("NESTED_DIRECTORY", 1);
       JarFile$JarFileType.NESTED_JAR = new JarFile$JarFileType("NESTED_JAR", 2);
       JarFile$JarFileType[] jarFileTypeA = new JarFile$JarFileType[]{JarFile$JarFileType.DIRECT,JarFile$JarFileType.NESTED_DIRECTORY,JarFile$JarFileType.NESTED_JAR};
       JarFile$JarFileType.$VALUES = jarFileTypeA;
    }
    private void JarFile$JarFileType(String p0,int p1){
       super(p0, p1);
    }
    public static JarFile$JarFileType valueOf(String name){
       return Enum.valueOf(JarFile$JarFileType.class, name);
    }
    public static JarFile$JarFileType[] values(){
       return JarFile$JarFileType.$VALUES.clone();
    }
}
