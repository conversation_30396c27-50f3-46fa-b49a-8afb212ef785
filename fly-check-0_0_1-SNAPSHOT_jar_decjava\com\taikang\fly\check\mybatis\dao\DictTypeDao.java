package com.taikang.fly.check.mybatis.dao.DictTypeDao;
import java.lang.String;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.DictType;

public interface abstract DictTypeDao	// class@0001e4 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    List findAll(Map p0);
    List findDictTypes();
    List getByDictType(String p0);
    List getMatchTables();
    int insert(DictType p0);
    int insertSelective(DictType p0);
    String isDictTypeExist(String p0);
    DictType selectByPrimaryKey(String p0);
    DictType selectBySelective(Map p0);
    int updateByPrimaryKey(DictType p0);
    int updateByPrimaryKeySelective(DictType p0);
}
