package com.taikang.fly.check.dto.system.role.RoleAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleAddDto implements Serializable	// class@0001b3 from classes.dex
{
    private String name;
    private String roleCode;
    private String signature;
    private static final long serialVersionUID = 0xc0c2c8142418e8c8;

    public void RoleAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleAddDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RoleAddDto) {
             b = false;
          }else {
             RoleAddDto roleAddDto = o;
             if (!roleAddDto.canEqual(this)) {
                b = false;
             }else {
                String roleCode = this.getRoleCode();
                String roleCode1 = roleAddDto.getRoleCode();
                if (roleCode == null) {
                   if (roleCode1 != null) {
                      b = false;
                   }
                }else if(roleCode.equals(roleCode1)){
                }
                String name = this.getName();
                String name1 = roleAddDto.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String signature = this.getSignature();
                String signature1 = roleAddDto.getSignature();
                if (signature == null) {
                   if (signature1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!signature.equals(signature1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public int hashCode(){
       String $roleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($roleCode = this.getRoleCode()) == null)? i: $roleCode.hashCode();
       result = i1 + 59;
       String $name = this.getName();
       int i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $signature = this.getSignature();
       i1 = result * 59;
       if ($signature != null) {
          i = $signature.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public String toString(){
       return "RoleAddDto\(roleCode="+this.getRoleCode()+", name="+this.getName()+", signature="+this.getSignature()+"\)";
    }
}
