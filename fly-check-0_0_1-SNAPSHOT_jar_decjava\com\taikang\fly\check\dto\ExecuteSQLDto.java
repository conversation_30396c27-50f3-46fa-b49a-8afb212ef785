package com.taikang.fly.check.dto.ExecuteSQLDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ExecuteSQLDto implements Serializable	// class@00009e from classes.dex
{
    private boolean lockedStatus;
    private String machine;
    private String owner;
    private String serial;
    private String sid;
    private String spid;
    private String sqlText;
    private String username;
    private static final long serialVersionUID = 0x1;

    public void ExecuteSQLDto(){
       super();
       this.lockedStatus = false;
    }
    protected boolean canEqual(Object other){
       return other instanceof ExecuteSQLDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ExecuteSQLDto){
          b = false;
       }else {
          ExecuteSQLDto uExecuteSQLD = o;
          if (!uExecuteSQLD.canEqual(this)) {
             b = false;
          }else {
             String sqlText = this.getSqlText();
             String sqlText1 = uExecuteSQLD.getSqlText();
             if (sqlText == null) {
                if (sqlText1 != null) {
                   b = false;
                }
             }else if(sqlText.equals(sqlText1)){
             }
             String owner = this.getOwner();
             String owner1 = uExecuteSQLD.getOwner();
             if (owner == null) {
                if (owner1 != null) {
                   b = false;
                }
             }else if(owner.equals(owner1)){
             }
             String username = this.getUsername();
             String username1 = uExecuteSQLD.getUsername();
             if (username == null) {
                if (username1 != null) {
                   b = false;
                }
             }else if(username.equals(username1)){
             }
             String sid = this.getSid();
             String sid1 = uExecuteSQLD.getSid();
             if (sid == null) {
                if (sid1 != null) {
                   b = false;
                }
             }else if(sid.equals(sid1)){
             }
             String serial = this.getSerial();
             String serial1 = uExecuteSQLD.getSerial();
             if (serial == null) {
                if (serial1 != null) {
                   b = false;
                }
             }else if(serial.equals(serial1)){
             }
             String spid = this.getSpid();
             String spid1 = uExecuteSQLD.getSpid();
             if (spid == null) {
                if (spid1 != null) {
                label_009a :
                   b = false;
                }
             }else if(spid.equals(spid1)){
             }
             String machine = this.getMachine();
             String machine1 = uExecuteSQLD.getMachine();
             if (machine == null) {
                if (machine1 != null) {
                   b = false;
                }
             }else if(machine.equals(machine1)){
             }
             if (this.isLockedStatus() != uExecuteSQLD.isLockedStatus()) {
                b = false;
             }else {
                b = true;
             }
          }
       }
       return b;
    }
    public String getMachine(){
       return this.machine;
    }
    public String getOwner(){
       return this.owner;
    }
    public String getSerial(){
       return this.serial;
    }
    public String getSid(){
       return this.sid;
    }
    public String getSpid(){
       return this.spid;
    }
    public String getSqlText(){
       return this.sqlText;
    }
    public String getUsername(){
       return this.username;
    }
    public int hashCode(){
       String $sqlText;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($sqlText = this.getSqlText()) == null)? i: $sqlText.hashCode();
       result = i1 + 59;
       String $owner = this.getOwner();
       int i2 = result * 59;
       i1 = ($owner == null)? i: $owner.hashCode();
       result = i2 + i1;
       String $username = this.getUsername();
       i2 = result * 59;
       i1 = ($username == null)? i: $username.hashCode();
       result = i2 + i1;
       String $sid = this.getSid();
       i2 = result * 59;
       i1 = ($sid == null)? i: $sid.hashCode();
       result = i2 + i1;
       String $serial = this.getSerial();
       i2 = result * 59;
       i1 = ($serial == null)? i: $serial.hashCode();
       result = i2 + i1;
       String spid = this.getSpid();
       i2 = result * 59;
       i1 = (spid == null)? i: spid.hashCode();
       String machine = this.getMachine();
       i1 = (i2 + i1) * 59;
       if (machine != null) {
          i = machine.hashCode();
       }
       i = (i1 + i) * 59;
       i1 = (this.isLockedStatus())? 79: 97;
       return (i + i1);
    }
    public boolean isLockedStatus(){
       return this.lockedStatus;
    }
    public void setLockedStatus(boolean lockedStatus){
       this.lockedStatus = lockedStatus;
    }
    public void setMachine(String machine){
       this.machine = machine;
    }
    public void setOwner(String owner){
       this.owner = owner;
    }
    public void setSerial(String serial){
       this.serial = serial;
    }
    public void setSid(String sid){
       this.sid = sid;
    }
    public void setSpid(String spid){
       this.spid = spid;
    }
    public void setSqlText(String sqlText){
       this.sqlText = sqlText;
    }
    public void setUsername(String username){
       this.username = username;
    }
    public String toString(){
       return "ExecuteSQLDto\(sqlText="+this.getSqlText()+", owner="+this.getOwner()+", username="+this.getUsername()+", sid="+this.getSid()+", serial="+this.getSerial()+", spid="+this.getSpid()+", machine="+this.getMachine()+", lockedStatus="+this.isLockedStatus()+"\)";
    }
}
