package com.taikang.fly.check.service.PlanService;
import com.taikang.fly.check.dto.plan.PlanAddDto;
import java.lang.Integer;
import com.taikang.fly.check.dto.plan.PlanRuleSearchDto;
import com.taikang.fly.check.comm.NativePage;
import java.lang.String;
import com.taikang.fly.check.dto.plan.PlanSearchDto;

public interface abstract PlanService	// class@0002f6 from classes.dex
{

    Integer add(PlanAddDto p0);
    NativePage configRules(PlanRuleSearchDto p0,Integer p1,Integer p2);
    Integer deleteById(String p0);
    NativePage queryPlanListPage(PlanSearchDto p0,Integer p1,Integer p2);
}
