package com.taikang.fly.check.dto.mapstruct.FlyRuleFeedBackMapping;
import com.taikang.fly.check.dto.flyRule.FlyRuleFeedBackRespDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleExchangerDto;
import java.util.List;

public interface abstract FlyRuleFeedBackMapping	// class@000152 from classes.dex
{

    FlyRuleExchangerDto flyRuleFeedBackResToExchanger(FlyRuleFeedBackRespDto p0);
    List flyRuleResListToexchangerList(List p0);
}
