package com.taikang.fly.check.mybatis.dao.DrgPreGroupMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.taikang.fly.check.vo.drg.CwMrGroupVo;
import java.util.List;
import java.lang.String;
import java.lang.Integer;

public interface abstract DrgPreGroupMapper implements BaseMapper	// class@0001e8 from classes.dex
{

    int add(CwMrGroupVo p0);
    List selectList();
    List selectWeight(String p0,String p1);
    int trun();
    Integer updateWeight(String p0,String p1);
}
