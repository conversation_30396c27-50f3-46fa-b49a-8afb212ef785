package com.taikang.fly.check.service.MfromtOfficeConfigService;
import java.lang.Object;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigQueryDto;
import java.lang.Integer;
import com.taikang.fly.check.comm.Page;
import java.util.Map;
import com.taikang.fly.check.utils.MapUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.MfromtOfficeConfigMapper;
import com.taikang.fly.check.dto.mapstruct.MfromtOfficeConfigMapping;

public class MfromtOfficeConfigService	// class@0002ef from classes.dex
{
    private MfromtOfficeConfigMapper mfromtOfficeConfigMapper;
    private MfromtOfficeConfigMapping mfromtOfficeConfigMapping;

    public void MfromtOfficeConfigService(){
       super();
    }
    public Page queryListPage(ColConfigQueryDto colConfigQueryDto,Integer pageNum,Integer pageSize){
       Map params = MapUtils.transBeanToMap(colConfigQueryDto);
       Page records = PageHelper.startPage(pageNum.intValue(), pageSize.intValue());
       List mfromtColumnConfigs = this.mfromtOfficeConfigMapper.queryList(params);
       List colConfigRespDtos = this.mfromtOfficeConfigMapping.toColConfigRespDtoList(mfromtColumnConfigs);
       Page pageDto = new Page(colConfigRespDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()));
       records.close();
       return pageDto;
    }
}
