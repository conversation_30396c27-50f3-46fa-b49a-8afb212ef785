package com.taikang.fly.check.rest.TransferTableController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.comm.CommResponse;
import java.util.ArrayList;
import java.util.List;
import com.taikang.fly.check.service.TableDataImportService;
import java.util.Collection;
import org.springframework.util.CollectionUtils;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.String;
import com.taikang.fly.check.service.SplitTableService;

public class TransferTableController	// class@0002b4 from classes.dex
{
    private SplitTableService splitTableService;
    private TableDataImportService tableDataImportService;
    private static final Logger log;

    static {
       TransferTableController.log = LoggerFactory.getLogger(TransferTableController.class);
    }
    public void TransferTableController(){
       super();
    }
    public CommResponse tableMathch(){
       List lists = new ArrayList();
       List tablesFromDB = this.tableDataImportService.getTablesFromDB();
       if (CollectionUtils.isEmpty(tablesFromDB)) {
          throw new BizException(ResponseCodeEnum.PLEASE_IMPORT_DATASOURCE);
       }
       return CommResponse.success(tablesFromDB);
    }
    public CommResponse transferTable(List list){
       return CommResponse.success(this.splitTableService.transferTable(list));
    }
}
