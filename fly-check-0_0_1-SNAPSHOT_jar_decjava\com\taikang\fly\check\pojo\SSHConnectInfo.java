package com.taikang.fly.check.pojo.SSHConnectInfo;
import java.lang.Object;
import com.jcraft.jsch.Channel;
import org.springframework.web.socket.WebSocketSession;
import com.jcraft.jsch.JSch;

public class SSHConnectInfo	// class@00027e from classes.dex
{
    private Channel channel;
    private JSch jSch;
    private WebSocketSession webSocketSession;

    public void SSHConnectInfo(){
       super();
    }
    public Channel getChannel(){
       return this.channel;
    }
    public WebSocketSession getWebSocketSession(){
       return this.webSocketSession;
    }
    public JSch getjSch(){
       return this.jSch;
    }
    public void setChannel(Channel channel){
       this.channel = channel;
    }
    public void setWebSocketSession(WebSocketSession webSocketSession){
       this.webSocketSession = webSocketSession;
    }
    public void setjSch(JSch jSch){
       this.jSch = jSch;
    }
}
