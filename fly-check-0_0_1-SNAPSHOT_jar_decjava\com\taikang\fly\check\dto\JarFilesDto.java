package com.taikang.fly.check.dto.JarFilesDto;
import java.lang.Object;
import org.springframework.web.multipart.MultipartFile;
import java.lang.String;
import java.lang.StringBuilder;

public class JarFilesDto	// class@0000a4 from classes.dex
{
    MultipartFile dmpfile;
    MultipartFile jarfile;
    MultipartFile sqlfile;

    public void JarFilesDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof JarFilesDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof JarFilesDto) {
             b = false;
          }else {
             JarFilesDto jarFilesDto = o;
             if (!jarFilesDto.canEqual(this)) {
                b = false;
             }else {
                MultipartFile jarfile = this.getJarfile();
                MultipartFile jarfile1 = jarFilesDto.getJarfile();
                if (jarfile == null) {
                   if (jarfile1 != null) {
                      b = false;
                   }
                }else if(jarfile.equals(jarfile1)){
                }
                MultipartFile dmpfile = this.getDmpfile();
                MultipartFile dmpfile1 = jarFilesDto.getDmpfile();
                if (dmpfile == null) {
                   if (dmpfile1 != null) {
                      b = false;
                   }
                }else if(dmpfile.equals(dmpfile1)){
                }
                MultipartFile sqlfile = this.getSqlfile();
                MultipartFile sqlfile1 = jarFilesDto.getSqlfile();
                if (sqlfile == null) {
                   if (sqlfile1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!sqlfile.equals(sqlfile1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public MultipartFile getDmpfile(){
       return this.dmpfile;
    }
    public MultipartFile getJarfile(){
       return this.jarfile;
    }
    public MultipartFile getSqlfile(){
       return this.sqlfile;
    }
    public int hashCode(){
       MultipartFile $jarfile;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($jarfile = this.getJarfile()) == null)? i: $jarfile.hashCode();
       result = i1 + 59;
       MultipartFile $dmpfile = this.getDmpfile();
       int i2 = result * 59;
       i1 = ($dmpfile == null)? i: $dmpfile.hashCode();
       result = i2 + i1;
       MultipartFile $sqlfile = this.getSqlfile();
       i1 = result * 59;
       if ($sqlfile != null) {
          i = $sqlfile.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDmpfile(MultipartFile dmpfile){
       this.dmpfile = dmpfile;
    }
    public void setJarfile(MultipartFile jarfile){
       this.jarfile = jarfile;
    }
    public void setSqlfile(MultipartFile sqlfile){
       this.sqlfile = sqlfile;
    }
    public String toString(){
       return "JarFilesDto\(jarfile="+this.getJarfile()+", dmpfile="+this.getDmpfile()+", sqlfile="+this.getSqlfile()+"\)";
    }
}
