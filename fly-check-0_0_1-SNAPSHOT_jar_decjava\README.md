# FlyCheck Python - 医保飞行检查系统

基于Python重写的医保飞行检查系统，保持原有Java版本的核心功能，采用现代化的Python技术栈。

## 技术栈

- **Web框架**: FastAPI
- **数据库**: DuckDB
- **ORM**: SQLAlchemy
- **包管理**: uv
- **任务队列**: Celery + Redis
- **数据处理**: Pandas
- **API文档**: Swagger/OpenAPI

## 核心功能

### 1. 规则引擎
- 医保检查规则的创建、编辑、审核
- 支持SQL规则和Python规则
- 规则版本管理和历史追踪

### 2. 检查计划
- 检查计划的制定和执行
- 批量规则执行
- 执行结果统计和分析

### 3. 医保基础数据
- 药品目录管理
- 诊疗项目管理
- 医用耗材管理
- 诊断编码和手术编码

### 4. DRG分析
- DRG分组分析
- 费用差异分析
- 盈亏分析

### 5. 数据管理
- 多格式数据导入导出
- 数据清洗规则
- 大文件处理

## 快速开始

### 环境要求
- Python 3.11+
- uv包管理器

### 方式一：一键启动（推荐）

```bash
# 下载并运行启动脚本
python run.py
```

启动脚本会自动：
- 检查并安装uv包管理器
- 创建虚拟环境
- 安装所有依赖
- 初始化数据库
- 创建示例数据
- 启动开发服务器

### 方式二：手动安装

```bash
# 1. 安装uv (如果还没有安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 创建虚拟环境并安装依赖
uv venv
source .venv/bin/activate  # Linux/Mac
# 或 .venv\Scripts\activate  # Windows

uv pip install -e .

# 3. 初始化数据库
uv run python -m flycheck.cli db init

# 4. 创建示例数据
uv run python -m flycheck.cli db seed

# 5. 启动服务器
uv run uvicorn flycheck.main:app --reload --host 0.0.0.0 --port 8000
```

### 方式三：Docker部署

```bash
# 使用Docker Compose
docker-compose up -d

# 或单独构建
docker build -t flycheck-python .
docker run -p 8000:8000 flycheck-python
```

## 项目结构

```
flycheck/
├── flycheck/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── cli.py               # 命令行工具
│   ├── config.py            # 配置管理
│   ├── database.py          # 数据库连接
│   ├── models/              # 数据模型
│   ├── schemas/             # Pydantic模型
│   ├── api/                 # API路由
│   ├── services/            # 业务逻辑
│   ├── core/                # 核心功能
│   │   ├── rule_engine.py   # 规则引擎
│   │   ├── plan_executor.py # 计划执行器
│   │   └── data_cleaner.py  # 数据清洗
│   ├── utils/               # 工具函数
│   └── tests/               # 测试文件
├── migrations/              # 数据库迁移
├── static/                  # 静态文件
├── templates/               # 模板文件
└── docs/                    # 文档
```

## API文档

启动服务后访问:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 开发

```bash
# 安装开发依赖
uv pip install -e ".[dev]"

# 运行测试
pytest

# 代码格式化
black flycheck/
isort flycheck/

# 类型检查
mypy flycheck/
```

## 部署

```bash
# 生产环境运行
uvicorn flycheck.main:app --host 0.0.0.0 --port 8000 --workers 4

# 或使用Docker
docker build -t flycheck-python .
docker run -p 8000:8000 flycheck-python
```

## 核心功能详解

### 1. 规则引擎
规则引擎是系统的核心，支持三种类型的规则：

#### SQL规则
```sql
-- 示例：检查药品费用异常
SELECT patient_id, drug_code, drug_name, total_fee
FROM medical_records
WHERE total_fee > 1000
AND drug_type = '西药'
```

#### Python规则
```python
# 示例：复杂的数据分析规则
import pandas as pd

# 获取数据
df = pd.read_sql('''
    SELECT patient_id, diagnosis_code, drug_code
    FROM medical_records
''', duckdb_conn)

# 业务逻辑处理
result = df.groupby('patient_id').agg({
    'drug_code': 'count'
}).reset_index()

# 返回异常数据
result = result[result['drug_code'] > 10]
```

#### 混合规则
结合SQL查询和Python处理的复杂规则。

### 2. 检查计划
- 支持定期执行和一次性执行
- 可配置规则执行顺序
- 并发执行多个规则
- 实时监控执行状态

### 3. 医保基础数据管理
- 药品目录（YB_DRUG_CATALOGUE）
- 诊疗项目（YB_DIAGNOSIS_TREATMENT）
- 医用耗材（YB_CONSUMABLES_LIST）
- 诊断编码（YB_DIAGNOSTIC_ENCODING）
- 手术编码（YB_OPERATIVE_ENCODING）

### 4. 数据导入导出
- 支持CSV、Excel格式
- 批量数据处理
- 数据验证和清洗
- 错误数据报告

## API使用示例

### 创建规则
```bash
curl -X POST "http://localhost:8000/api/v1/rules/" \
  -H "Content-Type: application/json" \
  -d '{
    "rule_name": "药品费用检查",
    "rule_describe": "检查药品费用是否合理",
    "rule_sql": "SELECT * FROM medical_records WHERE drug_fee > 1000",
    "rule_type": "sql",
    "rule_category1": "费用检查"
  }'
```

### 执行规则
```bash
curl -X POST "http://localhost:8000/api/v1/rules/{rule_id}/execute" \
  -H "Content-Type: application/json" \
  -d '{"start_date": "2024-01-01", "end_date": "2024-01-31"}'
```

### 创建检查计划
```bash
curl -X POST "http://localhost:8000/api/v1/plans/" \
  -H "Content-Type: application/json" \
  -d '{
    "plan_name": "月度检查计划",
    "plan_description": "每月定期检查",
    "rule_ids": ["rule_id_1", "rule_id_2"]
  }'
```

## 命令行工具

```bash
# 查看系统信息
uv run python -m flycheck.cli info

# 列出所有规则
uv run python -m flycheck.cli rule list

# 验证规则
uv run python -m flycheck.cli rule validate <rule_id>

# 重置数据库
uv run python -m flycheck.cli db reset
```

## 配置说明

系统支持通过环境变量或`.env`文件进行配置：

```bash
# 复制配置文件模板
cp .env.example .env

# 编辑配置
vim .env
```

主要配置项：
- `FLYCHECK_DATABASE_URL`: 数据库连接URL
- `FLYCHECK_REDIS_URL`: Redis连接URL
- `FLYCHECK_SECRET_KEY`: JWT密钥
- `FLYCHECK_RULE_TIMEOUT`: 规则执行超时时间
- `FLYCHECK_MAX_CONCURRENT_RULES`: 最大并发规则数

## 性能优化

### DuckDB优化
- 使用列式存储提高查询性能
- 支持并行查询处理
- 内存映射文件减少I/O开销

### 规则执行优化
- 并发执行多个规则
- 批量数据处理
- 结果缓存机制

### 大数据处理
- 分批处理大文件
- 流式数据处理
- 内存使用优化

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库文件权限
   ls -la flycheck.db

   # 重新初始化数据库
   uv run python -m flycheck.cli db reset
   ```

2. **规则执行超时**
   ```bash
   # 增加超时时间
   export FLYCHECK_RULE_TIMEOUT=600
   ```

3. **内存不足**
   ```bash
   # 减少批处理大小
   export FLYCHECK_BATCH_SIZE=500
   ```

### 日志查看
```bash
# 查看应用日志
tail -f logs/flycheck.log

# 查看错误日志
grep ERROR logs/flycheck.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

MIT License
