package com.taikang.fly.check.rest.ModuleController;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.service.ModuleService;
import com.taikang.fly.check.dto.module.ModuleRespEditDto;
import java.util.List;
import com.taikang.fly.check.dto.module.ModuleQueryDto;
import java.lang.Integer;
import com.taikang.fly.check.comm.Page;
import com.taikang.fly.check.dto.module.ModuleRespAddDto;

public class ModuleController	// class@0002a1 from classes.dex
{
    private ModuleService moduleService;

    public void ModuleController(){
       super();
    }
    public RmpResponse deleteModule(String moduleCode){
       this.moduleService.deleteModule(moduleCode);
       return RmpResponse.success();
    }
    public RmpResponse editModule(ModuleRespEditDto moduleRespEditDto){
       this.moduleService.updateModule(moduleRespEditDto);
       return RmpResponse.success();
    }
    public RmpResponse queryModuleList(){
       List moduleList = this.moduleService.queryModuleList();
       return RmpResponse.success(moduleList);
    }
    public RmpResponse queryUserListPage(ModuleQueryDto moduleQueryDto,Integer pageNum,Integer pageSize){
       Page userDtoPage = this.moduleService.queryModuleListPage(moduleQueryDto, pageNum, pageSize);
       return RmpResponse.success(userDtoPage);
    }
    public RmpResponse saveModule(ModuleRespAddDto moduleRespAddDto){
       this.moduleService.saveModule(moduleRespAddDto);
       return RmpResponse.success();
    }
}
