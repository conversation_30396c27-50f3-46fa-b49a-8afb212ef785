package com.taikang.fly.check.comm.enums.OracleInitialTableSpaceEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Class;
import java.lang.Object;

public final class OracleInitialTableSpaceEnum extends Enum	// class@000085 from classes.dex
{
    private String code;
    private String msg;
    private static final OracleInitialTableSpaceEnum[] $VALUES;
    public static final OracleInitialTableSpaceEnum SYSTEM;
    public static final OracleInitialTableSpaceEnum TEMP;
    public static final OracleInitialTableSpaceEnum UNDOTBS1;
    public static final OracleInitialTableSpaceEnum USERS;

    static {
       OracleInitialTableSpaceEnum.USERS = new OracleInitialTableSpaceEnum("USERS", 0, "USERS", "USERS");
       OracleInitialTableSpaceEnum.SYSTEM = new OracleInitialTableSpaceEnum("SYSTEM", 1, "SYSTEM", "SYSTEM");
       OracleInitialTableSpaceEnum.UNDOTBS1 = new OracleInitialTableSpaceEnum("UNDOTBS1", 2, "UNDOTBS1", "UNDOTBS1");
       OracleInitialTableSpaceEnum.TEMP = new OracleInitialTableSpaceEnum("TEMP", 3, "TEMP", "TEMP");
       OracleInitialTableSpaceEnum[] oracleInitia = new OracleInitialTableSpaceEnum[]{OracleInitialTableSpaceEnum.USERS,OracleInitialTableSpaceEnum.SYSTEM,OracleInitialTableSpaceEnum.UNDOTBS1,OracleInitialTableSpaceEnum.TEMP};
       OracleInitialTableSpaceEnum.$VALUES = oracleInitia;
    }
    private void OracleInitialTableSpaceEnum(String p0,int p1,String code,String msg){
       super(p0, p1);
       this.code = code;
       this.msg = msg;
    }
    public static OracleInitialTableSpaceEnum valueOf(String name){
       return Enum.valueOf(OracleInitialTableSpaceEnum.class, name);
    }
    public static OracleInitialTableSpaceEnum[] values(){
       return OracleInitialTableSpaceEnum.$VALUES.clone();
    }
    public String getCode(){
       return this.code;
    }
}
