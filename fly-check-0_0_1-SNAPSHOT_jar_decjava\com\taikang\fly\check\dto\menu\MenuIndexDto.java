package com.taikang.fly.check.dto.menu.MenuIndexDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MenuIndexDto implements Serializable	// class@000191 from classes.dex
{
    private String aclass;
    private String expanded;
    private String iclass;
    private String isLeaf;
    private String isMenu;
    private int level;
    private String menuDesc;
    private String menuId;
    private String menuName;
    private String menuOrder;
    private String parentId;
    private String permission;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MenuIndexDto(){
       super();
       this.expanded = "false";
       this.isLeaf = "false";
    }
    protected boolean canEqual(Object other){
       return other instanceof MenuIndexDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MenuIndexDto){
          b = false;
       }else {
          MenuIndexDto menuIndexDto = o;
          if (!menuIndexDto.canEqual(this)) {
             b = false;
          }else {
             String menuId = this.getMenuId();
             String menuId1 = menuIndexDto.getMenuId();
             if (menuId == null) {
                if (menuId1 != null) {
                   b = false;
                }
             }else if(menuId.equals(menuId1)){
             }
             String menuName = this.getMenuName();
             String menuName1 = menuIndexDto.getMenuName();
             if (menuName == null) {
                if (menuName1 != null) {
                   b = false;
                }
             }else if(menuName.equals(menuName1)){
             }
             String menuDesc = this.getMenuDesc();
             String menuDesc1 = menuIndexDto.getMenuDesc();
             if (menuDesc == null) {
                if (menuDesc1 != null) {
                   b = false;
                }
             }else if(menuDesc.equals(menuDesc1)){
             }
             String url = this.getUrl();
             String url1 = menuIndexDto.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String iclass = this.getIclass();
             String iclass1 = menuIndexDto.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String aclass = this.getAclass();
             String aclass1 = menuIndexDto.getAclass();
             if (aclass == null) {
                if (aclass1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(aclass.equals(aclass1)){
             }
             String menuOrder = this.getMenuOrder();
             String menuOrder1 = menuIndexDto.getMenuOrder();
             if (menuOrder == null) {
                if (menuOrder1 != null) {
                   b = false;
                }
             }else if(menuOrder.equals(menuOrder1)){
             }
             String expanded = this.getExpanded();
             String expanded1 = menuIndexDto.getExpanded();
             if (expanded == null) {
                if (expanded1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(expanded.equals(expanded1)){
             }
             String isLeaf = this.getIsLeaf();
             String isLeaf1 = menuIndexDto.getIsLeaf();
             if (isLeaf == null) {
                if (isLeaf1 != null) {
                   b = false;
                }
             }else if(isLeaf.equals(isLeaf1)){
             }
             if (this.getLevel() != menuIndexDto.getLevel()) {
                b = false;
             }else {
                String parentId = this.getParentId();
                String parentId1 = menuIndexDto.getParentId();
                if (parentId == null) {
                   if (parentId1 != null) {
                      b = false;
                   }
                }else if(parentId.equals(parentId1)){
                }
                String isMenu = this.getIsMenu();
                String isMenu1 = menuIndexDto.getIsMenu();
                if (isMenu == null) {
                   if (isMenu1 != null) {
                   label_012b :
                      b = false;
                   }
                }else if(isMenu.equals(isMenu1)){
                }
                String permission = this.getPermission();
                String permission1 = menuIndexDto.getPermission();
                if (permission == null) {
                   if (permission1 != null) {
                      b = false;
                   }
                }else if(permission.equals(permission1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getAclass(){
       return this.aclass;
    }
    public String getExpanded(){
       return this.expanded;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIsLeaf(){
       return this.isLeaf;
    }
    public String getIsMenu(){
       return this.isMenu;
    }
    public int getLevel(){
       return this.level;
    }
    public String getMenuDesc(){
       return this.menuDesc;
    }
    public String getMenuId(){
       return this.menuId;
    }
    public String getMenuName(){
       return this.menuName;
    }
    public String getMenuOrder(){
       return this.menuOrder;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getPermission(){
       return this.permission;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $menuId;
       int PRIME = 59;
       int result = 1;
       int i = (($menuId = this.getMenuId()) == null)? 43: $menuId.hashCode();
       result = i + 59;
       String $menuName = this.getMenuName();
       int i1 = result * 59;
       i = ($menuName == null)? 43: $menuName.hashCode();
       result = i1 + i;
       String $menuDesc = this.getMenuDesc();
       i1 = result * 59;
       i = ($menuDesc == null)? 43: $menuDesc.hashCode();
       result = i1 + i;
       String $url = this.getUrl();
       i1 = result * 59;
       i = ($url == null)? 43: $url.hashCode();
       result = i1 + i;
       String $iclass = this.getIclass();
       i1 = result * 59;
       i = ($iclass == null)? 43: $iclass.hashCode();
       result = i1 + i;
       String aclass = this.getAclass();
       i1 = result * 59;
       i = (aclass == null)? 43: aclass.hashCode();
       String menuOrder = this.getMenuOrder();
       i1 = (i1 + i) * 59;
       i = (menuOrder == null)? 43: menuOrder.hashCode();
       String expanded = this.getExpanded();
       i1 = (i1 + i) * 59;
       i = (expanded == null)? 43: expanded.hashCode();
       String isLeaf = this.getIsLeaf();
       i1 = (i1 + i) * 59;
       i = (isLeaf == null)? 43: isLeaf.hashCode();
       String parentId = this.getParentId();
       i1 = (((i1 + i) * 59) + this.getLevel()) * 59;
       i = (parentId == null)? 43: parentId.hashCode();
       String isMenu = this.getIsMenu();
       i1 = (i1 + i) * 59;
       i = (isMenu == null)? 43: isMenu.hashCode();
       String permission = this.getPermission();
       i1 = (i1 + i) * 59;
       i = (permission == null)? 43: permission.hashCode();
       return (i1 + i);
    }
    public void setAclass(String aclass){
       this.aclass = aclass;
    }
    public void setExpanded(String expanded){
       this.expanded = expanded;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIsLeaf(String isLeaf){
       this.isLeaf = isLeaf;
    }
    public void setIsMenu(String isMenu){
       this.isMenu = isMenu;
    }
    public void setLevel(int level){
       this.level = level;
    }
    public void setMenuDesc(String menuDesc){
       this.menuDesc = menuDesc;
    }
    public void setMenuId(String menuId){
       this.menuId = menuId;
    }
    public void setMenuName(String menuName){
       this.menuName = menuName;
    }
    public void setMenuOrder(String menuOrder){
       this.menuOrder = menuOrder;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setPermission(String permission){
       this.permission = permission;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "MenuIndexDto\(menuId="+this.getMenuId()+", menuName="+this.getMenuName()+", menuDesc="+this.getMenuDesc()+", url="+this.getUrl()+", iclass="+this.getIclass()+", aclass="+this.getAclass()+", menuOrder="+this.getMenuOrder()+", expanded="+this.getExpanded()+", isLeaf="+this.getIsLeaf()+", level="+this.getLevel()+", parentId="+this.getParentId()+", isMenu="+this.getIsMenu()+", permission="+this.getPermission()+"\)";
    }
}
