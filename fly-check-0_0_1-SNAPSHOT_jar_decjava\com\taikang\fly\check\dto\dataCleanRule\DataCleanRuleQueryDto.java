package com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleQueryDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DataCleanRuleQueryDto	// class@0000de from classes.dex
{
    private String status;
    private String tableName;

    public void DataCleanRuleQueryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataCleanRuleQueryDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DataCleanRuleQueryDto) {
             b = false;
          }else {
             DataCleanRuleQueryDto uDataCleanRu = o;
             if (!uDataCleanRu.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = uDataCleanRu.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String status = this.getStatus();
                String status1 = uDataCleanRu.getStatus();
                if (status == null) {
                   if (status1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!status.equals(status1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getStatus(){
       return this.status;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       String $status = this.getStatus();
       i1 = result * 59;
       if ($status != null) {
          i = $status.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "DataCleanRuleQueryDto\(tableName="+this.getTableName()+", status="+this.getStatus()+"\)";
    }
}
