package com.taikang.fly.check.service.TransmissionServerDataService;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.dto.TransmissionServerDataDto;
import com.taikang.fly.check.utils.SCPUtil.ScpUtil;

public class TransmissionServerDataService	// class@000305 from classes.dex
{
    private static final Logger log;

    static {
       TransmissionServerDataService.log = LoggerFactory.getLogger(TransmissionServerDataService.class);
    }
    public void TransmissionServerDataService(){
       super();
    }
    public void getTransmissionServerData(TransmissionServerDataDto transmission){
       ScpUtil scpUtil = new ScpUtil();
       scpUtil.scpFile(transmission);
    }
}
