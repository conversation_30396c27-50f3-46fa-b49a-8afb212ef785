"""
飞行检查规则API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from flycheck.database import get_db
from flycheck.models import FlyRule, FlyRuleAudit
from flycheck.schemas.fly_rule import (
    FlyRuleCreate, 
    FlyRuleUpdate, 
    FlyRuleResponse,
    FlyRuleAuditCreate,
    FlyRuleAuditResponse
)
from flycheck.services.fly_rule_service import FlyRuleService
from flycheck.core.rule_engine import RuleEngine

router = APIRouter()


@router.get("/", response_model=List[FlyRuleResponse])
async def get_rules(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    rule_name: Optional[str] = Query(None),
    rule_category1: Optional[str] = Query(None),
    state: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取规则列表"""
    service = FlyRuleService(db)
    return service.get_rules(
        skip=skip, 
        limit=limit,
        rule_name=rule_name,
        rule_category1=rule_category1,
        state=state
    )


@router.get("/{rule_id}", response_model=FlyRuleResponse)
async def get_rule(rule_id: str, db: Session = Depends(get_db)):
    """获取单个规则"""
    service = FlyRuleService(db)
    rule = service.get_rule_by_id(rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")
    return rule


@router.post("/", response_model=FlyRuleResponse)
async def create_rule(rule_data: FlyRuleCreate, db: Session = Depends(get_db)):
    """创建规则"""
    service = FlyRuleService(db)
    return service.create_rule(rule_data)


@router.put("/{rule_id}", response_model=FlyRuleResponse)
async def update_rule(
    rule_id: str, 
    rule_data: FlyRuleUpdate, 
    db: Session = Depends(get_db)
):
    """更新规则"""
    service = FlyRuleService(db)
    rule = service.update_rule(rule_id, rule_data)
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")
    return rule


@router.delete("/{rule_id}")
async def delete_rule(rule_id: str, db: Session = Depends(get_db)):
    """删除规则"""
    service = FlyRuleService(db)
    success = service.delete_rule(rule_id)
    if not success:
        raise HTTPException(status_code=404, detail="规则不存在")
    return {"message": "规则删除成功"}


@router.post("/{rule_id}/validate")
async def validate_rule(rule_id: str, db: Session = Depends(get_db)):
    """验证规则"""
    service = FlyRuleService(db)
    rule = service.get_rule_by_id(rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")
    
    rule_engine = RuleEngine(db)
    is_valid, message = rule_engine.validate_rule(rule)
    
    return {
        "is_valid": is_valid,
        "message": message
    }


@router.post("/{rule_id}/execute")
async def execute_rule(
    rule_id: str, 
    params: dict = None,
    db: Session = Depends(get_db)
):
    """执行单个规则"""
    service = FlyRuleService(db)
    rule = service.get_rule_by_id(rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")
    
    rule_engine = RuleEngine(db)
    result = rule_engine.execute_rule(rule, params or {})
    
    return {
        "rule_id": result.rule_id,
        "status": result.status.value,
        "total_records": result.total_records,
        "error_records": result.error_records,
        "warning_records": result.warning_records,
        "execution_time": result.execution_time,
        "error_message": result.error_message,
        "result_file_path": result.result_file_path
    }


@router.get("/{rule_id}/audits", response_model=List[FlyRuleAuditResponse])
async def get_rule_audits(rule_id: str, db: Session = Depends(get_db)):
    """获取规则审核记录"""
    audits = db.query(FlyRuleAudit).filter(FlyRuleAudit.rule_id == rule_id).all()
    return audits


@router.post("/{rule_id}/audits", response_model=FlyRuleAuditResponse)
async def create_rule_audit(
    rule_id: str,
    audit_data: FlyRuleAuditCreate,
    db: Session = Depends(get_db)
):
    """创建规则审核"""
    # 检查规则是否存在
    rule = db.query(FlyRule).filter(FlyRule.id == rule_id).first()
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")
    
    audit = FlyRuleAudit(
        rule_id=rule_id,
        **audit_data.dict()
    )
    
    db.add(audit)
    db.commit()
    db.refresh(audit)
    
    return audit
