package com.taikang.fly.check.mybatis.dao.ClickhouseMultiFlyRuleTemplateMapper;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.ClickhouseMultiFlyRuleTemplate;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.MultiFlyRuleTemplate;
import java.lang.Integer;

public interface abstract ClickhouseMultiFlyRuleTemplateMapper	// class@0001dd from classes.dex
{

    List getTemplateNameList();
    int insert(ClickhouseMultiFlyRuleTemplate p0);
    List selectAll();
    MultiFlyRuleTemplate selectById(String p0);
    ClickhouseMultiFlyRuleTemplate selectByPrimaryKey(String p0);
    List selectRuleName();
    List templateNameList(String p0);
    Integer updateByPrimaryKey(MultiFlyRuleTemplate p0);
    Integer updateByPrimaryKeySelective(MultiFlyRuleTemplate p0);
}
