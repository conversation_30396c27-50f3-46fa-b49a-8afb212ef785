package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ClickhouseFlyRuleEditDto implements Serializable	// class@0000c1 from classes.dex
{
    private String diagnosisType;
    private String id;
    private String newSqlName;
    private String operateTime;
    private String ps;
    private String region;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String ruleType;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseFlyRuleEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseFlyRuleEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseFlyRuleEditDto){
          b = false;
       }else {
          ClickhouseFlyRuleEditDto uClickhouseF = o;
          if (!uClickhouseF.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uClickhouseF.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uClickhouseF.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = uClickhouseF.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = uClickhouseF.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uClickhouseF.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String operateTime = this.getOperateTime();
             String operateTime1 = uClickhouseF.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             String ps = this.getPs();
             String ps1 = uClickhouseF.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uClickhouseF.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uClickhouseF.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                label_00eb :
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uClickhouseF.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uClickhouseF.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uClickhouseF.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public String getOperateTime(){
       return this.operateTime;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $newSqlName = this.getNewSqlName();
       i1 = result * 59;
       i = ($newSqlName == null)? 43: $newSqlName.hashCode();
       result = i1 + i;
       String $ruleType = this.getRuleType();
       i1 = result * 59;
       i = ($ruleType == null)? 43: $ruleType.hashCode();
       result = i1 + i;
       String operateTime = this.getOperateTime();
       i1 = result * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setOperateTime(String operateTime){
       this.operateTime = operateTime;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public String toString(){
       return "ClickhouseFlyRuleEditDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", region="+this.getRegion()+", newSqlName="+this.getNewSqlName()+", ruleType="+this.getRuleType()+", operateTime="+this.getOperateTime()+", ps="+this.getPs()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ruleLevel="+this.getRuleLevel()+"\)";
    }
}
