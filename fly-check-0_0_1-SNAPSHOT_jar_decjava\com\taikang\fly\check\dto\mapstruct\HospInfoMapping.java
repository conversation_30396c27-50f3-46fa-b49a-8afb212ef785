package com.taikang.fly.check.dto.mapstruct.HospInfoMapping;
import com.taikang.fly.check.mybatis.domain.HospInfo;
import com.taikang.fly.check.dto.hospinfo.HospInfoDto;
import com.taikang.fly.check.dto.hospinfo.HospInfoRespDto;
import java.util.List;

public interface abstract HospInfoMapping	// class@00015c from classes.dex
{

    HospInfoDto hospInfo2HospInfoDto(HospInfo p0);
    HospInfoRespDto hospInfo2RespDto(HospInfo p0);
    List hospInfoList2RespDtoList(List p0);
}
