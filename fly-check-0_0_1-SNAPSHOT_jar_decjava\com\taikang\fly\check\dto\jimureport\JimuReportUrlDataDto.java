package com.taikang.fly.check.dto.jimureport.JimuReportUrlDataDto;
import java.lang.String;
import java.lang.Object;

public class JimuReportUrlDataDto	// class@000136 from classes.dex
{
    private String oracleName;
    private String ruleName;
    private String urlData;
    private String year;

    public void JimuReportUrlDataDto(String ruleName,String oracleName,String year,String urlData){
       super();
       this.ruleName = ruleName;
       this.oracleName = oracleName;
       this.year = year;
       this.urlData = urlData;
    }
    public String getOracleName(){
       return this.oracleName;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getUrlData(){
       return this.urlData;
    }
    public String getYear(){
       return this.year;
    }
    public void setOracleName(String oracleName){
       this.oracleName = oracleName;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setUrlData(String urlData){
       this.urlData = urlData;
    }
    public void setYear(String year){
       this.year = year;
    }
}
