package com.taikang.fly.check.dto.importData.SplitDataDto;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.lang.StringBuilder;

public class SplitDataDto	// class@000134 from classes.dex
{
    private String dataSource;
    private boolean hospIdIndexNeedCreateTag;
    private String hospitalId;
    private String hospitalName;
    private List tableNames;

    public void SplitDataDto(){
       super();
       this.hospIdIndexNeedCreateTag = true;
    }
    protected boolean canEqual(Object other){
       return other instanceof SplitDataDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SplitDataDto) {
             b = false;
          }else {
             SplitDataDto splitDataDto = o;
             if (!splitDataDto.canEqual(this)) {
                b = false;
             }else {
                List tableNames = this.getTableNames();
                List tableNames1 = splitDataDto.getTableNames();
                if (tableNames == null) {
                   if (tableNames1 != null) {
                      b = false;
                   }
                }else if(tableNames.equals(tableNames1)){
                }
                String hospitalId = this.getHospitalId();
                String hospitalId1 = splitDataDto.getHospitalId();
                if (hospitalId == null) {
                   if (hospitalId1 != null) {
                      b = false;
                   }
                }else if(hospitalId.equals(hospitalId1)){
                }
                String hospitalName = this.getHospitalName();
                String hospitalName1 = splitDataDto.getHospitalName();
                if (hospitalName == null) {
                   if (hospitalName1 != null) {
                      b = false;
                   }
                }else if(hospitalName.equals(hospitalName1)){
                }
                String dataSource = this.getDataSource();
                String dataSource1 = splitDataDto.getDataSource();
                if (dataSource == null) {
                   if (dataSource1 != null) {
                      b = false;
                   }
                }else if(dataSource.equals(dataSource1)){
                }
                if (this.isHospIdIndexNeedCreateTag() != splitDataDto.isHospIdIndexNeedCreateTag()) {
                   b = false;
                }
             }
          }
       }
       return b;
    }
    public String getDataSource(){
       return this.dataSource;
    }
    public String getHospitalId(){
       return this.hospitalId;
    }
    public String getHospitalName(){
       return this.hospitalName;
    }
    public List getTableNames(){
       return this.tableNames;
    }
    public int hashCode(){
       List $tableNames;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableNames = this.getTableNames()) == null)? i: $tableNames.hashCode();
       result = i1 + 59;
       String $hospitalId = this.getHospitalId();
       int i2 = result * 59;
       i1 = ($hospitalId == null)? i: $hospitalId.hashCode();
       result = i2 + i1;
       String $hospitalName = this.getHospitalName();
       i2 = result * 59;
       i1 = ($hospitalName == null)? i: $hospitalName.hashCode();
       result = i2 + i1;
       String $dataSource = this.getDataSource();
       i1 = result * 59;
       if ($dataSource != null) {
          i = $dataSource.hashCode();
       }
       result = i1 + i;
       i = result * 59;
       i1 = (this.isHospIdIndexNeedCreateTag())? 79: 97;
       return (i + i1);
    }
    public boolean isHospIdIndexNeedCreateTag(){
       return this.hospIdIndexNeedCreateTag;
    }
    public void setDataSource(String dataSource){
       this.dataSource = dataSource;
    }
    public void setHospIdIndexNeedCreateTag(boolean hospIdIndexNeedCreateTag){
       this.hospIdIndexNeedCreateTag = hospIdIndexNeedCreateTag;
    }
    public void setHospitalId(String hospitalId){
       this.hospitalId = hospitalId;
    }
    public void setHospitalName(String hospitalName){
       this.hospitalName = hospitalName;
    }
    public void setTableNames(List tableNames){
       this.tableNames = tableNames;
    }
    public String toString(){
       return "SplitDataDto\(tableNames="+this.getTableNames()+", hospitalId="+this.getHospitalId()+", hospitalName="+this.getHospitalName()+", dataSource="+this.getDataSource()+", hospIdIndexNeedCreateTag="+this.isHospIdIndexNeedCreateTag()+"\)";
    }
}
