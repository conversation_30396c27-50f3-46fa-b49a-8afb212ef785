package com.taikang.fly.check.dto.dataSourceResult.MDataExportDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class MDataExportDto implements Serializable	// class@0000df from classes.dex
{
    private String allData;
    private List dataSource;
    private String executeType;
    private String id;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void MDataExportDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MDataExportDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MDataExportDto) {
             b = false;
          }else {
             MDataExportDto mDataExportD = o;
             if (!mDataExportD.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = mDataExportD.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                List dataSource = this.getDataSource();
                List dataSource1 = mDataExportD.getDataSource();
                if (dataSource == null) {
                   if (dataSource1 != null) {
                      b = false;
                   }
                }else if(dataSource.equals(dataSource1)){
                }
                String executeType = this.getExecuteType();
                String executeType1 = mDataExportD.getExecuteType();
                if (executeType == null) {
                   if (executeType1 != null) {
                      b = false;
                   }
                }else if(executeType.equals(executeType1)){
                }
                String allData = this.getAllData();
                String allData1 = mDataExportD.getAllData();
                if (allData == null) {
                   if (allData1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!allData.equals(allData1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getAllData(){
       return this.allData;
    }
    public List getDataSource(){
       return this.dataSource;
    }
    public String getExecuteType(){
       return this.executeType;
    }
    public String getId(){
       return this.id;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       List $dataSource = this.getDataSource();
       int i2 = result * 59;
       i1 = ($dataSource == null)? i: $dataSource.hashCode();
       result = i2 + i1;
       String $executeType = this.getExecuteType();
       i2 = result * 59;
       i1 = ($executeType == null)? i: $executeType.hashCode();
       result = i2 + i1;
       String $allData = this.getAllData();
       i1 = result * 59;
       if ($allData != null) {
          i = $allData.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setAllData(String allData){
       this.allData = allData;
    }
    public void setDataSource(List dataSource){
       this.dataSource = dataSource;
    }
    public void setExecuteType(String executeType){
       this.executeType = executeType;
    }
    public void setId(String id){
       this.id = id;
    }
    public String toString(){
       return "MDataExportDto\(id="+this.getId()+", dataSource="+this.getDataSource()+", executeType="+this.getExecuteType()+", allData="+this.getAllData()+"\)";
    }
}
