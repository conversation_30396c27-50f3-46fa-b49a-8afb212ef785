"""
医保基础数据API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session
import pandas as pd

from flycheck.database import get_db
from flycheck.models import DrugCatalogue, DiagnosisTreatment, ConsumablesList

router = APIRouter()


@router.get("/drugs", response_model=List[dict])
async def get_drugs(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    drug_name: Optional[str] = Query(None),
    drug_code: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取药品目录"""
    query = db.query(DrugCatalogue).filter(DrugCatalogue.is_deleted == "0")
    
    if drug_name:
        query = query.filter(DrugCatalogue.drug_name.contains(drug_name))
    
    if drug_code:
        query = query.filter(DrugCatalogue.drug_code == drug_code)
    
    drugs = query.offset(skip).limit(limit).all()
    return [drug.to_dict() for drug in drugs]


@router.get("/diagnosis-treatments", response_model=List[dict])
async def get_diagnosis_treatments(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    item_name: Optional[str] = Query(None),
    item_code: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取诊疗项目目录"""
    query = db.query(DiagnosisTreatment).filter(DiagnosisTreatment.is_deleted == "0")
    
    if item_name:
        query = query.filter(DiagnosisTreatment.item_name.contains(item_name))
    
    if item_code:
        query = query.filter(DiagnosisTreatment.item_code == item_code)
    
    items = query.offset(skip).limit(limit).all()
    return [item.to_dict() for item in items]


@router.get("/consumables", response_model=List[dict])
async def get_consumables(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    consumable_name: Optional[str] = Query(None),
    consumable_code: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取医用耗材目录"""
    query = db.query(ConsumablesList).filter(ConsumablesList.is_deleted == "0")
    
    if consumable_name:
        query = query.filter(ConsumablesList.consumable_name.contains(consumable_name))
    
    if consumable_code:
        query = query.filter(ConsumablesList.consumable_code == consumable_code)
    
    items = query.offset(skip).limit(limit).all()
    return [item.to_dict() for item in items]


@router.post("/drugs/import")
async def import_drugs(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """导入药品目录"""
    if not file.filename.endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="文件格式不支持，请上传CSV或Excel文件")
    
    try:
        # 读取文件
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file.file, encoding='utf-8-sig')
        else:
            df = pd.read_excel(file.file)
        
        # 数据验证和清洗
        required_columns = ['drug_code', 'drug_name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400, 
                detail=f"缺少必要列: {', '.join(missing_columns)}"
            )
        
        # 批量插入
        imported_count = 0
        for _, row in df.iterrows():
            # 检查是否已存在
            existing = db.query(DrugCatalogue).filter(
                DrugCatalogue.drug_code == row['drug_code']
            ).first()
            
            if not existing:
                drug = DrugCatalogue(
                    drug_code=row['drug_code'],
                    drug_name=row['drug_name'],
                    drug_type=row.get('drug_type'),
                    specification=row.get('specification'),
                    dosage_form=row.get('dosage_form'),
                    manufacturer=row.get('manufacturer'),
                    medical_insurance_code=row.get('medical_insurance_code'),
                    payment_category=row.get('payment_category'),
                    limit_price=row.get('limit_price'),
                )
                db.add(drug)
                imported_count += 1
        
        db.commit()
        
        return {
            "message": f"成功导入 {imported_count} 条药品数据",
            "total_rows": len(df),
            "imported_count": imported_count
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.post("/diagnosis-treatments/import")
async def import_diagnosis_treatments(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """导入诊疗项目目录"""
    if not file.filename.endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="文件格式不支持，请上传CSV或Excel文件")
    
    try:
        # 读取文件
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file.file, encoding='utf-8-sig')
        else:
            df = pd.read_excel(file.file)
        
        # 数据验证
        required_columns = ['item_code', 'item_name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400, 
                detail=f"缺少必要列: {', '.join(missing_columns)}"
            )
        
        # 批量插入
        imported_count = 0
        for _, row in df.iterrows():
            # 检查是否已存在
            existing = db.query(DiagnosisTreatment).filter(
                DiagnosisTreatment.item_code == row['item_code']
            ).first()
            
            if not existing:
                item = DiagnosisTreatment(
                    item_code=row['item_code'],
                    item_name=row['item_name'],
                    item_type=row.get('item_type'),
                    unit=row.get('unit'),
                    price=row.get('price'),
                    medical_insurance_code=row.get('medical_insurance_code'),
                    payment_category=row.get('payment_category'),
                )
                db.add(item)
                imported_count += 1
        
        db.commit()
        
        return {
            "message": f"成功导入 {imported_count} 条诊疗项目数据",
            "total_rows": len(df),
            "imported_count": imported_count
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


@router.get("/stats")
async def get_medical_stats(db: Session = Depends(get_db)):
    """获取医保数据统计"""
    drug_count = db.query(DrugCatalogue).filter(DrugCatalogue.is_deleted == "0").count()
    diagnosis_count = db.query(DiagnosisTreatment).filter(DiagnosisTreatment.is_deleted == "0").count()
    consumable_count = db.query(ConsumablesList).filter(ConsumablesList.is_deleted == "0").count()
    
    return {
        "drug_count": drug_count,
        "diagnosis_treatment_count": diagnosis_count,
        "consumable_count": consumable_count,
        "total_count": drug_count + diagnosis_count + consumable_count
    }
