package com.taikang.fly.check.mybatis.domain.MfromtColumnConfig;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class MfromtColumnConfig	// class@000252 from classes.dex
{
    private String businessId;
    private String columnComment;
    private String columnName;
    private Integer columnWidth;
    private String createdBy;
    private LocalDateTime createdTime;
    private String dictType;
    private String id;
    private String isValid;
    private String modifier;
    private LocalDateTime modifyTime;
    private String parentId;
    private Integer sortNum;

    public void MfromtColumnConfig(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MfromtColumnConfig;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MfromtColumnConfig){
          b = false;
       }else {
          MfromtColumnConfig mfromtColumn = o;
          if (!mfromtColumn.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = mfromtColumn.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String businessId = this.getBusinessId();
             String businessId1 = mfromtColumn.getBusinessId();
             if (businessId == null) {
                if (businessId1 != null) {
                   b = false;
                }
             }else if(businessId.equals(businessId1)){
             }
             String parentId = this.getParentId();
             String parentId1 = mfromtColumn.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String columnName = this.getColumnName();
             String columnName1 = mfromtColumn.getColumnName();
             if (columnName == null) {
                if (columnName1 != null) {
                   b = false;
                }
             }else if(columnName.equals(columnName1)){
             }
             String columnCommen = this.getColumnComment();
             String columnCommen1 = mfromtColumn.getColumnComment();
             if (columnCommen == null) {
                if (columnCommen1 != null) {
                   b = false;
                }
             }else if(columnCommen.equals(columnCommen1)){
             }
             Integer columnWidth = this.getColumnWidth();
             Integer columnWidth1 = mfromtColumn.getColumnWidth();
             if (columnWidth == null) {
                if (columnWidth1 != null) {
                   b = false;
                }
             }else if(columnWidth.equals(columnWidth1)){
             }
             Integer sortNum = this.getSortNum();
             Integer sortNum1 = mfromtColumn.getSortNum();
             if (sortNum == null) {
                if (sortNum1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(sortNum.equals(sortNum1)){
             }
             String createdBy = this.getCreatedBy();
             String createdBy1 = mfromtColumn.getCreatedBy();
             if (createdBy == null) {
                if (createdBy1 != null) {
                   b = false;
                }
             }else if(createdBy.equals(createdBy1)){
             }
             LocalDateTime createdTime = this.getCreatedTime();
             LocalDateTime createdTime1 = mfromtColumn.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modifier = this.getModifier();
             String modifier1 = mfromtColumn.getModifier();
             if (modifier == null) {
                if (modifier1 != null) {
                label_0103 :
                   b = false;
                }
             }else if(modifier.equals(modifier1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = mfromtColumn.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = mfromtColumn.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             String dictType = this.getDictType();
             String dictType1 = mfromtColumn.getDictType();
             if (dictType == null) {
                if (dictType1 != null) {
                   b = false;
                }
             }else if(dictType.equals(dictType1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getBusinessId(){
       return this.businessId;
    }
    public String getColumnComment(){
       return this.columnComment;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public Integer getColumnWidth(){
       return this.columnWidth;
    }
    public String getCreatedBy(){
       return this.createdBy;
    }
    public LocalDateTime getCreatedTime(){
       return this.createdTime;
    }
    public String getDictType(){
       return this.dictType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModifier(){
       return this.modifier;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getParentId(){
       return this.parentId;
    }
    public Integer getSortNum(){
       return this.sortNum;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $businessId = this.getBusinessId();
       int i1 = result * 59;
       i = ($businessId == null)? 43: $businessId.hashCode();
       result = i1 + i;
       String $parentId = this.getParentId();
       i1 = result * 59;
       i = ($parentId == null)? 43: $parentId.hashCode();
       result = i1 + i;
       String $columnName = this.getColumnName();
       i1 = result * 59;
       i = ($columnName == null)? 43: $columnName.hashCode();
       result = i1 + i;
       String $columnComment = this.getColumnComment();
       i1 = result * 59;
       i = ($columnComment == null)? 43: $columnComment.hashCode();
       result = i1 + i;
       Integer columnWidth = this.getColumnWidth();
       i1 = result * 59;
       i = (columnWidth == null)? 43: columnWidth.hashCode();
       Integer sortNum = this.getSortNum();
       i1 = (i1 + i) * 59;
       i = (sortNum == null)? 43: sortNum.hashCode();
       String createdBy = this.getCreatedBy();
       i1 = (i1 + i) * 59;
       i = (createdBy == null)? 43: createdBy.hashCode();
       LocalDateTime createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String modifier = this.getModifier();
       i1 = (i1 + i) * 59;
       i = (modifier == null)? 43: modifier.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String isValid = this.getIsValid();
       i1 = (i1 + i) * 59;
       i = (isValid == null)? 43: isValid.hashCode();
       String dictType = this.getDictType();
       i1 = (i1 + i) * 59;
       i = (dictType == null)? 43: dictType.hashCode();
       return (i1 + i);
    }
    public MfromtColumnConfig setBusinessId(String businessId){
       this.businessId = businessId;
       return this;
    }
    public MfromtColumnConfig setColumnComment(String columnComment){
       this.columnComment = columnComment;
       return this;
    }
    public MfromtColumnConfig setColumnName(String columnName){
       this.columnName = columnName;
       return this;
    }
    public MfromtColumnConfig setColumnWidth(Integer columnWidth){
       this.columnWidth = columnWidth;
       return this;
    }
    public MfromtColumnConfig setCreatedBy(String createdBy){
       this.createdBy = createdBy;
       return this;
    }
    public MfromtColumnConfig setCreatedTime(LocalDateTime createdTime){
       this.createdTime = createdTime;
       return this;
    }
    public MfromtColumnConfig setDictType(String dictType){
       this.dictType = dictType;
       return this;
    }
    public MfromtColumnConfig setId(String id){
       this.id = id;
       return this;
    }
    public MfromtColumnConfig setIsValid(String isValid){
       this.isValid = isValid;
       return this;
    }
    public MfromtColumnConfig setModifier(String modifier){
       this.modifier = modifier;
       return this;
    }
    public MfromtColumnConfig setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public MfromtColumnConfig setParentId(String parentId){
       this.parentId = parentId;
       return this;
    }
    public MfromtColumnConfig setSortNum(Integer sortNum){
       this.sortNum = sortNum;
       return this;
    }
    public String toString(){
       return "MfromtColumnConfig\(id="+this.getId()+", businessId="+this.getBusinessId()+", parentId="+this.getParentId()+", columnName="+this.getColumnName()+", columnComment="+this.getColumnComment()+", columnWidth="+this.getColumnWidth()+", sortNum="+this.getSortNum()+", createdBy="+this.getCreatedBy()+", createdTime="+this.getCreatedTime()+", modifier="+this.getModifier()+", modifyTime="+this.getModifyTime()+", isValid="+this.getIsValid()+", dictType="+this.getDictType()+"\)";
    }
}
