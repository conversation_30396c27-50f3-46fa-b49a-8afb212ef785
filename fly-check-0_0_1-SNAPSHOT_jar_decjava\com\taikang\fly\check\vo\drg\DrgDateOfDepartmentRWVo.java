package com.taikang.fly.check.vo.drg.DrgDateOfDepartmentRWVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgDateOfDepartmentRWVo	// class@000363 from classes.dex
{
    private String adrgCode;
    private String drgCode;
    private String lastYear;
    private String lastYearRW;
    private String rwGrowthRate;
    private String year;
    private String yearRW;

    public void DrgDateOfDepartmentRWVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgDateOfDepartmentRWVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgDateOfDepartmentRWVo){
          b = false;
       }else {
          DrgDateOfDepartmentRWVo uDrgDateOfDe = o;
          if (!uDrgDateOfDe.canEqual(this)) {
             b = false;
          }else {
             String year = this.getYear();
             String year1 = uDrgDateOfDe.getYear();
             if (year == null) {
                if (year1 != null) {
                   b = false;
                }
             }else if(year.equals(year1)){
             }
             String yearRW = this.getYearRW();
             String yearRW1 = uDrgDateOfDe.getYearRW();
             if (yearRW == null) {
                if (yearRW1 != null) {
                   b = false;
                }
             }else if(yearRW.equals(yearRW1)){
             }
             String lastYearRW = this.getLastYearRW();
             String lastYearRW1 = uDrgDateOfDe.getLastYearRW();
             if (lastYearRW == null) {
                if (lastYearRW1 != null) {
                   b = false;
                }
             }else if(lastYearRW.equals(lastYearRW1)){
             }
             String lastYear = this.getLastYear();
             String lastYear1 = uDrgDateOfDe.getLastYear();
             if (lastYear == null) {
                if (lastYear1 != null) {
                   b = false;
                }
             }else if(lastYear.equals(lastYear1)){
             }
             String drgCode = this.getDrgCode();
             String drgCode1 = uDrgDateOfDe.getDrgCode();
             if (drgCode == null) {
                if (drgCode1 != null) {
                   b = false;
                }
             }else if(drgCode.equals(drgCode1)){
             }
             String adrgCode = this.getAdrgCode();
             String adrgCode1 = uDrgDateOfDe.getAdrgCode();
             if (adrgCode == null) {
                if (adrgCode1 != null) {
                   b = false;
                }
             }else if(adrgCode.equals(adrgCode1)){
             }
             String rwGrowthRate = this.getRwGrowthRate();
             String rwGrowthRate1 = uDrgDateOfDe.getRwGrowthRate();
             if (rwGrowthRate == null) {
                if (rwGrowthRate1 != null) {
                label_00b0 :
                   b = false;
                }
             }else if(rwGrowthRate.equals(rwGrowthRate1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAdrgCode(){
       return this.adrgCode;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getLastYear(){
       return this.lastYear;
    }
    public String getLastYearRW(){
       return this.lastYearRW;
    }
    public String getRwGrowthRate(){
       return this.rwGrowthRate;
    }
    public String getYear(){
       return this.year;
    }
    public String getYearRW(){
       return this.yearRW;
    }
    public int hashCode(){
       String $year;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($year = this.getYear()) == null)? i: $year.hashCode();
       result = i1 + 59;
       String $yearRW = this.getYearRW();
       int i2 = result * 59;
       i1 = ($yearRW == null)? i: $yearRW.hashCode();
       result = i2 + i1;
       String $lastYearRW = this.getLastYearRW();
       i2 = result * 59;
       i1 = ($lastYearRW == null)? i: $lastYearRW.hashCode();
       result = i2 + i1;
       String $lastYear = this.getLastYear();
       i2 = result * 59;
       i1 = ($lastYear == null)? i: $lastYear.hashCode();
       result = i2 + i1;
       String $drgCode = this.getDrgCode();
       i2 = result * 59;
       i1 = ($drgCode == null)? i: $drgCode.hashCode();
       result = i2 + i1;
       String adrgCode = this.getAdrgCode();
       i2 = result * 59;
       i1 = (adrgCode == null)? i: adrgCode.hashCode();
       String rwGrowthRate = this.getRwGrowthRate();
       i1 = (i2 + i1) * 59;
       if (rwGrowthRate != null) {
          i = rwGrowthRate.hashCode();
       }
       return (i1 + i);
    }
    public void setAdrgCode(String adrgCode){
       this.adrgCode = adrgCode;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setLastYear(String lastYear){
       this.lastYear = lastYear;
    }
    public void setLastYearRW(String lastYearRW){
       this.lastYearRW = lastYearRW;
    }
    public void setRwGrowthRate(String rwGrowthRate){
       this.rwGrowthRate = rwGrowthRate;
    }
    public void setYear(String year){
       this.year = year;
    }
    public void setYearRW(String yearRW){
       this.yearRW = yearRW;
    }
    public String toString(){
       return "DrgDateOfDepartmentRWVo\(year="+this.getYear()+", yearRW="+this.getYearRW()+", lastYearRW="+this.getLastYearRW()+", lastYear="+this.getLastYear()+", drgCode="+this.getDrgCode()+", adrgCode="+this.getAdrgCode()+", rwGrowthRate="+this.getRwGrowthRate()+"\)";
    }
}
