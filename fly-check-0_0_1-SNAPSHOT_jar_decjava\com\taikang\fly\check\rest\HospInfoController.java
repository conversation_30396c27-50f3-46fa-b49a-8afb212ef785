package com.taikang.fly.check.rest.HospInfoController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.comm.CommResponse;
import java.util.List;
import com.taikang.fly.check.service.HospInfoService;
import java.lang.Integer;
import com.taikang.fly.check.dto.hospinfo.HospInfoSearchDto;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.String;

public class HospInfoController	// class@000298 from classes.dex
{
    private HospInfoService hospInfoService;
    private static final Logger log;

    static {
       HospInfoController.log = LoggerFactory.getLogger(HospInfoController.class);
    }
    public void HospInfoController(){
       super();
    }
    public CommResponse getHosNames(){
       return CommResponse.success(this.hospInfoService.getHosNames());
    }
    public CommResponse getHospInfo(Integer page,Integer size,HospInfoSearchDto hospInfoSearchDto){
       return CommResponse.success(this.hospInfoService.findHospInfoList(page, size, hospInfoSearchDto));
    }
    public CommResponse removeHospInfo(List primaryKeys){
       List list = this.hospInfoService.batchDeleteHospInfo(primaryKeys);
       CommResponse uCommRespons = (!list.size())? CommResponse.success(): CommResponse.error(ResponseCodeEnum.ERROR.getCode(), ResponseCodeEnum.ERROR.getMsg(), list);
       return uCommRespons;
    }
}
