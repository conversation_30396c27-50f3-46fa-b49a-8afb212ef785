package com.taikang.fly.check.dto.exportReport.DataExportEntry;
import java.lang.Object;
import java.lang.Integer;
import java.lang.Double;
import java.lang.String;
import java.lang.StringBuilder;

public class DataExportEntry	// class@0000ef from classes.dex
{
    private double avgDay;
    private double avgFeeMZ;
    private double avgFeeZY;
    private Integer multiMdtrtIdMZ;
    private Integer multiMdtrtIdZY;
    private Integer mzmxCount;
    private Integer mzzdCount;
    private Integer negativeFeeMZ;
    private Integer negativeFeeZY;
    private Integer negativeMedfeeSumamtMZ;
    private Integer negativeMedfeeSumamtZY;
    private Integer notRelatedMZ;
    private Integer notRelatedZY;
    private Integer numOfPatientsMZ;
    private Integer numOfPatientsZY;
    private Integer total;
    private Integer zymxCount;
    private Integer zyzdCount;

    public void DataExportEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataExportEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DataExportEntry){
          b = false;
       }else {
          DataExportEntry uDataExportE = o;
          if (!uDataExportE.canEqual(this)) {
             b = false;
          }else {
             Integer zymxCount = this.getZymxCount();
             Integer zymxCount1 = uDataExportE.getZymxCount();
             if (zymxCount == null) {
                if (zymxCount1 != null) {
                   b = false;
                }
             }else if(zymxCount.equals(zymxCount1)){
             }
             Integer zyzdCount = this.getZyzdCount();
             Integer zyzdCount1 = uDataExportE.getZyzdCount();
             if (zyzdCount == null) {
                if (zyzdCount1 != null) {
                   b = false;
                }
             }else if(zyzdCount.equals(zyzdCount1)){
             }
             Integer mzmxCount = this.getMzmxCount();
             Integer mzmxCount1 = uDataExportE.getMzmxCount();
             if (mzmxCount == null) {
                if (mzmxCount1 != null) {
                   b = false;
                }
             }else if(mzmxCount.equals(mzmxCount1)){
             }
             Integer mzzdCount = this.getMzzdCount();
             Integer mzzdCount1 = uDataExportE.getMzzdCount();
             if (mzzdCount == null) {
                if (mzzdCount1 != null) {
                   b = false;
                }
             }else if(mzzdCount.equals(mzzdCount1)){
             }
             Integer total = this.getTotal();
             Integer total1 = uDataExportE.getTotal();
             if (total == null) {
                if (total1 != null) {
                   b = false;
                }
             }else if(total.equals(total1)){
             }
             Integer numOfPatient = this.getNumOfPatientsMZ();
             Integer numOfPatient1 = uDataExportE.getNumOfPatientsMZ();
             if (numOfPatient == null) {
                if (numOfPatient1 != null) {
                   b = false;
                }
             }else if(numOfPatient.equals(numOfPatient1)){
             }
             Integer numOfPatient2 = this.getNumOfPatientsZY();
             Integer numOfPatient3 = uDataExportE.getNumOfPatientsZY();
             if (numOfPatient2 == null) {
                if (numOfPatient3 != null) {
                label_00bf :
                   b = false;
                }
             }else if(numOfPatient2.equals(numOfPatient3)){
             }
             if (Double.compare(this.getAvgFeeZY(), uDataExportE.getAvgFeeZY())) {
                b = false;
             }else if(Double.compare(this.getAvgFeeMZ(), uDataExportE.getAvgFeeMZ())){
                b = false;
             }else if(Double.compare(this.getAvgDay(), uDataExportE.getAvgDay())){
                b = false;
             }else {
                Integer notRelatedZY = this.getNotRelatedZY();
                Integer notRelatedZY1 = uDataExportE.getNotRelatedZY();
                if (notRelatedZY == null) {
                   if (notRelatedZY1 != null) {
                      b = false;
                   }
                }else if(notRelatedZY.equals(notRelatedZY1)){
                }
                Integer notRelatedMZ = this.getNotRelatedMZ();
                Integer notRelatedMZ1 = uDataExportE.getNotRelatedMZ();
                if (notRelatedMZ == null) {
                   if (notRelatedMZ1 != null) {
                      b = false;
                   }
                }else if(notRelatedMZ.equals(notRelatedMZ1)){
                }
                Integer negativeFeeZ = this.getNegativeFeeZY();
                Integer negativeFeeZ1 = uDataExportE.getNegativeFeeZY();
                if (negativeFeeZ == null) {
                   if (negativeFeeZ1 != null) {
                   label_013d :
                      b = false;
                   }
                }else if(negativeFeeZ.equals(negativeFeeZ1)){
                }
                Integer negativeFeeM = this.getNegativeFeeMZ();
                Integer negativeFeeM1 = uDataExportE.getNegativeFeeMZ();
                if (negativeFeeM == null) {
                   if (negativeFeeM1 != null) {
                      b = false;
                   }
                }else if(negativeFeeM.equals(negativeFeeM1)){
                }
                Integer negativeMedf = this.getNegativeMedfeeSumamtZY();
                Integer negativeMedf1 = uDataExportE.getNegativeMedfeeSumamtZY();
                if (negativeMedf == null) {
                   if (negativeMedf1 != null) {
                   label_016d :
                      b = false;
                   }
                }else if(negativeMedf.equals(negativeMedf1)){
                }
                Integer negativeMedf2 = this.getNegativeMedfeeSumamtMZ();
                Integer negativeMedf3 = uDataExportE.getNegativeMedfeeSumamtMZ();
                if (negativeMedf2 == null) {
                   if (negativeMedf3 != null) {
                      b = false;
                   }
                }else if(negativeMedf2.equals(negativeMedf3)){
                }
                Integer multiMdtrtId = this.getMultiMdtrtIdMZ();
                Integer multiMdtrtId1 = uDataExportE.getMultiMdtrtIdMZ();
                if (multiMdtrtId == null) {
                   if (multiMdtrtId1 != null) {
                   label_019d :
                      b = false;
                   }
                }else if(multiMdtrtId.equals(multiMdtrtId1)){
                }
                Integer multiMdtrtId2 = this.getMultiMdtrtIdZY();
                Integer multiMdtrtId3 = uDataExportE.getMultiMdtrtIdZY();
                if (multiMdtrtId2 == null) {
                   if (multiMdtrtId3 != null) {
                      b = false;
                   }
                }else if(multiMdtrtId2.equals(multiMdtrtId3)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public double getAvgDay(){
       return this.avgDay;
    }
    public double getAvgFeeMZ(){
       return this.avgFeeMZ;
    }
    public double getAvgFeeZY(){
       return this.avgFeeZY;
    }
    public Integer getMultiMdtrtIdMZ(){
       return this.multiMdtrtIdMZ;
    }
    public Integer getMultiMdtrtIdZY(){
       return this.multiMdtrtIdZY;
    }
    public Integer getMzmxCount(){
       return this.mzmxCount;
    }
    public Integer getMzzdCount(){
       return this.mzzdCount;
    }
    public Integer getNegativeFeeMZ(){
       return this.negativeFeeMZ;
    }
    public Integer getNegativeFeeZY(){
       return this.negativeFeeZY;
    }
    public Integer getNegativeMedfeeSumamtMZ(){
       return this.negativeMedfeeSumamtMZ;
    }
    public Integer getNegativeMedfeeSumamtZY(){
       return this.negativeMedfeeSumamtZY;
    }
    public Integer getNotRelatedMZ(){
       return this.notRelatedMZ;
    }
    public Integer getNotRelatedZY(){
       return this.notRelatedZY;
    }
    public Integer getNumOfPatientsMZ(){
       return this.numOfPatientsMZ;
    }
    public Integer getNumOfPatientsZY(){
       return this.numOfPatientsZY;
    }
    public Integer getTotal(){
       return this.total;
    }
    public Integer getZymxCount(){
       return this.zymxCount;
    }
    public Integer getZyzdCount(){
       return this.zyzdCount;
    }
    public int hashCode(){
       Integer $zymxCount;
       int PRIME = 59;
       int result = 1;
       int i = (($zymxCount = this.getZymxCount()) == null)? 43: $zymxCount.hashCode();
       result = i + 59;
       Integer $zyzdCount = this.getZyzdCount();
       int i1 = result * 59;
       i = ($zyzdCount == null)? 43: $zyzdCount.hashCode();
       result = i1 + i;
       Integer $mzmxCount = this.getMzmxCount();
       i1 = result * 59;
       i = ($mzmxCount == null)? 43: $mzmxCount.hashCode();
       result = i1 + i;
       Integer $mzzdCount = this.getMzzdCount();
       i1 = result * 59;
       i = ($mzzdCount == null)? 43: $mzzdCount.hashCode();
       result = i1 + i;
       Integer $total = this.getTotal();
       i1 = result * 59;
       i = ($total == null)? 43: $total.hashCode();
       result = i1 + i;
       Integer numOfPatient = this.getNumOfPatientsMZ();
       i1 = result * 59;
       i = (numOfPatient == null)? 43: numOfPatient.hashCode();
       Integer numOfPatient1 = this.getNumOfPatientsZY();
       i1 = (i1 + i) * 59;
       i = (numOfPatient1 == null)? 43: numOfPatient1.hashCode();
       long l = Double.doubleToLongBits(this.getAvgFeeZY());
       long l1 = Double.doubleToLongBits(this.getAvgFeeMZ());
       long l2 = Double.doubleToLongBits(this.getAvgDay());
       Integer notRelatedZY = this.getNotRelatedZY();
       i1 = (((((((i1 + i) * 59) + (int)((l >> 32) ^ l)) * 59) + (int)((l1 >> 32) ^ l1)) * 59) + (int)((l2 >> 32) ^ l2)) * 59;
       i = (notRelatedZY == null)? 43: notRelatedZY.hashCode();
       Integer notRelatedMZ = this.getNotRelatedMZ();
       i1 = (i1 + i) * 59;
       i = (notRelatedMZ == null)? 43: notRelatedMZ.hashCode();
       Integer negativeFeeZ = this.getNegativeFeeZY();
       i1 = (i1 + i) * 59;
       i = (negativeFeeZ == null)? 43: negativeFeeZ.hashCode();
       Integer negativeFeeM = this.getNegativeFeeMZ();
       i1 = (i1 + i) * 59;
       i = (negativeFeeM == null)? 43: negativeFeeM.hashCode();
       Integer negativeMedf = this.getNegativeMedfeeSumamtZY();
       i1 = (i1 + i) * 59;
       i = (negativeMedf == null)? 43: negativeMedf.hashCode();
       Integer negativeMedf1 = this.getNegativeMedfeeSumamtMZ();
       i1 = (i1 + i) * 59;
       i = (negativeMedf1 == null)? 43: negativeMedf1.hashCode();
       Integer multiMdtrtId = this.getMultiMdtrtIdMZ();
       i1 = (i1 + i) * 59;
       i = (multiMdtrtId == null)? 43: multiMdtrtId.hashCode();
       Integer multiMdtrtId1 = this.getMultiMdtrtIdZY();
       i1 = (i1 + i) * 59;
       i = (multiMdtrtId1 == null)? 43: multiMdtrtId1.hashCode();
       return (i1 + i);
    }
    public void setAvgDay(double avgDay){
       this.avgDay = avgDay;
    }
    public void setAvgFeeMZ(double avgFeeMZ){
       this.avgFeeMZ = avgFeeMZ;
    }
    public void setAvgFeeZY(double avgFeeZY){
       this.avgFeeZY = avgFeeZY;
    }
    public void setMultiMdtrtIdMZ(Integer multiMdtrtIdMZ){
       this.multiMdtrtIdMZ = multiMdtrtIdMZ;
    }
    public void setMultiMdtrtIdZY(Integer multiMdtrtIdZY){
       this.multiMdtrtIdZY = multiMdtrtIdZY;
    }
    public void setMzmxCount(Integer mzmxCount){
       this.mzmxCount = mzmxCount;
    }
    public void setMzzdCount(Integer mzzdCount){
       this.mzzdCount = mzzdCount;
    }
    public void setNegativeFeeMZ(Integer negativeFeeMZ){
       this.negativeFeeMZ = negativeFeeMZ;
    }
    public void setNegativeFeeZY(Integer negativeFeeZY){
       this.negativeFeeZY = negativeFeeZY;
    }
    public void setNegativeMedfeeSumamtMZ(Integer negativeMedfeeSumamtMZ){
       this.negativeMedfeeSumamtMZ = negativeMedfeeSumamtMZ;
    }
    public void setNegativeMedfeeSumamtZY(Integer negativeMedfeeSumamtZY){
       this.negativeMedfeeSumamtZY = negativeMedfeeSumamtZY;
    }
    public void setNotRelatedMZ(Integer notRelatedMZ){
       this.notRelatedMZ = notRelatedMZ;
    }
    public void setNotRelatedZY(Integer notRelatedZY){
       this.notRelatedZY = notRelatedZY;
    }
    public void setNumOfPatientsMZ(Integer numOfPatientsMZ){
       this.numOfPatientsMZ = numOfPatientsMZ;
    }
    public void setNumOfPatientsZY(Integer numOfPatientsZY){
       this.numOfPatientsZY = numOfPatientsZY;
    }
    public void setTotal(Integer total){
       this.total = total;
    }
    public void setZymxCount(Integer zymxCount){
       this.zymxCount = zymxCount;
    }
    public void setZyzdCount(Integer zyzdCount){
       this.zyzdCount = zyzdCount;
    }
    public String toString(){
       return "DataExportEntry\(zymxCount="+this.getZymxCount()+", zyzdCount="+this.getZyzdCount()+", mzmxCount="+this.getMzmxCount()+", mzzdCount="+this.getMzzdCount()+", total="+this.getTotal()+", numOfPatientsMZ="+this.getNumOfPatientsMZ()+", numOfPatientsZY="+this.getNumOfPatientsZY()+", avgFeeZY="+this.getAvgFeeZY()+", avgFeeMZ="+this.getAvgFeeMZ()+", avgDay="+this.getAvgDay()+", notRelatedZY="+this.getNotRelatedZY()+", notRelatedMZ="+this.getNotRelatedMZ()+", negativeFeeZY="+this.getNegativeFeeZY()+", negativeFeeMZ="+this.getNegativeFeeMZ()+", negativeMedfeeSumamtZY="+this.getNegativeMedfeeSumamtZY()+", negativeMedfeeSumamtMZ="+this.getNegativeMedfeeSumamtMZ()+", multiMdtrtIdMZ="+this.getMultiMdtrtIdMZ()+", multiMdtrtIdZY="+this.getMultiMdtrtIdZY()+"\)";
    }
}
