package com.taikang.fly.check.comm.ModelWorkflowException;
import java.lang.RuntimeException;
import java.lang.Exception;
import java.lang.String;

public class ModelWorkflowException extends RuntimeException	// class@000074 from classes.dex
{
    private final String message;

    public void ModelWorkflowException(Exception e){
       super(e.getMessage());
    }
    public void ModelWorkflowException(String message){
       super();
       this.message = message;
    }
    public String getMessage(){
       return this.message;
    }
}
