package com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MultiFlyRuleTemplateSearchDto implements Serializable	// class@000123 from classes.dex
{
    private String diagnosisType;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String templateName;
    private String templateType;
    private static final long serialVersionUID = 0x1;

    public void MultiFlyRuleTemplateSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MultiFlyRuleTemplateSearchDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MultiFlyRuleTemplateSearchDto){
          b = false;
       }else {
          MultiFlyRuleTemplateSearchDto multiFlyRule = o;
          if (!multiFlyRule.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = multiFlyRule.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = multiFlyRule.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = multiFlyRule.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = multiFlyRule.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = multiFlyRule.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = multiFlyRule.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String templateType = this.getTemplateType();
             String templateType1 = multiFlyRule.getTemplateType();
             if (templateType == null) {
                if (templateType1 != null) {
                label_00b0 :
                   b = false;
                }
             }else if(templateType.equals(templateType1)){
             }
             String templateName = this.getTemplateName();
             String templateName1 = multiFlyRule.getTemplateName();
             if (templateName == null) {
                if (templateName1 != null) {
                   b = false;
                }
             }else if(templateName.equals(templateName1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getTemplateName(){
       return this.templateName;
    }
    public String getTemplateType(){
       return this.templateType;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $ruleLevel = this.getRuleLevel();
       int i2 = result * 59;
       i1 = ($ruleLevel == null)? i: $ruleLevel.hashCode();
       result = i2 + i1;
       String $ruleCategory1 = this.getRuleCategory1();
       i2 = result * 59;
       i1 = ($ruleCategory1 == null)? i: $ruleCategory1.hashCode();
       result = i2 + i1;
       String $ruleCategory2 = this.getRuleCategory2();
       i2 = result * 59;
       i1 = ($ruleCategory2 == null)? i: $ruleCategory2.hashCode();
       result = i2 + i1;
       String $diagnosisType = this.getDiagnosisType();
       i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       String ruleDescribe = this.getRuleDescribe();
       i2 = result * 59;
       i1 = (ruleDescribe == null)? i: ruleDescribe.hashCode();
       String templateType = this.getTemplateType();
       i2 = (i2 + i1) * 59;
       i1 = (templateType == null)? i: templateType.hashCode();
       String templateName = this.getTemplateName();
       i1 = (i2 + i1) * 59;
       if (templateName != null) {
          i = templateName.hashCode();
       }
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setTemplateName(String templateName){
       this.templateName = templateName;
    }
    public void setTemplateType(String templateType){
       this.templateType = templateType;
    }
    public String toString(){
       return "MultiFlyRuleTemplateSearchDto\(ruleName="+this.getRuleName()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", templateType="+this.getTemplateType()+", templateName="+this.getTemplateName()+"\)";
    }
}
