package com.taikang.fly.check.dto.mapstruct.OperativeEncodingMapping;
import com.taikang.fly.check.mybatis.domain.GlOperativeEncoding;
import com.taikang.fly.check.dto.operativeencoding.OperativeEncodingDto;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.YbOperativeEncoding;

public interface abstract OperativeEncodingMapping	// class@00016f from classes.dex
{

    OperativeEncodingDto toGlDto(GlOperativeEncoding p0);
    List toGlDtoList(List p0);
    GlOperativeEncoding toGlEntity(OperativeEncodingDto p0);
    List toGlEntityList(List p0);
    OperativeEncodingDto toYbDto(YbOperativeEncoding p0);
    List toYbDtoList(List p0);
    YbOperativeEncoding toYbEntity(OperativeEncodingDto p0);
    List toYbEntityList(List p0);
}
