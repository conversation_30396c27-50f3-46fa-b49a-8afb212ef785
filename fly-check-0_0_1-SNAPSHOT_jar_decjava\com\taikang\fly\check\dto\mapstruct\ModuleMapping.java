package com.taikang.fly.check.dto.mapstruct.ModuleMapping;
import com.taikang.fly.check.dto.module.ModuleRespAddDto;
import com.taikang.fly.check.mybatis.domain.Module;
import com.taikang.fly.check.dto.module.ModuleRespEditDto;
import com.taikang.fly.check.dto.module.ModuleIgnoreFiledRespDto;
import java.util.List;
import com.taikang.fly.check.dto.module.ModuleRespDto;

public interface abstract ModuleMapping	// class@00016b from classes.dex
{

    Module moduleRespAddDtoToModule(ModuleRespAddDto p0);
    Module moduleRespEditDtoToModule(ModuleRespEditDto p0);
    ModuleIgnoreFiledRespDto moduleToModuleIgnoreFieldRespDto(Module p0);
    List moduleToModuleIgnoreFieldRespDtos(List p0);
    ModuleRespDto moduleToModuleRespDto(Module p0);
    List usersToUserDtos(List p0);
}
