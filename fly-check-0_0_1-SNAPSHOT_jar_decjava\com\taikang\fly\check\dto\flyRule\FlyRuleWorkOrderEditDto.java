package com.taikang.fly.check.dto.flyRule.FlyRuleWorkOrderEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleWorkOrderEditDto implements Serializable	// class@00010e from classes.dex
{
    private String diagnosisType;
    private String id;
    private String isSpecial;
    private String newSqlName;
    private String operateTime;
    private String policyBasis;
    private String ps;
    private String region;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleDimension;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;
    private String ruleSuitTimeEnd;
    private String ruleSuitTimeStart;
    private String ruleType;
    private String sqlName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleWorkOrderEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleWorkOrderEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleWorkOrderEditDto){
          b = false;
       }else {
          FlyRuleWorkOrderEditDto uFlyRuleWork = o;
          if (!uFlyRuleWork.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFlyRuleWork.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleWork.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleWork.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uFlyRuleWork.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = uFlyRuleWork.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleWork.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String operateTime = this.getOperateTime();
             String operateTime1 = uFlyRuleWork.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                label_00c1 :
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             String ps = this.getPs();
             String ps1 = uFlyRuleWork.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uFlyRuleWork.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                label_00f1 :
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uFlyRuleWork.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleWork.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                label_0121 :
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uFlyRuleWork.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleWork.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                label_0151 :
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleWork.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = uFlyRuleWork.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             String ruleSuitTime = this.getRuleSuitTimeStart();
             String ruleSuitTime1 = uFlyRuleWork.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                label_0199 :
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             String ruleSuitTime2 = this.getRuleSuitTimeEnd();
             String ruleSuitTime3 = uFlyRuleWork.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = uFlyRuleWork.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                label_01cd :
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = uFlyRuleWork.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public String getOperateTime(){
       return this.operateTime;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public String getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $sqlName = this.getSqlName();
       i1 = result * 59;
       i = ($sqlName == null)? 43: $sqlName.hashCode();
       result = i1 + i;
       String $newSqlName = this.getNewSqlName();
       i1 = result * 59;
       i = ($newSqlName == null)? 43: $newSqlName.hashCode();
       result = i1 + i;
       String ruleType = this.getRuleType();
       i1 = result * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       String ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       String ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleScopeApp = this.getRuleScopeApply();
       i1 = (i1 + i) * 59;
       i = (ruleScopeApp == null)? 43: ruleScopeApp.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setOperateTime(String operateTime){
       this.operateTime = operateTime;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setRuleSuitTimeEnd(String ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(String ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public String toString(){
       return "FlyRuleWorkOrderEditDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", region="+this.getRegion()+", sqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", ruleType="+this.getRuleType()+", operateTime="+this.getOperateTime()+", ps="+this.getPs()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ruleLevel="+this.getRuleLevel()+", policyBasis="+this.getPolicyBasis()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleScopeApply="+this.getRuleScopeApply()+", ruleDimension="+this.getRuleDimension()+"\)";
    }
}
