package com.taikang.fly.check.dto.flyRule.FlyRuleAuditConfirmationDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleAuditConfirmationDto implements Serializable	// class@0000f8 from classes.dex
{
    private String id;
    private String newSqlName;
    private String ps;
    private String ruleName;
    private String sqlName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleAuditConfirmationDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleAuditConfirmationDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleAuditConfirmationDto) {
             b = false;
          }else {
             FlyRuleAuditConfirmationDto uFlyRuleAudi = o;
             if (!uFlyRuleAudi.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uFlyRuleAudi.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = uFlyRuleAudi.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String sqlName = this.getSqlName();
                String sqlName1 = uFlyRuleAudi.getSqlName();
                if (sqlName == null) {
                   if (sqlName1 != null) {
                      b = false;
                   }
                }else if(sqlName.equals(sqlName1)){
                }
                String newSqlName = this.getNewSqlName();
                String newSqlName1 = uFlyRuleAudi.getNewSqlName();
                if (newSqlName == null) {
                   if (newSqlName1 != null) {
                      b = false;
                   }
                }else if(newSqlName.equals(newSqlName1)){
                }
                String ps = this.getPs();
                String ps1 = uFlyRuleAudi.getPs();
                if (ps == null) {
                   if (ps1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!ps.equals(ps1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $sqlName = this.getSqlName();
       i2 = result * 59;
       i1 = ($sqlName == null)? i: $sqlName.hashCode();
       result = i2 + i1;
       String $newSqlName = this.getNewSqlName();
       i2 = result * 59;
       i1 = ($newSqlName == null)? i: $newSqlName.hashCode();
       result = i2 + i1;
       String $ps = this.getPs();
       i1 = result * 59;
       if ($ps != null) {
          i = $ps.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public String toString(){
       return "FlyRuleAuditConfirmationDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", sqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", ps="+this.getPs()+"\)";
    }
}
