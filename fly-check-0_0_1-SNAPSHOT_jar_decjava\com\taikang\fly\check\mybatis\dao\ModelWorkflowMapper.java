package com.taikang.fly.check.mybatis.dao.ModelWorkflowMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.ModelWorkflow;
import java.time.LocalDate;

public interface abstract ModelWorkflowMapper implements BaseMapper	// class@000210 from classes.dex
{

    ModelWorkflow selectByUser(String p0);
    void updateStage(String p0,char p1);
    void updateStageAndError(String p0,char p1,String p2);
    void updateStageAndSettleTime(String p0,char p1,LocalDate p2,LocalDate p3,String p4);
    void updateStageAndSql(String p0,char p1,String p2,String p3,String p4);
    void updateStatus(String p0);
}
