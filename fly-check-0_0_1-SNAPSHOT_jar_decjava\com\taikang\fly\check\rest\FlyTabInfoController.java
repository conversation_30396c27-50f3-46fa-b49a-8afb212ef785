package com.taikang.fly.check.rest.FlyTabInfoController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import org.springframework.web.multipart.MultipartFile;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.FlyTabInfoService;
import java.util.List;

public class FlyTabInfoController	// class@000295 from classes.dex
{
    private FlyTabInfoService flyTabInfoService;
    private static final Logger log;

    static {
       FlyTabInfoController.log = LoggerFactory.getLogger(FlyTabInfoController.class);
    }
    public void FlyTabInfoController(){
       super();
    }
    public CommResponse importFlyRuleInfo(MultipartFile file){
       this.flyTabInfoService.importFlyRuleInfo(file);
       return CommResponse.success();
    }
    public CommResponse importFlyRuleInfo2(MultipartFile file){
       return this.flyTabInfoService.importFlyRuleInfo2(file);
    }
    public CommResponse queryFlySoftInfo(){
       return CommResponse.success(this.flyTabInfoService.queryFlySoftInfo());
    }
    public CommResponse queryFlyTabInfo(){
       return CommResponse.success(this.flyTabInfoService.queryFlyTabInfo());
    }
}
