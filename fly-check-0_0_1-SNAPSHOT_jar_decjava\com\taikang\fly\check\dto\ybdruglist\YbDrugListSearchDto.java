package com.taikang.fly.check.dto.ybdruglist.YbDrugListSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class YbDrugListSearchDto implements Serializable	// class@0001d5 from classes.dex
{
    private String drugCode;
    private String drugMame;
    private static final long serialVersionUID = 0x1;

    public void YbDrugListSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbDrugListSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof YbDrugListSearchDto) {
             b = false;
          }else {
             YbDrugListSearchDto ybDrugListSe = o;
             if (!ybDrugListSe.canEqual(this)) {
                b = false;
             }else {
                String drugCode = this.getDrugCode();
                String drugCode1 = ybDrugListSe.getDrugCode();
                if (drugCode == null) {
                   if (drugCode1 != null) {
                      b = false;
                   }
                }else if(drugCode.equals(drugCode1)){
                }
                String drugMame = this.getDrugMame();
                String drugMame1 = ybDrugListSe.getDrugMame();
                if (drugMame == null) {
                   if (drugMame1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!drugMame.equals(drugMame1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrugCode(){
       return this.drugCode;
    }
    public String getDrugMame(){
       return this.drugMame;
    }
    public int hashCode(){
       String $drugCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drugCode = this.getDrugCode()) == null)? i: $drugCode.hashCode();
       result = i1 + 59;
       String $drugMame = this.getDrugMame();
       i1 = result * 59;
       if ($drugMame != null) {
          i = $drugMame.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrugCode(String drugCode){
       this.drugCode = drugCode;
    }
    public void setDrugMame(String drugMame){
       this.drugMame = drugMame;
    }
    public String toString(){
       return "YbDrugListSearchDto\(drugCode="+this.getDrugCode()+", drugMame="+this.getDrugMame()+"\)";
    }
}
