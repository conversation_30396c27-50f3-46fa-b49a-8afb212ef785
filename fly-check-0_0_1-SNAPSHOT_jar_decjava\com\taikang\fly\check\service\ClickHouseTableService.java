package com.taikang.fly.check.service.ClickHouseTableService;
import com.baomidou.mybatisplus.extension.service.IService;
import java.lang.String;
import java.util.List;

public interface abstract ClickHouseTableService implements IService	// class@0002ca from classes.dex
{

    void createBillAndDetails();
    void delClickhouseTable(String p0);
    void execute(List p0);
    void geneSql(String p0);
    List queryClickHouseTableField(String p0);
    List queryClickHouseTableList();
    String querySqlById(String p0);
    void renameClickhouseTable(String p0,String p1);
    void saveClickHouseTable(String p0,String p1,int p2);
}
