package org.springframework.boot.loader.PropertiesLauncher$ArchiveEntryFilter;
import org.springframework.boot.loader.archive.Archive$EntryFilter;
import java.lang.Object;
import org.springframework.boot.loader.PropertiesLauncher$1;
import org.springframework.boot.loader.archive.Archive$Entry;
import java.lang.String;

final class PropertiesLauncher$ArchiveEntryFilter implements Archive$EntryFilter	// class@000535 from classes.dex
{
    private static final String DOT_JAR = ".jar";
    private static final String DOT_ZIP = ".zip";

    private void PropertiesLauncher$ArchiveEntryFilter(){
       super();
    }
    void PropertiesLauncher$ArchiveEntryFilter(PropertiesLauncher$1 x0){
       super();
    }
    public boolean matches(Archive$Entry entry){
       boolean b = (!entry.getName().endsWith(".jar") && !entry.getName().endsWith(".zip"))? false: true;
       return b;
    }
}
