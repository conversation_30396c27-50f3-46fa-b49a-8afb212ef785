package com.taikang.fly.check.dto.threeOrder.ThreeOrderAllDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ThreeOrderAllDto implements Serializable	// class@0001bd from classes.dex
{
    private String hospitalProjectC;
    private String hospitalProjectCode;
    private String hospitalProjectN;
    private String hospitalProjectName;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void ThreeOrderAllDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ThreeOrderAllDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ThreeOrderAllDto) {
             b = false;
          }else {
             ThreeOrderAllDto threeOrderAl = o;
             if (!threeOrderAl.canEqual(this)) {
                b = false;
             }else {
                String hospitalProj = this.getHospitalProjectName();
                String hospitalProj1 = threeOrderAl.getHospitalProjectName();
                if (hospitalProj == null) {
                   if (hospitalProj1 != null) {
                      b = false;
                   }
                }else if(hospitalProj.equals(hospitalProj1)){
                }
                String hospitalProj2 = this.getHospitalProjectCode();
                String hospitalProj3 = threeOrderAl.getHospitalProjectCode();
                if (hospitalProj2 == null) {
                   if (hospitalProj3 != null) {
                      b = false;
                   }
                }else if(hospitalProj2.equals(hospitalProj3)){
                }
                String hospitalProj4 = this.getHospitalProjectC();
                String hospitalProj5 = threeOrderAl.getHospitalProjectC();
                if (hospitalProj4 == null) {
                   if (hospitalProj5 != null) {
                      b = false;
                   }
                }else if(hospitalProj4.equals(hospitalProj5)){
                }
                String hospitalProj6 = this.getHospitalProjectN();
                String hospitalProj7 = threeOrderAl.getHospitalProjectN();
                if (hospitalProj6 == null) {
                   if (hospitalProj7 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!hospitalProj6.equals(hospitalProj7)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getHospitalProjectC(){
       return this.hospitalProjectC;
    }
    public String getHospitalProjectCode(){
       return this.hospitalProjectCode;
    }
    public String getHospitalProjectN(){
       return this.hospitalProjectN;
    }
    public String getHospitalProjectName(){
       return this.hospitalProjectName;
    }
    public int hashCode(){
       String $hospitalProjectName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($hospitalProjectName = this.getHospitalProjectName()) == null)? i: $hospitalProjectName.hashCode();
       result = i1 + 59;
       String $hospitalProjectCode = this.getHospitalProjectCode();
       int i2 = result * 59;
       i1 = ($hospitalProjectCode == null)? i: $hospitalProjectCode.hashCode();
       result = i2 + i1;
       String $hospitalProjectC = this.getHospitalProjectC();
       i2 = result * 59;
       i1 = ($hospitalProjectC == null)? i: $hospitalProjectC.hashCode();
       result = i2 + i1;
       String $hospitalProjectN = this.getHospitalProjectN();
       i1 = result * 59;
       if ($hospitalProjectN != null) {
          i = $hospitalProjectN.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setHospitalProjectC(String hospitalProjectC){
       this.hospitalProjectC = hospitalProjectC;
    }
    public void setHospitalProjectCode(String hospitalProjectCode){
       this.hospitalProjectCode = hospitalProjectCode;
    }
    public void setHospitalProjectN(String hospitalProjectN){
       this.hospitalProjectN = hospitalProjectN;
    }
    public void setHospitalProjectName(String hospitalProjectName){
       this.hospitalProjectName = hospitalProjectName;
    }
    public String toString(){
       return "ThreeOrderAllDto\(hospitalProjectName="+this.getHospitalProjectName()+", hospitalProjectCode="+this.getHospitalProjectCode()+", hospitalProjectC="+this.getHospitalProjectC()+", hospitalProjectN="+this.getHospitalProjectN()+"\)";
    }
}
