package com.taikang.fly.check.config.LicenseCheckConfig;
import java.lang.Object;
import com.taikang.license.verifyer.VerifyLicense;
import com.taikang.license.verifyer.VerifyLicense$LicenseVerifyerBuilder;

public class LicenseCheckConfig	// class@00008d from classes.dex
{

    public void LicenseCheckConfig(){
       super();
    }
    public VerifyLicense verifyLicense(){
       return new VerifyLicense$LicenseVerifyerBuilder().useDefaultCheckHandler().build();
    }
}
