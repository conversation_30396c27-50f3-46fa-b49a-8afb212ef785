package com.taikang.fly.check.dto.flyRule.ArgsDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ArgsDto implements Serializable	// class@0000f4 from classes.dex
{
    private String include;
    private String paramName;
    private String paramPs;
    private String paramType;
    private String paramValue;
    private String placeholder;
    private String sign;
    private static final long serialVersionUID = 0x1;

    public void ArgsDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ArgsDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ArgsDto){
          b = false;
       }else {
          ArgsDto uArgsDto = o;
          if (!uArgsDto.canEqual(this)) {
             b = false;
          }else {
             String paramName = this.getParamName();
             String paramName1 = uArgsDto.getParamName();
             if (paramName == null) {
                if (paramName1 != null) {
                   b = false;
                }
             }else if(paramName.equals(paramName1)){
             }
             String paramType = this.getParamType();
             String paramType1 = uArgsDto.getParamType();
             if (paramType == null) {
                if (paramType1 != null) {
                   b = false;
                }
             }else if(paramType.equals(paramType1)){
             }
             String paramValue = this.getParamValue();
             String paramValue1 = uArgsDto.getParamValue();
             if (paramValue == null) {
                if (paramValue1 != null) {
                   b = false;
                }
             }else if(paramValue.equals(paramValue1)){
             }
             String paramPs = this.getParamPs();
             String paramPs1 = uArgsDto.getParamPs();
             if (paramPs == null) {
                if (paramPs1 != null) {
                   b = false;
                }
             }else if(paramPs.equals(paramPs1)){
             }
             String sign = this.getSign();
             String sign1 = uArgsDto.getSign();
             if (sign == null) {
                if (sign1 != null) {
                   b = false;
                }
             }else if(sign.equals(sign1)){
             }
             String include = this.getInclude();
             String include1 = uArgsDto.getInclude();
             if (include == null) {
                if (include1 != null) {
                label_009a :
                   b = false;
                }
             }else if(include.equals(include1)){
             }
             String placeholder = this.getPlaceholder();
             String placeholder1 = uArgsDto.getPlaceholder();
             if (placeholder == null) {
                if (placeholder1 != null) {
                   b = false;
                }
             }else if(placeholder.equals(placeholder1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getInclude(){
       return this.include;
    }
    public String getParamName(){
       return this.paramName;
    }
    public String getParamPs(){
       return this.paramPs;
    }
    public String getParamType(){
       return this.paramType;
    }
    public String getParamValue(){
       return this.paramValue;
    }
    public String getPlaceholder(){
       return this.placeholder;
    }
    public String getSign(){
       return this.sign;
    }
    public int hashCode(){
       String $paramName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($paramName = this.getParamName()) == null)? i: $paramName.hashCode();
       result = i1 + 59;
       String $paramType = this.getParamType();
       int i2 = result * 59;
       i1 = ($paramType == null)? i: $paramType.hashCode();
       result = i2 + i1;
       String $paramValue = this.getParamValue();
       i2 = result * 59;
       i1 = ($paramValue == null)? i: $paramValue.hashCode();
       result = i2 + i1;
       String $paramPs = this.getParamPs();
       i2 = result * 59;
       i1 = ($paramPs == null)? i: $paramPs.hashCode();
       result = i2 + i1;
       String $sign = this.getSign();
       i2 = result * 59;
       i1 = ($sign == null)? i: $sign.hashCode();
       result = i2 + i1;
       String include = this.getInclude();
       i2 = result * 59;
       i1 = (include == null)? i: include.hashCode();
       String placeholder = this.getPlaceholder();
       i1 = (i2 + i1) * 59;
       if (placeholder != null) {
          i = placeholder.hashCode();
       }
       return (i1 + i);
    }
    public void setInclude(String include){
       this.include = include;
    }
    public void setParamName(String paramName){
       this.paramName = paramName;
    }
    public void setParamPs(String paramPs){
       this.paramPs = paramPs;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public void setParamValue(String paramValue){
       this.paramValue = paramValue;
    }
    public void setPlaceholder(String placeholder){
       this.placeholder = placeholder;
    }
    public void setSign(String sign){
       this.sign = sign;
    }
    public String toString(){
       return "ArgsDto\(paramName="+this.getParamName()+", paramType="+this.getParamType()+", paramValue="+this.getParamValue()+", paramPs="+this.getParamPs()+", sign="+this.getSign()+", include="+this.getInclude()+", placeholder="+this.getPlaceholder()+"\)";
    }
}
