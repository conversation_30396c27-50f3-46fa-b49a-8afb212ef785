package com.taikang.fly.check.dto.hospinfo.HospInfoSearchDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class HospInfoSearchDto	// class@000128 from classes.dex
{
    private String creator;
    private String hospName;
    private String oraUserName;
    private String region;

    public void HospInfoSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof HospInfoSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof HospInfoSearchDto) {
             b = false;
          }else {
             HospInfoSearchDto hospInfoSear = o;
             if (!hospInfoSear.canEqual(this)) {
                b = false;
             }else {
                String oraUserName = this.getOraUserName();
                String oraUserName1 = hospInfoSear.getOraUserName();
                if (oraUserName == null) {
                   if (oraUserName1 != null) {
                      b = false;
                   }
                }else if(oraUserName.equals(oraUserName1)){
                }
                String hospName = this.getHospName();
                String hospName1 = hospInfoSear.getHospName();
                if (hospName == null) {
                   if (hospName1 != null) {
                      b = false;
                   }
                }else if(hospName.equals(hospName1)){
                }
                String creator = this.getCreator();
                String creator1 = hospInfoSear.getCreator();
                if (creator == null) {
                   if (creator1 != null) {
                      b = false;
                   }
                }else if(creator.equals(creator1)){
                }
                String region = this.getRegion();
                String region1 = hospInfoSear.getRegion();
                if (region == null) {
                   if (region1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!region.equals(region1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCreator(){
       return this.creator;
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public String getRegion(){
       return this.region;
    }
    public int hashCode(){
       String $oraUserName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oraUserName = this.getOraUserName()) == null)? i: $oraUserName.hashCode();
       result = i1 + 59;
       String $hospName = this.getHospName();
       int i2 = result * 59;
       i1 = ($hospName == null)? i: $hospName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i1 = result * 59;
       if ($region != null) {
          i = $region.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public String toString(){
       return "HospInfoSearchDto\(oraUserName="+this.getOraUserName()+", hospName="+this.getHospName()+", creator="+this.getCreator()+", region="+this.getRegion()+"\)";
    }
}
