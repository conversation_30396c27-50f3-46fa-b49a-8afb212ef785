package com.taikang.fly.check.dto.system.role.RoleSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleSearchDto implements Serializable	// class@0001b8 from classes.dex
{
    private String name;
    private String roleCode;
    private static final long serialVersionUID = 0xfe5b5f7871749224;

    public void RoleSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RoleSearchDto) {
             b = false;
          }else {
             RoleSearchDto roleSearchDt = o;
             if (!roleSearchDt.canEqual(this)) {
                b = false;
             }else {
                String roleCode = this.getRoleCode();
                String roleCode1 = roleSearchDt.getRoleCode();
                if (roleCode == null) {
                   if (roleCode1 != null) {
                      b = false;
                   }
                }else if(roleCode.equals(roleCode1)){
                }
                String name = this.getName();
                String name1 = roleSearchDt.getName();
                if (name == null) {
                   if (name1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!name.equals(name1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public int hashCode(){
       String $roleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($roleCode = this.getRoleCode()) == null)? i: $roleCode.hashCode();
       result = i1 + 59;
       String $name = this.getName();
       i1 = result * 59;
       if ($name != null) {
          i = $name.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public String toString(){
       return "RoleSearchDto\(roleCode="+this.getRoleCode()+", name="+this.getName()+"\)";
    }
}
