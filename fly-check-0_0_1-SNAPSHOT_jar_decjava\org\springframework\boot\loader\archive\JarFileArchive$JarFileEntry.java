package org.springframework.boot.loader.archive.JarFileArchive$JarFileEntry;
import org.springframework.boot.loader.archive.Archive$Entry;
import java.util.jar.JarEntry;
import java.lang.Object;
import java.lang.String;

class JarFileArchive$JarFileEntry implements Archive$Entry	// class@000542 from classes.dex
{
    private final JarEntry jarEntry;

    void JarFileArchive$JarFileEntry(JarEntry jarEntry){
       super();
       this.jarEntry = jarEntry;
    }
    public JarEntry getJarEntry(){
       return this.jarEntry;
    }
    public String getName(){
       return this.jarEntry.getName();
    }
    public boolean isDirectory(){
       return this.jarEntry.isDirectory();
    }
}
