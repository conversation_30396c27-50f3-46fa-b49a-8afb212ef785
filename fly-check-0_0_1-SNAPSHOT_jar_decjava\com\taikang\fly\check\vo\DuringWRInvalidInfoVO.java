package com.taikang.fly.check.vo.DuringWRInvalidInfoVO;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DuringWRInvalidInfoVO	// class@000358 from classes.dex
{
    private String fail;
    private int number;
    private String rowNum;
    private String sheetName;

    public void DuringWRInvalidInfoVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DuringWRInvalidInfoVO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DuringWRInvalidInfoVO) {
             b = false;
          }else {
             DuringWRInvalidInfoVO uDuringWRInv = o;
             if (!uDuringWRInv.canEqual(this)) {
                b = false;
             }else {
                String sheetName = this.getSheetName();
                String sheetName1 = uDuringWRInv.getSheetName();
                if (sheetName == null) {
                   if (sheetName1 != null) {
                      b = false;
                   }
                }else if(sheetName.equals(sheetName1)){
                }
                String rowNum = this.getRowNum();
                String rowNum1 = uDuringWRInv.getRowNum();
                if (rowNum == null) {
                   if (rowNum1 != null) {
                      b = false;
                   }
                }else if(rowNum.equals(rowNum1)){
                }
                if (this.getNumber() != uDuringWRInv.getNumber()) {
                   b = false;
                }else {
                   String fail = this.getFail();
                   String fail1 = uDuringWRInv.getFail();
                   if (fail == null) {
                      if (fail1 == null) {
                      label_0004 :
                         return b;
                      }
                   }else if(!fail.equals(fail1)){
                   }
                   b = false;
                   goto label_0004 ;
                }
             }
          }
       }
    }
    public String getFail(){
       return this.fail;
    }
    public int getNumber(){
       return this.number;
    }
    public String getRowNum(){
       return this.rowNum;
    }
    public String getSheetName(){
       return this.sheetName;
    }
    public int hashCode(){
       String $sheetName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($sheetName = this.getSheetName()) == null)? i: $sheetName.hashCode();
       result = i1 + 59;
       String $rowNum = this.getRowNum();
       int i2 = result * 59;
       i1 = ($rowNum == null)? i: $rowNum.hashCode();
       result = i2 + i1;
       result = (result * 59) + this.getNumber();
       String $fail = this.getFail();
       i1 = result * 59;
       if ($fail != null) {
          i = $fail.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFail(String fail){
       this.fail = fail;
    }
    public void setNumber(int number){
       this.number = number;
    }
    public void setRowNum(String rowNum){
       this.rowNum = rowNum;
    }
    public void setSheetName(String sheetName){
       this.sheetName = sheetName;
    }
    public String toString(){
       return "DuringWRInvalidInfoVO\(sheetName="+this.getSheetName()+", rowNum="+this.getRowNum()+", number="+this.getNumber()+", fail="+this.getFail()+"\)";
    }
}
