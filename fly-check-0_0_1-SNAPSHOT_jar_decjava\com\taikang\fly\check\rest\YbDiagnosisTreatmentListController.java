package com.taikang.fly.check.rest.YbDiagnosisTreatmentListController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybDiagnosisTreatmentList.YbDiagnosisTreatmentListSearchDto;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.YbDiagnosisTreatmentListService;

public class YbDiagnosisTreatmentListController	// class@0002bb from classes.dex
{
    private YbDiagnosisTreatmentListService ybDiagnosisTreatmentListService;
    private static final Logger log;

    static {
       YbDiagnosisTreatmentListController.log = LoggerFactory.getLogger(YbDiagnosisTreatmentListController.class);
    }
    public void YbDiagnosisTreatmentListController(){
       super();
    }
    public CommResponse queryYbDiagnosisTreatmentList(Integer page,Integer size,YbDiagnosisTreatmentListSearchDto ybDiagnosisTreatmentListSearchDto){
       return CommResponse.success(this.ybDiagnosisTreatmentListService.queryList(page, size, ybDiagnosisTreatmentListSearchDto));
    }
}
