package com.taikang.fly.check.service.FieldTypeRemarksServiceImpl;
import com.taikang.fly.check.service.FieldTypeRemarksService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.FieldTypeRemarksMapper;

public class FieldTypeRemarksServiceImpl extends ServiceImpl implements FieldTypeRemarksService	// class@0002da from classes.dex
{
    private FieldTypeRemarksMapper fieldTypeRemarksMapper;

    public void FieldTypeRemarksServiceImpl(){
       super();
    }
    public List queryInfo(String dataType){
       return this.fieldTypeRemarksMapper.queryInfo(dataType);
    }
    public List queryTypeInfo(){
       return this.fieldTypeRemarksMapper.queryTypeInfo();
    }
}
