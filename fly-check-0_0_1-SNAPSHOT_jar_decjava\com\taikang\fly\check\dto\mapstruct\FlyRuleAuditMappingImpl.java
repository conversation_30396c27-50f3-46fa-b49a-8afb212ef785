package com.taikang.fly.check.dto.mapstruct.FlyRuleAuditMappingImpl;
import com.taikang.fly.check.dto.mapstruct.FlyRuleAuditMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditRespDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleAudit;
import java.lang.String;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class FlyRuleAuditMappingImpl implements FlyRuleAuditMapping	// class@000151 from classes.dex
{

    public void FlyRuleAuditMappingImpl(){
       super();
    }
    public FlyRuleAudit flyRuleAuditDtoToFlyRuleAudit(FlyRuleAuditRespDto flyRuleAuditDto){
       FlyRuleAudit uFlyRuleAudi;
       if (flyRuleAuditDto == null) {
          uFlyRuleAudi = null;
       }else {
          uFlyRuleAudi = new FlyRuleAudit();
          uFlyRuleAudi.setId(flyRuleAuditDto.getId());
          uFlyRuleAudi.setRuleName(flyRuleAuditDto.getRuleName());
          uFlyRuleAudi.setRegion(flyRuleAuditDto.getRegion());
          uFlyRuleAudi.setRuleLevel(flyRuleAuditDto.getRuleLevel());
          uFlyRuleAudi.setDiagnosisType(flyRuleAuditDto.getDiagnosisType());
          uFlyRuleAudi.setRuleScopeApply(flyRuleAuditDto.getRuleScopeApply());
          uFlyRuleAudi.setAuditState(flyRuleAuditDto.getAuditState());
          uFlyRuleAudi.setAuditRejectReason(flyRuleAuditDto.getAuditRejectReason());
       }
       return uFlyRuleAudi;
    }
    public List ruleAuditDtoListToRuleAuditList(List flyRuleAuditDtoList){
       List list;
       if (flyRuleAuditDtoList == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleAuditDtoList.size());
          Iterator iterator = flyRuleAuditDtoList.iterator();
          while (iterator.hasNext()) {
             list.add(this.flyRuleAuditDtoToFlyRuleAudit(iterator.next()));
          }
       }
       return list;
    }
}
