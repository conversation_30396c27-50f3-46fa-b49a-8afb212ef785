"""
命令行工具
"""

import typer
import uvicorn
from rich.console import Console
from rich.table import Table

from flycheck.config import get_settings
from flycheck.database import init_db, db_manager

app = typer.Typer(help="FlyCheck Python 命令行工具")
console = Console()
settings = get_settings()


@app.command()
def serve(
    host: str = typer.Option("0.0.0.0", help="服务器地址"),
    port: int = typer.Option(8000, help="服务器端口"),
    reload: bool = typer.Option(False, help="开启热重载"),
    workers: int = typer.Option(1, help="工作进程数")
):
    """启动Web服务器"""
    console.print(f"🚁 启动 FlyCheck Python 服务器", style="bold green")
    console.print(f"地址: http://{host}:{port}")
    console.print(f"API文档: http://{host}:{port}/docs")
    
    uvicorn.run(
        "flycheck.main:app",
        host=host,
        port=port,
        reload=reload,
        workers=workers if not reload else 1,
        log_level=settings.log_level.lower()
    )


@app.group()
def db():
    """数据库管理命令"""
    pass


@db.command("init")
def db_init():
    """初始化数据库"""
    console.print("🗄️ 初始化数据库...", style="bold blue")
    try:
        init_db()
        console.print("✅ 数据库初始化成功", style="bold green")
    except Exception as e:
        console.print(f"❌ 数据库初始化失败: {e}", style="bold red")
        raise typer.Exit(1)


@db.command("reset")
def db_reset():
    """重置数据库"""
    confirm = typer.confirm("⚠️ 这将删除所有数据，确定要继续吗？")
    if not confirm:
        console.print("操作已取消", style="yellow")
        return
    
    console.print("🗄️ 重置数据库...", style="bold blue")
    try:
        db_manager.drop_tables()
        db_manager.create_tables()
        console.print("✅ 数据库重置成功", style="bold green")
    except Exception as e:
        console.print(f"❌ 数据库重置失败: {e}", style="bold red")
        raise typer.Exit(1)


@db.command("seed")
def db_seed():
    """创建示例数据"""
    console.print("🌱 创建示例数据...", style="bold blue")
    try:
        from flycheck.utils.seed_data import create_seed_data
        create_seed_data()
        console.print("✅ 示例数据创建成功", style="bold green")
    except Exception as e:
        console.print(f"❌ 示例数据创建失败: {e}", style="bold red")
        raise typer.Exit(1)


@app.command()
def info():
    """显示系统信息"""
    table = Table(title="FlyCheck Python 系统信息")
    table.add_column("配置项", style="cyan")
    table.add_column("值", style="green")
    
    table.add_row("应用名称", settings.app_name)
    table.add_row("版本", settings.app_version)
    table.add_row("数据库URL", settings.database_url)
    table.add_row("上传目录", settings.upload_dir)
    table.add_row("日志级别", settings.log_level)
    table.add_row("调试模式", str(settings.debug))
    
    console.print(table)


@app.group()
def rule():
    """规则管理命令"""
    pass


@rule.command("list")
def rule_list():
    """列出所有规则"""
    console.print("📋 规则列表", style="bold blue")
    
    try:
        from flycheck.database import get_db
        from flycheck.models import FlyRule
        
        db = next(get_db())
        rules = db.query(FlyRule).filter(FlyRule.is_deleted == "0").all()
        
        if not rules:
            console.print("没有找到规则", style="yellow")
            return
        
        table = Table()
        table.add_column("ID", style="cyan")
        table.add_column("规则名称", style="green")
        table.add_column("类型", style="blue")
        table.add_column("状态", style="yellow")
        table.add_column("创建时间", style="magenta")
        
        for rule in rules:
            table.add_row(
                rule.id[:8] + "...",
                rule.rule_name,
                rule.rule_type or "sql",
                "启用" if rule.state == "1" else "禁用",
                rule.created_time.strftime("%Y-%m-%d %H:%M:%S")
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"❌ 获取规则列表失败: {e}", style="bold red")


@rule.command("validate")
def rule_validate(rule_id: str):
    """验证规则"""
    console.print(f"🔍 验证规则: {rule_id}", style="bold blue")
    
    try:
        from flycheck.database import get_db
        from flycheck.models import FlyRule
        from flycheck.core.rule_engine import RuleEngine
        
        db = next(get_db())
        rule = db.query(FlyRule).filter(FlyRule.id == rule_id).first()
        
        if not rule:
            console.print("❌ 规则不存在", style="bold red")
            return
        
        rule_engine = RuleEngine(db)
        is_valid, message = rule_engine.validate_rule(rule)
        
        if is_valid:
            console.print("✅ 规则验证通过", style="bold green")
        else:
            console.print(f"❌ 规则验证失败: {message}", style="bold red")
        
    except Exception as e:
        console.print(f"❌ 规则验证失败: {e}", style="bold red")


if __name__ == "__main__":
    app()
