package com.taikang.fly.check.dto.TableSpacesReductionDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TableSpacesReductionDto implements Serializable	// class@0000b4 from classes.dex
{
    private String currentSize;
    private String fileId;
    private String name;
    private String resizeTo;
    private String tablespaceName;
    private static final long serialVersionUID = 0x1;

    public void TableSpacesReductionDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TableSpacesReductionDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof TableSpacesReductionDto) {
             b = false;
          }else {
             TableSpacesReductionDto tableSpacesR = o;
             if (!tableSpacesR.canEqual(this)) {
                b = false;
             }else {
                String fileId = this.getFileId();
                String fileId1 = tableSpacesR.getFileId();
                if (fileId == null) {
                   if (fileId1 != null) {
                      b = false;
                   }
                }else if(fileId.equals(fileId1)){
                }
                String tablespaceNa = this.getTablespaceName();
                String tablespaceNa1 = tableSpacesR.getTablespaceName();
                if (tablespaceNa == null) {
                   if (tablespaceNa1 != null) {
                      b = false;
                   }
                }else if(tablespaceNa.equals(tablespaceNa1)){
                }
                String name = this.getName();
                String name1 = tableSpacesR.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String currentSize = this.getCurrentSize();
                String currentSize1 = tableSpacesR.getCurrentSize();
                if (currentSize == null) {
                   if (currentSize1 != null) {
                      b = false;
                   }
                }else if(currentSize.equals(currentSize1)){
                }
                String resizeTo = this.getResizeTo();
                String resizeTo1 = tableSpacesR.getResizeTo();
                if (resizeTo == null) {
                   if (resizeTo1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!resizeTo.equals(resizeTo1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCurrentSize(){
       return this.currentSize;
    }
    public String getFileId(){
       return this.fileId;
    }
    public String getName(){
       return this.name;
    }
    public String getResizeTo(){
       return this.resizeTo;
    }
    public String getTablespaceName(){
       return this.tablespaceName;
    }
    public int hashCode(){
       String $fileId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($fileId = this.getFileId()) == null)? i: $fileId.hashCode();
       result = i1 + 59;
       String $tablespaceName = this.getTablespaceName();
       int i2 = result * 59;
       i1 = ($tablespaceName == null)? i: $tablespaceName.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $currentSize = this.getCurrentSize();
       i2 = result * 59;
       i1 = ($currentSize == null)? i: $currentSize.hashCode();
       result = i2 + i1;
       String $resizeTo = this.getResizeTo();
       i1 = result * 59;
       if ($resizeTo != null) {
          i = $resizeTo.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCurrentSize(String currentSize){
       this.currentSize = currentSize;
    }
    public void setFileId(String fileId){
       this.fileId = fileId;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setResizeTo(String resizeTo){
       this.resizeTo = resizeTo;
    }
    public void setTablespaceName(String tablespaceName){
       this.tablespaceName = tablespaceName;
    }
    public String toString(){
       return "TableSpacesReductionDto\(fileId="+this.getFileId()+", tablespaceName="+this.getTablespaceName()+", name="+this.getName()+", currentSize="+this.getCurrentSize()+", resizeTo="+this.getResizeTo()+"\)";
    }
}
