package com.taikang.fly.check.dto.system.role.RoleEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleEditDto implements Serializable	// class@0001b4 from classes.dex
{
    private String id;
    private String name;
    private static final long serialVersionUID = 0x2f63eed37d87000;

    public void RoleEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleEditDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RoleEditDto) {
             b = false;
          }else {
             RoleEditDto roleEditDto = o;
             if (!roleEditDto.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = roleEditDto.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String name = this.getName();
                String name1 = roleEditDto.getName();
                if (name == null) {
                   if (name1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!name.equals(name1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getName(){
       return this.name;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $name = this.getName();
       i1 = result * 59;
       if ($name != null) {
          i = $name.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setName(String name){
       this.name = name;
    }
    public String toString(){
       return "RoleEditDto\(id="+this.getId()+", name="+this.getName()+"\)";
    }
}
