package org.springframework.boot.loader.data.RandomAccessDataFile$DataInputStream;
import java.io.InputStream;
import org.springframework.boot.loader.data.RandomAccessDataFile;
import org.springframework.boot.loader.data.RandomAccessDataFile$1;
import java.lang.Math;
import java.lang.NullPointerException;
import java.lang.String;

class RandomAccessDataFile$DataInputStream extends InputStream	// class@000546 from classes.dex
{
    private int position;
    final RandomAccessDataFile this$0;

    private void RandomAccessDataFile$DataInputStream(RandomAccessDataFile p0){
       this.this$0 = p0;
       super();
    }
    void RandomAccessDataFile$DataInputStream(RandomAccessDataFile x0,RandomAccessDataFile$1 x1){
       super(x0);
    }
    private int cap(long n){
       return (int)Math.min((RandomAccessDataFile.access$800(this.this$0) - (long)this.position), n);
    }
    private long moveOn(int amount){
       this.position = this.position + amount;
       return (long)amount;
    }
    public int doRead(byte[] b,int off,int len){
       int i;
       int i1;
       if (!len) {
          i = 0;
       }else if((i1 = this.cap((long)len)) <= 0){
          i = -1;
       }else {
          i = (int)this.moveOn(RandomAccessDataFile.access$700(this.this$0, b, (long)this.position, off, i1));
       }
       return i;
    }
    public int read(){
       int read;
       if ((read = RandomAccessDataFile.access$600(this.this$0, (long)this.position)) > -1) {
          this.moveOn(1);
       }
       return read;
    }
    public int read(byte[] b){
       int len = (b != null)? b.length: 0;
       return this.read(b, 0, len);
    }
    public int read(byte[] b,int off,int len){
       if (b == null) {
          throw new NullPointerException("Bytes must not be null");
       }
       return this.doRead(b, off, len);
    }
    public long skip(long n){
       long l = 0;
       if ((n - l) > 0) {
          l = this.moveOn(this.cap(n));
       }
       return l;
    }
}
