package com.taikang.fly.check.vo.drg.DrgQueryFeeVarianceOfDepartmentVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgQueryFeeVarianceOfDepartmentVo	// class@000375 from classes.dex
{
    private String drgCode;
    private String tag;
    private String year;

    public void DrgQueryFeeVarianceOfDepartmentVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgQueryFeeVarianceOfDepartmentVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgQueryFeeVarianceOfDepartmentVo) {
             b = false;
          }else {
             DrgQueryFeeVarianceOfDepartmentVo uDrgQueryFee = o;
             if (!uDrgQueryFee.canEqual(this)) {
                b = false;
             }else {
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgQueryFee.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String year = this.getYear();
                String year1 = uDrgQueryFee.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String tag = this.getTag();
                String tag1 = uDrgQueryFee.getTag();
                if (tag == null) {
                   if (tag1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!tag.equals(tag1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getTag(){
       return this.tag;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $drgCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgCode = this.getDrgCode()) == null)? i: $drgCode.hashCode();
       result = i1 + 59;
       String $year = this.getYear();
       int i2 = result * 59;
       i1 = ($year == null)? i: $year.hashCode();
       result = i2 + i1;
       String $tag = this.getTag();
       i1 = result * 59;
       if ($tag != null) {
          i = $tag.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setTag(String tag){
       this.tag = tag;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DrgQueryFeeVarianceOfDepartmentVo\(drgCode="+this.getDrgCode()+", year="+this.getYear()+", tag="+this.getTag()+"\)";
    }
}
