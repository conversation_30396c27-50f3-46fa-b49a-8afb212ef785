package com.taikang.fly.check.rest.YbDrugListController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybdruglist.YbDrugListSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.YbDrugListService;

public class YbDrugListController	// class@0002be from classes.dex
{
    private YbDrugListService ybDrugListService;
    private static final Logger log;

    static {
       YbDrugListController.log = LoggerFactory.getLogger(YbDrugListController.class);
    }
    public void YbDrugListController(){
       super();
    }
    public RmpResponse queryByPid(Integer page,Integer size,YbDrugListSearchDto ybDrugListSearchDto){
       return RmpResponse.success(this.ybDrugListService.queryList(page, size, ybDrugListSearchDto));
    }
}
