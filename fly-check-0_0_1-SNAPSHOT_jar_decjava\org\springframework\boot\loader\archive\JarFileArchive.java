package org.springframework.boot.loader.archive.JarFileArchive;
import org.springframework.boot.loader.archive.Archive;
import java.io.File;
import java.net.URL;
import org.springframework.boot.loader.jar.JarFile;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;
import java.util.UUID;
import java.lang.IllegalStateException;
import java.lang.System;
import java.util.jar.JarEntry;
import java.net.URI;
import java.util.zip.ZipEntry;
import java.io.InputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.Throwable;
import java.util.jar.Manifest;
import org.springframework.boot.loader.archive.Archive$Entry;
import org.springframework.boot.loader.archive.JarFileArchive$JarFileEntry;
import org.springframework.boot.loader.archive.Archive$EntryFilter;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Collections;
import org.springframework.boot.loader.archive.JarFileArchive$EntryIterator;
import java.util.Enumeration;

public class JarFileArchive implements Archive	// class@000543 from classes.dex
{
    private final JarFile jarFile;
    private File tempUnpackFolder;
    private URL url;
    private static final int BUFFER_SIZE = 32768;
    private static final String UNPACK_MARKER = "UNPACK:";

    public void JarFileArchive(File file){
       super(file, null);
    }
    public void JarFileArchive(File file,URL url){
       super(new JarFile(file));
       this.url = url;
    }
    public void JarFileArchive(JarFile jarFile){
       super();
       this.jarFile = jarFile;
    }
    private File createUnpackFolder(File parent){
       File unpackFolder;
       int attempts = 0;
       attempts = attempts;
       while (true) {
          attempts = attempts + 1;
          if (attempts >= 1000) {
             throw new IllegalStateException("Failed to create unpack folder in directory \'"+parent+"\'");
          }
          String fileName = new File(this.jarFile.getName()).getName();
          unpackFolder = new File(parent, "".append(fileName).append("-spring-boot-libs-").append(UUID.randomUUID()).toString());
          if (unpackFolder.mkdirs()) {
             break ;
          }else {
             attempts = attempts;
          }
       }
       return unpackFolder;
    }
    private File getTempUnpackFolder(){
       if (this.tempUnpackFolder == null) {
          File tempFolder = new File(System.getProperty("java.io.tmpdir"));
          this.tempUnpackFolder = this.createUnpackFolder(tempFolder);
       }
       return this.tempUnpackFolder;
    }
    private Archive getUnpackedNestedArchive(JarEntry jarEntry){
       int i = 47;
       String name = jarEntry.getName();
       if (name.lastIndexOf(i) != -1) {
          name = name.substring((name.lastIndexOf(i) + 1));
       }
       File uFile = new File(this.getTempUnpackFolder(), name);
       if (!uFile.exists() || (uFile.length() - jarEntry.getSize())) {
          this.unpack(jarEntry, uFile);
       }
       return new JarFileArchive(uFile, uFile.toURI().toURL());
    }
    private void unpack(JarEntry entry,File file){
       int bytesRead;
       InputStream inputStream = this.jarFile.getInputStream(entry);
       try{
          OutputStream outputStream = new FileOutputStream(file);
          int i = 0x8000;
          byte[] buffer = new byte[i];
          while ((bytesRead = inputStream.read(buffer)) != -1) {
             outputStream.write(buffer, 0, bytesRead);
          }
          try{
             outputStream.flush();
             if (outputStream != null) {
                if (0) {
                   outputStream.close();
                }else {
                   outputStream.close();
                }
             }
          }catch(java.lang.Throwable e5){
             0.addSuppressed(e5);
          }
          try{
             if (inputStream != null) {
                if (0) {
                   inputStream.close();
                }else {
                   inputStream.close();
                }
             }
          }catch(java.lang.Throwable e4){
             0.addSuppressed(e4);
          }
          return;
       }catch(java.lang.Throwable e4){
          throw e4;
       }catch(java.lang.Throwable e4){
          throw e4;
       }
    }
    public Manifest getManifest(){
       return this.jarFile.getManifest();
    }
    protected Archive getNestedArchive(Archive$Entry entry){
       Archive unpackedNest;
       JarEntry jarEntry = entry.getJarEntry();
       if (jarEntry.getComment().startsWith("UNPACK:")) {
          unpackedNest = this.getUnpackedNestedArchive(jarEntry);
       }else {
          try{
             unpackedNest = new JarFileArchive(this.jarFile.getNestedJarFile(jarEntry));
          }catch(java.lang.Exception e0){
             throw new IllegalStateException("Failed to get nested archive for entry "+entry.getName(), e0);
          }
       }
       return unpackedNest;
    }
    public List getNestedArchives(Archive$EntryFilter filter){
       List nestedArchives = new ArrayList();
       Iterator iterator = this.iterator();
       while (iterator.hasNext()) {
          Archive$Entry uEntry = iterator.next();
          if (filter.matches(uEntry)) {
             nestedArchives.add(this.getNestedArchive(uEntry));
          }
       }
       return Collections.unmodifiableList(nestedArchives);
    }
    public URL getUrl(){
       JarFileArchive turl = (this.url != null)? this.url: this.jarFile.getUrl();
       return turl;
    }
    public Iterator iterator(){
       return new JarFileArchive$EntryIterator(this.jarFile.entries());
    }
    public String toString(){
       String str;
       try{
          str = this.getUrl().toString();
       }catch(java.lang.Exception e0){
          str = "jar archive";
       }
       return str;
    }
}
