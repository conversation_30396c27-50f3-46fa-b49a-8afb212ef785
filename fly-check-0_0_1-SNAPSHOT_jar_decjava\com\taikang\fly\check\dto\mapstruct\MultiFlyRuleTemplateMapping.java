package com.taikang.fly.check.dto.mapstruct.MultiFlyRuleTemplateMapping;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateAddDto;
import com.taikang.fly.check.mybatis.domain.MultiFlyRuleTemplate;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateRespDto;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateUpdateDto;
import java.util.List;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateEditDto;

public interface abstract MultiFlyRuleTemplateMapping	// class@00016d from classes.dex
{

    MultiFlyRuleTemplate addDtoToDomain(MultiFlyRuleTemplateAddDto p0);
    MultiFlyRuleTemplateRespDto domainToInfoDto(MultiFlyRuleTemplate p0);
    MultiFlyRuleTemplateUpdateDto domainToUpdateDto(MultiFlyRuleTemplate p0);
    MultiFlyRuleTemplateAddDto domainToaddDto(MultiFlyRuleTemplate p0);
    MultiFlyRuleTemplate dtoToDomain(MultiFlyRuleTemplateRespDto p0);
    List dtoToDomains(List p0);
    MultiFlyRuleTemplate editDtoToDomain(MultiFlyRuleTemplateEditDto p0,MultiFlyRuleTemplate p1);
    List entityToDtos(List p0);
    List entityToUpdateDtos(List p0);
    MultiFlyRuleTemplate updateDtoToDomain(MultiFlyRuleTemplateUpdateDto p0);
}
