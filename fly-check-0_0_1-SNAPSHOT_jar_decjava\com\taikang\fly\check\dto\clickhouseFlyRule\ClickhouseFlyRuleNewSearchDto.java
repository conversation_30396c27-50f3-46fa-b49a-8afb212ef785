package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleNewSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ClickhouseFlyRuleNewSearchDto implements Serializable	// class@0000c2 from classes.dex
{
    private String region;
    private String state;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseFlyRuleNewSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseFlyRuleNewSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ClickhouseFlyRuleNewSearchDto) {
             b = false;
          }else {
             ClickhouseFlyRuleNewSearchDto uClickhouseF = o;
             if (!uClickhouseF.canEqual(this)) {
                b = false;
             }else {
                String region = this.getRegion();
                String region1 = uClickhouseF.getRegion();
                if (region == null) {
                   if (region1 != null) {
                      b = false;
                   }
                }else if(region.equals(region1)){
                }
                String state = this.getState();
                String state1 = uClickhouseF.getState();
                if (state == null) {
                   if (state1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!state.equals(state1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getRegion(){
       return this.region;
    }
    public String getState(){
       return this.state;
    }
    public int hashCode(){
       String $region;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($region = this.getRegion()) == null)? i: $region.hashCode();
       result = i1 + 59;
       String $state = this.getState();
       i1 = result * 59;
       if ($state != null) {
          i = $state.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setState(String state){
       this.state = state;
    }
    public String toString(){
       return "ClickhouseFlyRuleNewSearchDto\(region="+this.getRegion()+", state="+this.getState()+"\)";
    }
}
