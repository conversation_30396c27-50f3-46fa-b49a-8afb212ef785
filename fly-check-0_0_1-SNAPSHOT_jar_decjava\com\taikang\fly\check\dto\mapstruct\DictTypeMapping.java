package com.taikang.fly.check.dto.mapstruct.DictTypeMapping;
import com.taikang.fly.check.mybatis.domain.DictType;
import com.taikang.fly.check.dto.dictType.DictTypeDto;
import java.util.List;

public interface abstract DictTypeMapping	// class@00014c from classes.dex
{

    DictTypeDto toDto(DictType p0);
    List toDtoList(List p0);
    DictType toEntity(DictTypeDto p0);
    List toEntityList(List p0);
}
