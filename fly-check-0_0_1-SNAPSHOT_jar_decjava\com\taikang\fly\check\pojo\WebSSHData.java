package com.taikang.fly.check.pojo.WebSSHData;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;

public class WebSSHData	// class@00027f from classes.dex
{
    private String command;
    private String host;
    private String operate;
    private String password;
    private Integer port;
    private String username;

    public void WebSSHData(){
       super();
       this.port = Integer.valueOf(22);
       this.command = "";
    }
    public String getCommand(){
       return this.command;
    }
    public String getHost(){
       return this.host;
    }
    public String getOperate(){
       return this.operate;
    }
    public String getPassword(){
       return this.password;
    }
    public Integer getPort(){
       return this.port;
    }
    public String getUsername(){
       return this.username;
    }
    public void setCommand(String command){
       this.command = command;
    }
    public void setHost(String host){
       this.host = host;
    }
    public void setOperate(String operate){
       this.operate = operate;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setPort(Integer port){
       this.port = port;
    }
    public void setUsername(String username){
       this.username = username;
    }
}
