package com.taikang.fly.check.mybatis.dao.TemplateInfoMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.TemplateInfo;

public interface abstract TemplateInfoMapper implements BaseMapper	// class@000222 from classes.dex
{

    List findByIds(List p0);
    List getTableNameList();
    void insertByDeclare(TemplateInfo p0);
}
