package org.springframework.boot.loader.archive.JarFileArchive$EntryIterator;
import java.util.Iterator;
import java.util.Enumeration;
import java.lang.Object;
import org.springframework.boot.loader.archive.Archive$Entry;
import org.springframework.boot.loader.archive.JarFileArchive$JarFileEntry;
import java.util.jar.JarEntry;
import java.lang.UnsupportedOperationException;
import java.lang.String;

class JarFileArchive$EntryIterator implements Iterator	// class@000541 from classes.dex
{
    private final Enumeration enumeration;

    void JarFileArchive$EntryIterator(Enumeration enumeration){
       super();
       this.enumeration = enumeration;
    }
    public boolean hasNext(){
       return this.enumeration.hasMoreElements();
    }
    public Object next(){
       return this.next();
    }
    public Archive$Entry next(){
       return new JarFileArchive$JarFileEntry(this.enumeration.nextElement());
    }
    public void remove(){
       throw new UnsupportedOperationException("remove");
    }
}
