package com.taikang.fly.check.security.core.DomainContext;
import java.lang.Object;
import com.taikang.fly.check.dto.user.UserDto;
import java.lang.String;
import java.lang.StringBuilder;

public class DomainContext	// class@0002c3 from classes.dex
{
    private UserDto userInfo;

    public void DomainContext(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DomainContext;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DomainContext) {
             b = false;
          }else {
             DomainContext uDomainConte = o;
             if (!uDomainConte.canEqual(this)) {
                b = false;
             }else {
                UserDto userInfo = this.getUserInfo();
                UserDto userInfo1 = uDomainConte.getUserInfo();
                if (userInfo == null) {
                   if (userInfo1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!userInfo.equals(userInfo1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public UserDto getUserInfo(){
       return this.userInfo;
    }
    public int hashCode(){
       UserDto $userInfo;
       int PRIME = 59;
       int result = 1;
       int i = (($userInfo = this.getUserInfo()) == null)? 43: $userInfo.hashCode();
       result = i + 59;
       return result;
    }
    public void setUserInfo(UserDto userInfo){
       this.userInfo = userInfo;
    }
    public String toString(){
       return "DomainContext\(userInfo="+this.getUserInfo()+"\)";
    }
}
