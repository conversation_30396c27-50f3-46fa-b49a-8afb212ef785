package com.taikang.fly.check.dto.drg.DrgProfitAndLossDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgProfitAndLossDto implements Serializable	// class@0000ed from classes.dex
{
    private String drgType;
    private String name;
    private String oraUserName;
    private static final long serialVersionUID = 0x1;

    public void DrgProfitAndLossDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgProfitAndLossDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgProfitAndLossDto) {
             b = false;
          }else {
             DrgProfitAndLossDto uDrgProfitAn = o;
             if (!uDrgProfitAn.canEqual(this)) {
                b = false;
             }else {
                String oraUserName = this.getOraUserName();
                String oraUserName1 = uDrgProfitAn.getOraUserName();
                if (oraUserName == null) {
                   if (oraUserName1 != null) {
                      b = false;
                   }
                }else if(oraUserName.equals(oraUserName1)){
                }
                String drgType = this.getDrgType();
                String drgType1 = uDrgProfitAn.getDrgType();
                if (drgType == null) {
                   if (drgType1 != null) {
                      b = false;
                   }
                }else if(drgType.equals(drgType1)){
                }
                String name = this.getName();
                String name1 = uDrgProfitAn.getName();
                if (name == null) {
                   if (name1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!name.equals(name1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgType(){
       return this.drgType;
    }
    public String getName(){
       return this.name;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public int hashCode(){
       String $oraUserName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oraUserName = this.getOraUserName()) == null)? i: $oraUserName.hashCode();
       result = i1 + 59;
       String $drgType = this.getDrgType();
       int i2 = result * 59;
       i1 = ($drgType == null)? i: $drgType.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i1 = result * 59;
       if ($name != null) {
          i = $name.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgType(String drgType){
       this.drgType = drgType;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public String toString(){
       return "DrgProfitAndLossDto\(oraUserName="+this.getOraUserName()+", drgType="+this.getDrgType()+", name="+this.getName()+"\)";
    }
}
