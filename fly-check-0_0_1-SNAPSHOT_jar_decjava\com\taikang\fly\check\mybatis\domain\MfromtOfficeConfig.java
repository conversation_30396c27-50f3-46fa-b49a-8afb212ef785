package com.taikang.fly.check.mybatis.domain.MfromtOfficeConfig;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class MfromtOfficeConfig	// class@000253 from classes.dex
{
    private String businessKey;
    private String businessName;
    private String createdBy;
    private LocalDateTime createdTime;
    private String id;
    private String isValid;
    private String modifier;
    private LocalDateTime modifyTime;

    public void MfromtOfficeConfig(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MfromtOfficeConfig;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MfromtOfficeConfig){
          b = false;
       }else {
          MfromtOfficeConfig mfromtOffice = o;
          if (!mfromtOffice.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = mfromtOffice.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String businessKey = this.getBusinessKey();
             String businessKey1 = mfromtOffice.getBusinessKey();
             if (businessKey == null) {
                if (businessKey1 != null) {
                   b = false;
                }
             }else if(businessKey.equals(businessKey1)){
             }
             String businessName = this.getBusinessName();
             String businessName1 = mfromtOffice.getBusinessName();
             if (businessName == null) {
                if (businessName1 != null) {
                   b = false;
                }
             }else if(businessName.equals(businessName1)){
             }
             String createdBy = this.getCreatedBy();
             String createdBy1 = mfromtOffice.getCreatedBy();
             if (createdBy == null) {
                if (createdBy1 != null) {
                   b = false;
                }
             }else if(createdBy.equals(createdBy1)){
             }
             LocalDateTime createdTime = this.getCreatedTime();
             LocalDateTime createdTime1 = mfromtOffice.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modifier = this.getModifier();
             String modifier1 = mfromtOffice.getModifier();
             if (modifier == null) {
                if (modifier1 != null) {
                   b = false;
                }
             }else if(modifier.equals(modifier1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = mfromtOffice.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = mfromtOffice.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getBusinessKey(){
       return this.businessKey;
    }
    public String getBusinessName(){
       return this.businessName;
    }
    public String getCreatedBy(){
       return this.createdBy;
    }
    public LocalDateTime getCreatedTime(){
       return this.createdTime;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModifier(){
       return this.modifier;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $businessKey = this.getBusinessKey();
       int i2 = result * 59;
       i1 = ($businessKey == null)? i: $businessKey.hashCode();
       result = i2 + i1;
       String $businessName = this.getBusinessName();
       i2 = result * 59;
       i1 = ($businessName == null)? i: $businessName.hashCode();
       result = i2 + i1;
       String $createdBy = this.getCreatedBy();
       i2 = result * 59;
       i1 = ($createdBy == null)? i: $createdBy.hashCode();
       result = i2 + i1;
       LocalDateTime $createdTime = this.getCreatedTime();
       i2 = result * 59;
       i1 = ($createdTime == null)? i: $createdTime.hashCode();
       result = i2 + i1;
       String modifier = this.getModifier();
       i2 = result * 59;
       i1 = (modifier == null)? i: modifier.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String isValid = this.getIsValid();
       i1 = (i2 + i1) * 59;
       if (isValid != null) {
          i = isValid.hashCode();
       }
       return (i1 + i);
    }
    public MfromtOfficeConfig setBusinessKey(String businessKey){
       this.businessKey = businessKey;
       return this;
    }
    public MfromtOfficeConfig setBusinessName(String businessName){
       this.businessName = businessName;
       return this;
    }
    public MfromtOfficeConfig setCreatedBy(String createdBy){
       this.createdBy = createdBy;
       return this;
    }
    public MfromtOfficeConfig setCreatedTime(LocalDateTime createdTime){
       this.createdTime = createdTime;
       return this;
    }
    public MfromtOfficeConfig setId(String id){
       this.id = id;
       return this;
    }
    public MfromtOfficeConfig setIsValid(String isValid){
       this.isValid = isValid;
       return this;
    }
    public MfromtOfficeConfig setModifier(String modifier){
       this.modifier = modifier;
       return this;
    }
    public MfromtOfficeConfig setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public String toString(){
       return "MfromtOfficeConfig\(id="+this.getId()+", businessKey="+this.getBusinessKey()+", businessName="+this.getBusinessName()+", createdBy="+this.getCreatedBy()+", createdTime="+this.getCreatedTime()+", modifier="+this.getModifier()+", modifyTime="+this.getModifyTime()+", isValid="+this.getIsValid()+"\)";
    }
}
