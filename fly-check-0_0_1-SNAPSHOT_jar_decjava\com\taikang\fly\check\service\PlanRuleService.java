package com.taikang.fly.check.service.PlanRuleService;
import java.util.List;
import java.lang.Integer;
import com.taikang.fly.check.dto.planRule.PlanRuleRespNewDto;
import java.lang.String;
import com.taikang.fly.check.dto.flyRule.FlyRulePlanDto;
import com.taikang.fly.check.comm.NativePage;

public interface abstract PlanRuleService	// class@0002f5 from classes.dex
{

    Integer add(List p0);
    Integer addAll(PlanRuleRespNewDto p0);
    Integer addAllS(List p0);
    Integer deleteById(String p0);
    NativePage queryRuleInfoNew(Integer p0,Integer p1,FlyRulePlanDto p2);
}
