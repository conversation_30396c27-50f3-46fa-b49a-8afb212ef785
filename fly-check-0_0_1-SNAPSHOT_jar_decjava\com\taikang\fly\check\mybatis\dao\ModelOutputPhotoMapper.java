package com.taikang.fly.check.mybatis.dao.ModelOutputPhotoMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.ModelOutputPhoto;

public interface abstract ModelOutputPhotoMapper implements BaseMapper	// class@000209 from classes.dex
{

    List selectByWorkflowId(String p0);
    ModelOutputPhoto selectByWorkflowIdAndName(String p0,String p1);
}
