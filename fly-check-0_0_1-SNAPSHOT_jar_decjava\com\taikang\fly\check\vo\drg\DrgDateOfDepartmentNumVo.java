package com.taikang.fly.check.vo.drg.DrgDateOfDepartmentNumVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgDateOfDepartmentNumVo	// class@000362 from classes.dex
{
    private String adrgCode;
    private String drgCode;
    private String lastNum;
    private String lastProportionCases;
    private String lastYear;
    private String proportionCases;
    private String proportionCasesGrowthRate;
    private String totalNum;
    private String year;
    private String yearNum;

    public void DrgDateOfDepartmentNumVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgDateOfDepartmentNumVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgDateOfDepartmentNumVo){
          b = false;
       }else {
          DrgDateOfDepartmentNumVo uDrgDateOfDe = o;
          if (!uDrgDateOfDe.canEqual(this)) {
             b = false;
          }else {
             String adrgCode = this.getAdrgCode();
             String adrgCode1 = uDrgDateOfDe.getAdrgCode();
             if (adrgCode == null) {
                if (adrgCode1 != null) {
                   b = false;
                }
             }else if(adrgCode.equals(adrgCode1)){
             }
             String drgCode = this.getDrgCode();
             String drgCode1 = uDrgDateOfDe.getDrgCode();
             if (drgCode == null) {
                if (drgCode1 != null) {
                   b = false;
                }
             }else if(drgCode.equals(drgCode1)){
             }
             String year = this.getYear();
             String year1 = uDrgDateOfDe.getYear();
             if (year == null) {
                if (year1 != null) {
                   b = false;
                }
             }else if(year.equals(year1)){
             }
             String yearNum = this.getYearNum();
             String yearNum1 = uDrgDateOfDe.getYearNum();
             if (yearNum == null) {
                if (yearNum1 != null) {
                   b = false;
                }
             }else if(yearNum.equals(yearNum1)){
             }
             String proportionCa = this.getProportionCases();
             String proportionCa1 = uDrgDateOfDe.getProportionCases();
             if (proportionCa == null) {
                if (proportionCa1 != null) {
                   b = false;
                }
             }else if(proportionCa.equals(proportionCa1)){
             }
             String lastYear = this.getLastYear();
             String lastYear1 = uDrgDateOfDe.getLastYear();
             if (lastYear == null) {
                if (lastYear1 != null) {
                   b = false;
                }
             }else if(lastYear.equals(lastYear1)){
             }
             String lastNum = this.getLastNum();
             String lastNum1 = uDrgDateOfDe.getLastNum();
             if (lastNum == null) {
                if (lastNum1 != null) {
                label_00b7 :
                   b = false;
                }
             }else if(lastNum.equals(lastNum1)){
             }
             String lastProporti = this.getLastProportionCases();
             String lastProporti1 = uDrgDateOfDe.getLastProportionCases();
             if (lastProporti == null) {
                if (lastProporti1 != null) {
                   b = false;
                }
             }else if(lastProporti.equals(lastProporti1)){
             }
             String totalNum = this.getTotalNum();
             String totalNum1 = uDrgDateOfDe.getTotalNum();
             if (totalNum == null) {
                if (totalNum1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(totalNum.equals(totalNum1)){
             }
             String proportionCa2 = this.getProportionCasesGrowthRate();
             String proportionCa3 = uDrgDateOfDe.getProportionCasesGrowthRate();
             if (proportionCa2 == null) {
                if (proportionCa3 != null) {
                   b = false;
                }
             }else if(proportionCa2.equals(proportionCa3)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAdrgCode(){
       return this.adrgCode;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getLastNum(){
       return this.lastNum;
    }
    public String getLastProportionCases(){
       return this.lastProportionCases;
    }
    public String getLastYear(){
       return this.lastYear;
    }
    public String getProportionCases(){
       return this.proportionCases;
    }
    public String getProportionCasesGrowthRate(){
       return this.proportionCasesGrowthRate;
    }
    public String getTotalNum(){
       return this.totalNum;
    }
    public String getYear(){
       return this.year;
    }
    public String getYearNum(){
       return this.yearNum;
    }
    public int hashCode(){
       String $adrgCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($adrgCode = this.getAdrgCode()) == null)? i: $adrgCode.hashCode();
       result = i1 + 59;
       String $drgCode = this.getDrgCode();
       int i2 = result * 59;
       i1 = ($drgCode == null)? i: $drgCode.hashCode();
       result = i2 + i1;
       String $year = this.getYear();
       i2 = result * 59;
       i1 = ($year == null)? i: $year.hashCode();
       result = i2 + i1;
       String $yearNum = this.getYearNum();
       i2 = result * 59;
       i1 = ($yearNum == null)? i: $yearNum.hashCode();
       result = i2 + i1;
       String $proportionCases = this.getProportionCases();
       i2 = result * 59;
       i1 = ($proportionCases == null)? i: $proportionCases.hashCode();
       result = i2 + i1;
       String lastYear = this.getLastYear();
       i2 = result * 59;
       i1 = (lastYear == null)? i: lastYear.hashCode();
       String lastNum = this.getLastNum();
       i2 = (i2 + i1) * 59;
       i1 = (lastNum == null)? i: lastNum.hashCode();
       String lastProporti = this.getLastProportionCases();
       i2 = (i2 + i1) * 59;
       i1 = (lastProporti == null)? i: lastProporti.hashCode();
       String totalNum = this.getTotalNum();
       i2 = (i2 + i1) * 59;
       i1 = (totalNum == null)? i: totalNum.hashCode();
       String proportionCa = this.getProportionCasesGrowthRate();
       i1 = (i2 + i1) * 59;
       if (proportionCa != null) {
          i = proportionCa.hashCode();
       }
       return (i1 + i);
    }
    public void setAdrgCode(String adrgCode){
       this.adrgCode = adrgCode;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setLastNum(String lastNum){
       this.lastNum = lastNum;
    }
    public void setLastProportionCases(String lastProportionCases){
       this.lastProportionCases = lastProportionCases;
    }
    public void setLastYear(String lastYear){
       this.lastYear = lastYear;
    }
    public void setProportionCases(String proportionCases){
       this.proportionCases = proportionCases;
    }
    public void setProportionCasesGrowthRate(String proportionCasesGrowthRate){
       this.proportionCasesGrowthRate = proportionCasesGrowthRate;
    }
    public void setTotalNum(String totalNum){
       this.totalNum = totalNum;
    }
    public void setYear(String year){
       this.year = year;
    }
    public void setYearNum(String yearNum){
       this.yearNum = yearNum;
    }
    public String toString(){
       return "DrgDateOfDepartmentNumVo\(adrgCode="+this.getAdrgCode()+", drgCode="+this.getDrgCode()+", year="+this.getYear()+", yearNum="+this.getYearNum()+", proportionCases="+this.getProportionCases()+", lastYear="+this.getLastYear()+", lastNum="+this.getLastNum()+", lastProportionCases="+this.getLastProportionCases()+", totalNum="+this.getTotalNum()+", proportionCasesGrowthRate="+this.getProportionCasesGrowthRate()+"\)";
    }
}
