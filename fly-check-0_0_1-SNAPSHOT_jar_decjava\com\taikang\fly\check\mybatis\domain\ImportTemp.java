package com.taikang.fly.check.mybatis.domain.ImportTemp;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ImportTemp implements Serializable	// class@00024d from classes.dex
{
    private String code;
    private String id;
    private String name;
    private String templateType;
    private String type;
    private static final long serialVersionUID = 0x1;

    public void ImportTemp(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ImportTemp;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ImportTemp) {
             b = false;
          }else {
             ImportTemp importTemp = o;
             if (!importTemp.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = importTemp.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String code = this.getCode();
                String code1 = importTemp.getCode();
                if (code == null) {
                   if (code1 != null) {
                      b = false;
                   }
                }else if(code.equals(code1)){
                }
                String name = this.getName();
                String name1 = importTemp.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String type = this.getType();
                String type1 = importTemp.getType();
                if (type == null) {
                   if (type1 != null) {
                      b = false;
                   }
                }else if(type.equals(type1)){
                }
                String templateType = this.getTemplateType();
                String templateType1 = importTemp.getTemplateType();
                if (templateType == null) {
                   if (templateType1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!templateType.equals(templateType1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCode(){
       return this.code;
    }
    public String getId(){
       return this.id;
    }
    public String getName(){
       return this.name;
    }
    public String getTemplateType(){
       return this.templateType;
    }
    public String getType(){
       return this.type;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $code = this.getCode();
       int i2 = result * 59;
       i1 = ($code == null)? i: $code.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $type = this.getType();
       i2 = result * 59;
       i1 = ($type == null)? i: $type.hashCode();
       result = i2 + i1;
       String $templateType = this.getTemplateType();
       i1 = result * 59;
       if ($templateType != null) {
          i = $templateType.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCode(String code){
       this.code = code;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setTemplateType(String templateType){
       this.templateType = templateType;
    }
    public void setType(String type){
       this.type = type;
    }
    public String toString(){
       return "ImportTemp\(id="+this.getId()+", code="+this.getCode()+", name="+this.getName()+", type="+this.getType()+", templateType="+this.getTemplateType()+"\)";
    }
}
