package com.taikang.fly.check.dto.mapstruct.MfromtOfficeConfigMappingImpl;
import com.taikang.fly.check.dto.mapstruct.MfromtOfficeConfigMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.MfromtOfficeConfig;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigRespDto;
import java.lang.String;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.DateUtils;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class MfromtOfficeConfigMappingImpl implements MfromtOfficeConfigMapping	// class@00016a from classes.dex
{

    public void MfromtOfficeConfigMappingImpl(){
       super();
    }
    public ColConfigRespDto toColConfigRespDto(MfromtOfficeConfig mfromtOfficeConfig){
       ColConfigRespDto uColConfigRe;
       if (mfromtOfficeConfig == null) {
          uColConfigRe = null;
       }else {
          uColConfigRe = new ColConfigRespDto();
          uColConfigRe.setIsValid(mfromtOfficeConfig.getIsValid());
          uColConfigRe.setId(mfromtOfficeConfig.getId());
          uColConfigRe.setBusinessKey(mfromtOfficeConfig.getBusinessKey());
          uColConfigRe.setBusinessName(mfromtOfficeConfig.getBusinessName());
          uColConfigRe.setCreatedBy(mfromtOfficeConfig.getCreatedBy());
          uColConfigRe.setModifier(mfromtOfficeConfig.getModifier());
          uColConfigRe.setCreatedTime(DateUtils.formatLocalDateTime(mfromtOfficeConfig.getCreatedTime()));
          uColConfigRe.setModifyTime(DateUtils.formatLocalDateTime(mfromtOfficeConfig.getModifyTime()));
       }
       return uColConfigRe;
    }
    public List toColConfigRespDtoList(List mfromtColumnConfigs){
       List list;
       if (mfromtColumnConfigs == null) {
          list = null;
       }else {
          list = new ArrayList(mfromtColumnConfigs.size());
          Iterator iterator = mfromtColumnConfigs.iterator();
          while (iterator.hasNext()) {
             list.add(this.toColConfigRespDto(iterator.next()));
          }
       }
       return list;
    }
}
