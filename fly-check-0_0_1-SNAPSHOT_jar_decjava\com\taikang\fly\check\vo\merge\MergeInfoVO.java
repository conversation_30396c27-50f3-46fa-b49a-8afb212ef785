package com.taikang.fly.check.vo.merge.MergeInfoVO;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.Double;
import java.lang.StringBuilder;

public class MergeInfoVO	// class@00037f from classes.dex
{
    private Double acctPay;
    private LocalDateTime admDate;
    private String admDeptName;
    private String admDiseaseCode;
    private String admDiseaseName;
    private String age;
    private String alternateFieldFive;
    private String alternateFieldFour;
    private String alternateFieldOne;
    private String alternateFieldThree;
    private String alternateFieldTwo;
    private Double amt;
    private Double beyondAmt;
    private Double beyondCnt;
    private String billingDeptCode;
    private String billingDeptName;
    private String billingPdrCode;
    private String billingPdrName;
    private String birthDate;
    private String bridgeId;
    private String caseNum;
    private Double cashPayAmt;
    private String certNo;
    private String chfPdrCode;
    private String chfPdrName;
    private Double civilServantMafPay;
    private String claimType;
    private Double cnt;
    private Double coordinatePay;
    private Double criticalIllnessInsurance;
    private String datasource;
    private String dosageFrom;
    private LocalDateTime dsgDate;
    private String dsgDeptName;
    private String dsgDiseaseCode;
    private String dsgDiseaseName;
    private String dsgWay;
    private String empName;
    private Double enterprisePay;
    private LocalDateTime feeTime;
    private String gender;
    private String hospLv;
    private String hospitalItemCode;
    private String hospitalItemName;
    private String icCard;
    private String ifLocalFlag;
    private Double inScpAmt;
    private String insuranceType;
    private String iptDays;
    private String iptOtpNo;
    private Double largeAmountPay;
    private Double mafPay;
    private String mainOp;
    private String medType;
    private String medicalInstitutionCode;
    private String medicalInstitutionName;
    private Double medicalInsuranceAmt;
    private String medicalInsuranceItemCode;
    private String medicalInsuranceItemName;
    private String medicalInsurancePayType;
    private String month;
    private String packageUnit;
    private String payType;
    private String policyBasis;
    private Double price;
    private String psnName;
    private Double psnPay;
    private Double psnPayAmt;
    private String psnType;
    private String psoNo;
    private String reimbursement;
    private String ruleConnotation;
    private String ruleId;
    private String ruleName;
    private String ruleSql;
    private String ruleType;
    private String setlId;
    private LocalDateTime setlTime;
    private String spec;
    private String subOp;
    private Double totalMedicalExpenses;
    private String transferDeptName;
    private Double useAmt;
    private Double useCnt;
    private String year;

    public void MergeInfoVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MergeInfoVO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MergeInfoVO){
          b = false;
       }else {
          MergeInfoVO mergeInfoVO = o;
          if (!mergeInfoVO.canEqual(this)) {
             b = false;
          }else {
             String bridgeId = this.getBridgeId();
             String bridgeId1 = mergeInfoVO.getBridgeId();
             if (bridgeId == null) {
                if (bridgeId1 != null) {
                   b = false;
                }
             }else if(bridgeId.equals(bridgeId1)){
             }
             String setlId = this.getSetlId();
             String setlId1 = mergeInfoVO.getSetlId();
             if (setlId == null) {
                if (setlId1 != null) {
                   b = false;
                }
             }else if(setlId.equals(setlId1)){
             }
             String medicalInsti = this.getMedicalInstitutionCode();
             String medicalInsti1 = mergeInfoVO.getMedicalInstitutionCode();
             if (medicalInsti == null) {
                if (medicalInsti1 != null) {
                   b = false;
                }
             }else if(medicalInsti.equals(medicalInsti1)){
             }
             String medicalInsti2 = this.getMedicalInstitutionName();
             String medicalInsti3 = mergeInfoVO.getMedicalInstitutionName();
             if (medicalInsti2 == null) {
                if (medicalInsti3 != null) {
                   b = false;
                }
             }else if(medicalInsti2.equals(medicalInsti3)){
             }
             String hospLv = this.getHospLv();
             String hospLv1 = mergeInfoVO.getHospLv();
             if (hospLv == null) {
                if (hospLv1 != null) {
                   b = false;
                }
             }else if(hospLv.equals(hospLv1)){
             }
             LocalDateTime setlTime = this.getSetlTime();
             LocalDateTime setlTime1 = mergeInfoVO.getSetlTime();
             if (setlTime == null) {
                if (setlTime1 != null) {
                   b = false;
                }
             }else if(setlTime.equals(setlTime1)){
             }
             String year = this.getYear();
             String year1 = mergeInfoVO.getYear();
             if (year == null) {
                if (year1 != null) {
                label_00c7 :
                   b = false;
                }
             }else if(year.equals(year1)){
             }
             String month = this.getMonth();
             String month1 = mergeInfoVO.getMonth();
             if (month == null) {
                if (month1 != null) {
                   b = false;
                }
             }else if(month.equals(month1)){
             }
             String iptOtpNo = this.getIptOtpNo();
             String iptOtpNo1 = mergeInfoVO.getIptOtpNo();
             if (iptOtpNo == null) {
                if (iptOtpNo1 != null) {
                label_00fb :
                   b = false;
                }
             }else if(iptOtpNo.equals(iptOtpNo1)){
             }
             String psoNo = this.getPsoNo();
             String psoNo1 = mergeInfoVO.getPsoNo();
             if (psoNo == null) {
                if (psoNo1 != null) {
                   b = false;
                }
             }else if(psoNo.equals(psoNo1)){
             }
             String certNo = this.getCertNo();
             String certNo1 = mergeInfoVO.getCertNo();
             if (certNo == null) {
                if (certNo1 != null) {
                label_012f :
                   b = false;
                }
             }else if(certNo.equals(certNo1)){
             }
             String caseNum = this.getCaseNum();
             String caseNum1 = mergeInfoVO.getCaseNum();
             if (caseNum == null) {
                if (caseNum1 != null) {
                   b = false;
                }
             }else if(caseNum.equals(caseNum1)){
             }
             String insuranceTyp = this.getInsuranceType();
             String insuranceTyp1 = mergeInfoVO.getInsuranceType();
             if (insuranceTyp == null) {
                if (insuranceTyp1 != null) {
                   b = false;
                }
             }else if(insuranceTyp.equals(insuranceTyp1)){
             }
             String psnType = this.getPsnType();
             String psnType1 = mergeInfoVO.getPsnType();
             if (psnType == null) {
                if (psnType1 != null) {
                label_017d :
                   b = false;
                }
             }else if(psnType.equals(psnType1)){
             }
             String admDeptName = this.getAdmDeptName();
             String admDeptName1 = mergeInfoVO.getAdmDeptName();
             if (admDeptName == null) {
                if (admDeptName1 != null) {
                   b = false;
                }
             }else if(admDeptName.equals(admDeptName1)){
             }
             String transferDept = this.getTransferDeptName();
             String transferDept1 = mergeInfoVO.getTransferDeptName();
             if (transferDept == null) {
                if (transferDept1 != null) {
                label_01af :
                   b = false;
                }
             }else if(transferDept.equals(transferDept1)){
             }
             String dsgDeptName = this.getDsgDeptName();
             String dsgDeptName1 = mergeInfoVO.getDsgDeptName();
             if (dsgDeptName == null) {
                if (dsgDeptName1 != null) {
                   b = false;
                }
             }else if(dsgDeptName.equals(dsgDeptName1)){
             }
             String chfPdrCode = this.getChfPdrCode();
             String chfPdrCode1 = mergeInfoVO.getChfPdrCode();
             if (chfPdrCode == null) {
                if (chfPdrCode1 != null) {
                label_01e3 :
                   b = false;
                }
             }else if(chfPdrCode.equals(chfPdrCode1)){
             }
             String chfPdrName = this.getChfPdrName();
             String chfPdrName1 = mergeInfoVO.getChfPdrName();
             if (chfPdrName == null) {
                if (chfPdrName1 != null) {
                   b = false;
                }
             }else if(chfPdrName.equals(chfPdrName1)){
             }
             String psnName = this.getPsnName();
             String psnName1 = mergeInfoVO.getPsnName();
             if (psnName == null) {
                if (psnName1 != null) {
                label_0217 :
                   b = false;
                }
             }else if(psnName.equals(psnName1)){
             }
             String gender = this.getGender();
             String gender1 = mergeInfoVO.getGender();
             if (gender == null) {
                if (gender1 != null) {
                   b = false;
                }
             }else if(gender.equals(gender1)){
             }
             String birthDate = this.getBirthDate();
             String birthDate1 = mergeInfoVO.getBirthDate();
             if (birthDate == null) {
                if (birthDate1 != null) {
                label_024b :
                   b = false;
                }
             }else if(birthDate.equals(birthDate1)){
             }
             String age = this.getAge();
             String age1 = mergeInfoVO.getAge();
             if (age == null) {
                if (age1 != null) {
                   b = false;
                }
             }else if(age.equals(age1)){
             }
             String empName = this.getEmpName();
             String empName1 = mergeInfoVO.getEmpName();
             if (empName == null) {
                if (empName1 != null) {
                label_027d :
                   b = false;
                }
             }else if(empName.equals(empName1)){
             }
             String claimType = this.getClaimType();
             String claimType1 = mergeInfoVO.getClaimType();
             if (claimType == null) {
                if (claimType1 != null) {
                   b = false;
                }
             }else if(claimType.equals(claimType1)){
             }
             String ifLocalFlag = this.getIfLocalFlag();
             String ifLocalFlag1 = mergeInfoVO.getIfLocalFlag();
             if (ifLocalFlag == null) {
                if (ifLocalFlag1 != null) {
                   b = false;
                }
             }else if(ifLocalFlag.equals(ifLocalFlag1)){
             }
             LocalDateTime admDate = this.getAdmDate();
             LocalDateTime admDate1 = mergeInfoVO.getAdmDate();
             if (admDate == null) {
                if (admDate1 != null) {
                   b = false;
                }
             }else if(admDate.equals(admDate1)){
             }
             LocalDateTime dsgDate = this.getDsgDate();
             LocalDateTime dsgDate1 = mergeInfoVO.getDsgDate();
             if (dsgDate == null) {
                if (dsgDate1 != null) {
                   b = false;
                }
             }else if(dsgDate.equals(dsgDate1)){
             }
             String iptDays = this.getIptDays();
             String iptDays1 = mergeInfoVO.getIptDays();
             if (iptDays == null) {
                if (iptDays1 != null) {
                label_02fd :
                   b = false;
                }
             }else if(iptDays.equals(iptDays1)){
             }
             String dsgWay = this.getDsgWay();
             String dsgWay1 = mergeInfoVO.getDsgWay();
             if (dsgWay == null) {
                if (dsgWay1 != null) {
                   b = false;
                }
             }else if(dsgWay.equals(dsgWay1)){
             }
             Double totalMedical = this.getTotalMedicalExpenses();
             Double totalMedical1 = mergeInfoVO.getTotalMedicalExpenses();
             if (totalMedical == null) {
                if (totalMedical1 != null) {
                label_0331 :
                   b = false;
                }
             }else if(totalMedical.equals(totalMedical1)){
             }
             Double coordinatePa = this.getCoordinatePay();
             Double coordinatePa1 = mergeInfoVO.getCoordinatePay();
             if (coordinatePa == null) {
                if (coordinatePa1 != null) {
                   b = false;
                }
             }else if(coordinatePa.equals(coordinatePa1)){
             }
             Double criticalIlln = this.getCriticalIllnessInsurance();
             Double criticalIlln1 = mergeInfoVO.getCriticalIllnessInsurance();
             if (criticalIlln == null) {
                if (criticalIlln1 != null) {
                label_0365 :
                   b = false;
                }
             }else if(criticalIlln.equals(criticalIlln1)){
             }
             Double mafPay = this.getMafPay();
             Double mafPay1 = mergeInfoVO.getMafPay();
             if (mafPay == null) {
                if (mafPay1 != null) {
                   b = false;
                }
             }else if(mafPay.equals(mafPay1)){
             }
             Double civilServant = this.getCivilServantMafPay();
             Double civilServant1 = mergeInfoVO.getCivilServantMafPay();
             if (civilServant == null) {
                if (civilServant1 != null) {
                label_0399 :
                   b = false;
                }
             }else if(civilServant.equals(civilServant1)){
             }
             Double largeAmountP = this.getLargeAmountPay();
             Double largeAmountP1 = mergeInfoVO.getLargeAmountPay();
             if (largeAmountP == null) {
                if (largeAmountP1 != null) {
                   b = false;
                }
             }else if(largeAmountP.equals(largeAmountP1)){
             }
             Double enterprisePa = this.getEnterprisePay();
             Double enterprisePa1 = mergeInfoVO.getEnterprisePay();
             if (enterprisePa == null) {
                if (enterprisePa1 != null) {
                   b = false;
                }
             }else if(enterprisePa.equals(enterprisePa1)){
             }
             Double cashPayAmt = this.getCashPayAmt();
             Double cashPayAmt1 = mergeInfoVO.getCashPayAmt();
             if (cashPayAmt == null) {
                if (cashPayAmt1 != null) {
                label_03e7 :
                   b = false;
                }
             }else if(cashPayAmt.equals(cashPayAmt1)){
             }
             Double acctPay = this.getAcctPay();
             Double acctPay1 = mergeInfoVO.getAcctPay();
             if (acctPay == null) {
                if (acctPay1 != null) {
                   b = false;
                }
             }else if(acctPay.equals(acctPay1)){
             }
             Double psnPay = this.getPsnPay();
             Double psnPay1 = mergeInfoVO.getPsnPay();
             if (psnPay == null) {
                if (psnPay1 != null) {
                label_0419 :
                   b = false;
                }
             }else if(psnPay.equals(psnPay1)){
             }
             Double psnPayAmt = this.getPsnPayAmt();
             Double psnPayAmt1 = mergeInfoVO.getPsnPayAmt();
             if (psnPayAmt == null) {
                if (psnPayAmt1 != null) {
                   b = false;
                }
             }else if(psnPayAmt.equals(psnPayAmt1)){
             }
             Double inScpAmt = this.getInScpAmt();
             Double inScpAmt1 = mergeInfoVO.getInScpAmt();
             if (inScpAmt == null) {
                if (inScpAmt1 != null) {
                label_044d :
                   b = false;
                }
             }else if(inScpAmt.equals(inScpAmt1)){
             }
             String admDiseaseCo = this.getAdmDiseaseCode();
             String admDiseaseCo1 = mergeInfoVO.getAdmDiseaseCode();
             if (admDiseaseCo == null) {
                if (admDiseaseCo1 != null) {
                   b = false;
                }
             }else if(admDiseaseCo.equals(admDiseaseCo1)){
             }
             String admDiseaseNa = this.getAdmDiseaseName();
             String admDiseaseNa1 = mergeInfoVO.getAdmDiseaseName();
             if (admDiseaseNa == null) {
                if (admDiseaseNa1 != null) {
                label_047f :
                   b = false;
                }
             }else if(admDiseaseNa.equals(admDiseaseNa1)){
             }
             String dsgDiseaseCo = this.getDsgDiseaseCode();
             String dsgDiseaseCo1 = mergeInfoVO.getDsgDiseaseCode();
             if (dsgDiseaseCo == null) {
                if (dsgDiseaseCo1 != null) {
                   b = false;
                }
             }else if(dsgDiseaseCo.equals(dsgDiseaseCo1)){
             }
             String dsgDiseaseNa = this.getDsgDiseaseName();
             String dsgDiseaseNa1 = mergeInfoVO.getDsgDiseaseName();
             if (dsgDiseaseNa == null) {
                if (dsgDiseaseNa1 != null) {
                label_04b1 :
                   b = false;
                }
             }else if(dsgDiseaseNa.equals(dsgDiseaseNa1)){
             }
             String medicalInsur = this.getMedicalInsurancePayType();
             String medicalInsur1 = mergeInfoVO.getMedicalInsurancePayType();
             if (medicalInsur == null) {
                if (medicalInsur1 != null) {
                   b = false;
                }
             }else if(medicalInsur.equals(medicalInsur1)){
             }
             String billingDeptC = this.getBillingDeptCode();
             String billingDeptC1 = mergeInfoVO.getBillingDeptCode();
             if (billingDeptC == null) {
                if (billingDeptC1 != null) {
                label_04e5 :
                   b = false;
                }
             }else if(billingDeptC.equals(billingDeptC1)){
             }
             String billingDeptN = this.getBillingDeptName();
             String billingDeptN1 = mergeInfoVO.getBillingDeptName();
             if (billingDeptN == null) {
                if (billingDeptN1 != null) {
                   b = false;
                }
             }else if(billingDeptN.equals(billingDeptN1)){
             }
             String billingPdrCo = this.getBillingPdrCode();
             String billingPdrCo1 = mergeInfoVO.getBillingPdrCode();
             if (billingPdrCo == null) {
                if (billingPdrCo1 != null) {
                   b = false;
                }
             }else if(billingPdrCo.equals(billingPdrCo1)){
             }
             String billingPdrNa = this.getBillingPdrName();
             String billingPdrNa1 = mergeInfoVO.getBillingPdrName();
             if (billingPdrNa == null) {
                if (billingPdrNa1 != null) {
                label_0533 :
                   b = false;
                }
             }else if(billingPdrNa.equals(billingPdrNa1)){
             }
             String medType = this.getMedType();
             String medType1 = mergeInfoVO.getMedType();
             if (medType == null) {
                if (medType1 != null) {
                   b = false;
                }
             }else if(medType.equals(medType1)){
             }
             LocalDateTime feeTime = this.getFeeTime();
             LocalDateTime feeTime1 = mergeInfoVO.getFeeTime();
             if (feeTime == null) {
                if (feeTime1 != null) {
                   b = false;
                }
             }else if(feeTime.equals(feeTime1)){
             }
             String hospitalItem = this.getHospitalItemCode();
             String hospitalItem1 = mergeInfoVO.getHospitalItemCode();
             if (hospitalItem == null) {
                if (hospitalItem1 != null) {
                label_0581 :
                   b = false;
                }
             }else if(hospitalItem.equals(hospitalItem1)){
             }
             String hospitalItem2 = this.getHospitalItemName();
             String hospitalItem3 = mergeInfoVO.getHospitalItemName();
             if (hospitalItem2 == null) {
                if (hospitalItem3 != null) {
                   b = false;
                }
             }else if(hospitalItem2.equals(hospitalItem3)){
             }
             String medicalInsur2 = this.getMedicalInsuranceItemCode();
             String medicalInsur3 = mergeInfoVO.getMedicalInsuranceItemCode();
             if (medicalInsur2 == null) {
                if (medicalInsur3 != null) {
                label_05b5 :
                   b = false;
                }
             }else if(medicalInsur2.equals(medicalInsur3)){
             }
             String medicalInsur4 = this.getMedicalInsuranceItemName();
             String medicalInsur5 = mergeInfoVO.getMedicalInsuranceItemName();
             if (medicalInsur4 == null) {
                if (medicalInsur5 != null) {
                   b = false;
                }
             }else if(medicalInsur4.equals(medicalInsur5)){
             }
             String spec = this.getSpec();
             String spec1 = mergeInfoVO.getSpec();
             if (spec == null) {
                if (spec1 != null) {
                label_05e9 :
                   b = false;
                }
             }else if(spec.equals(spec1)){
             }
             String dosageFrom = this.getDosageFrom();
             String dosageFrom1 = mergeInfoVO.getDosageFrom();
             if (dosageFrom == null) {
                if (dosageFrom1 != null) {
                   b = false;
                }
             }else if(dosageFrom.equals(dosageFrom1)){
             }
             String packageUnit = this.getPackageUnit();
             String packageUnit1 = mergeInfoVO.getPackageUnit();
             if (packageUnit == null) {
                if (packageUnit1 != null) {
                label_061d :
                   b = false;
                }
             }else if(packageUnit.equals(packageUnit1)){
             }
             Double price = this.getPrice();
             Double price1 = mergeInfoVO.getPrice();
             if (price == null) {
                if (price1 != null) {
                   b = false;
                }
             }else if(price.equals(price1)){
             }
             Double cnt = this.getCnt();
             Double cnt1 = mergeInfoVO.getCnt();
             if (cnt == null) {
                if (cnt1 != null) {
                   b = false;
                }
             }else if(cnt.equals(cnt1)){
             }
             Double amt = this.getAmt();
             Double amt1 = mergeInfoVO.getAmt();
             if (amt == null) {
                if (amt1 != null) {
                label_066b :
                   b = false;
                }
             }else if(amt.equals(amt1)){
             }
             Double medicalInsur6 = this.getMedicalInsuranceAmt();
             Double medicalInsur7 = mergeInfoVO.getMedicalInsuranceAmt();
             if (medicalInsur6 == null) {
                if (medicalInsur7 != null) {
                   b = false;
                }
             }else if(medicalInsur6.equals(medicalInsur7)){
             }
             String payType = this.getPayType();
             String payType1 = mergeInfoVO.getPayType();
             if (payType == null) {
                if (payType1 != null) {
                label_069d :
                   b = false;
                }
             }else if(payType.equals(payType1)){
             }
             String reimbursemen = this.getReimbursement();
             String reimbursemen1 = mergeInfoVO.getReimbursement();
             if (reimbursemen == null) {
                if (reimbursemen1 != null) {
                   b = false;
                }
             }else if(reimbursemen.equals(reimbursemen1)){
             }
             Double useCnt = this.getUseCnt();
             Double useCnt1 = mergeInfoVO.getUseCnt();
             if (useCnt == null) {
                if (useCnt1 != null) {
                label_06d1 :
                   b = false;
                }
             }else if(useCnt.equals(useCnt1)){
             }
             Double useAmt = this.getUseAmt();
             Double useAmt1 = mergeInfoVO.getUseAmt();
             if (useAmt == null) {
                if (useAmt1 != null) {
                   b = false;
                }
             }else if(useAmt.equals(useAmt1)){
             }
             Double beyondCnt = this.getBeyondCnt();
             Double beyondCnt1 = mergeInfoVO.getBeyondCnt();
             if (beyondCnt == null) {
                if (beyondCnt1 != null) {
                label_0705 :
                   b = false;
                }
             }else if(beyondCnt.equals(beyondCnt1)){
             }
             Double beyondAmt = this.getBeyondAmt();
             Double beyondAmt1 = mergeInfoVO.getBeyondAmt();
             if (beyondAmt == null) {
                if (beyondAmt1 != null) {
                   b = false;
                }
             }else if(beyondAmt.equals(beyondAmt1)){
             }
             String icCard = this.getIcCard();
             String icCard1 = mergeInfoVO.getIcCard();
             if (icCard == null) {
                if (icCard1 != null) {
                label_0737 :
                   b = false;
                }
             }else if(icCard.equals(icCard1)){
             }
             String mainOp = this.getMainOp();
             String mainOp1 = mergeInfoVO.getMainOp();
             if (mainOp == null) {
                if (mainOp1 != null) {
                   b = false;
                }
             }else if(mainOp.equals(mainOp1)){
             }
             String subOp = this.getSubOp();
             String subOp1 = mergeInfoVO.getSubOp();
             if (subOp == null) {
                if (subOp1 != null) {
                label_076b :
                   b = false;
                }
             }else if(subOp.equals(subOp1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = mergeInfoVO.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = mergeInfoVO.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String datasource = this.getDatasource();
             String datasource1 = mergeInfoVO.getDatasource();
             if (datasource == null) {
                if (datasource1 != null) {
                label_07b9 :
                   b = false;
                }
             }else if(datasource.equals(datasource1)){
             }
             String ruleId = this.getRuleId();
             String ruleId1 = mergeInfoVO.getRuleId();
             if (ruleId == null) {
                if (ruleId1 != null) {
                   b = false;
                }
             }else if(ruleId.equals(ruleId1)){
             }
             String ruleConnotat = this.getRuleConnotation();
             String ruleConnotat1 = mergeInfoVO.getRuleConnotation();
             if (ruleConnotat == null) {
                if (ruleConnotat1 != null) {
                label_07ed :
                   b = false;
                }
             }else if(ruleConnotat.equals(ruleConnotat1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = mergeInfoVO.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             String alternateFie = this.getAlternateFieldOne();
             String alternateFie1 = mergeInfoVO.getAlternateFieldOne();
             if (alternateFie == null) {
                if (alternateFie1 != null) {
                label_0821 :
                   b = false;
                }
             }else if(alternateFie.equals(alternateFie1)){
             }
             String alternateFie2 = this.getAlternateFieldTwo();
             String alternateFie3 = mergeInfoVO.getAlternateFieldTwo();
             if (alternateFie2 == null) {
                if (alternateFie3 != null) {
                   b = false;
                }
             }else if(alternateFie2.equals(alternateFie3)){
             }
             String alternateFie4 = this.getAlternateFieldThree();
             String alternateFie5 = mergeInfoVO.getAlternateFieldThree();
             if (alternateFie4 == null) {
                if (alternateFie5 != null) {
                label_0851 :
                   b = false;
                }
             }else if(alternateFie4.equals(alternateFie5)){
             }
             String alternateFie6 = this.getAlternateFieldFour();
             String alternateFie7 = mergeInfoVO.getAlternateFieldFour();
             if (alternateFie6 == null) {
                if (alternateFie7 != null) {
                   b = false;
                }
             }else if(alternateFie6.equals(alternateFie7)){
             }
             String alternateFie8 = this.getAlternateFieldFive();
             String alternateFie9 = mergeInfoVO.getAlternateFieldFive();
             if (alternateFie8 == null) {
                if (alternateFie9 != null) {
                label_0881 :
                   b = false;
                }
             }else if(alternateFie8.equals(alternateFie9)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = mergeInfoVO.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Double getAcctPay(){
       return this.acctPay;
    }
    public LocalDateTime getAdmDate(){
       return this.admDate;
    }
    public String getAdmDeptName(){
       return this.admDeptName;
    }
    public String getAdmDiseaseCode(){
       return this.admDiseaseCode;
    }
    public String getAdmDiseaseName(){
       return this.admDiseaseName;
    }
    public String getAge(){
       return this.age;
    }
    public String getAlternateFieldFive(){
       return this.alternateFieldFive;
    }
    public String getAlternateFieldFour(){
       return this.alternateFieldFour;
    }
    public String getAlternateFieldOne(){
       return this.alternateFieldOne;
    }
    public String getAlternateFieldThree(){
       return this.alternateFieldThree;
    }
    public String getAlternateFieldTwo(){
       return this.alternateFieldTwo;
    }
    public Double getAmt(){
       return this.amt;
    }
    public Double getBeyondAmt(){
       return this.beyondAmt;
    }
    public Double getBeyondCnt(){
       return this.beyondCnt;
    }
    public String getBillingDeptCode(){
       return this.billingDeptCode;
    }
    public String getBillingDeptName(){
       return this.billingDeptName;
    }
    public String getBillingPdrCode(){
       return this.billingPdrCode;
    }
    public String getBillingPdrName(){
       return this.billingPdrName;
    }
    public String getBirthDate(){
       return this.birthDate;
    }
    public String getBridgeId(){
       return this.bridgeId;
    }
    public String getCaseNum(){
       return this.caseNum;
    }
    public Double getCashPayAmt(){
       return this.cashPayAmt;
    }
    public String getCertNo(){
       return this.certNo;
    }
    public String getChfPdrCode(){
       return this.chfPdrCode;
    }
    public String getChfPdrName(){
       return this.chfPdrName;
    }
    public Double getCivilServantMafPay(){
       return this.civilServantMafPay;
    }
    public String getClaimType(){
       return this.claimType;
    }
    public Double getCnt(){
       return this.cnt;
    }
    public Double getCoordinatePay(){
       return this.coordinatePay;
    }
    public Double getCriticalIllnessInsurance(){
       return this.criticalIllnessInsurance;
    }
    public String getDatasource(){
       return this.datasource;
    }
    public String getDosageFrom(){
       return this.dosageFrom;
    }
    public LocalDateTime getDsgDate(){
       return this.dsgDate;
    }
    public String getDsgDeptName(){
       return this.dsgDeptName;
    }
    public String getDsgDiseaseCode(){
       return this.dsgDiseaseCode;
    }
    public String getDsgDiseaseName(){
       return this.dsgDiseaseName;
    }
    public String getDsgWay(){
       return this.dsgWay;
    }
    public String getEmpName(){
       return this.empName;
    }
    public Double getEnterprisePay(){
       return this.enterprisePay;
    }
    public LocalDateTime getFeeTime(){
       return this.feeTime;
    }
    public String getGender(){
       return this.gender;
    }
    public String getHospLv(){
       return this.hospLv;
    }
    public String getHospitalItemCode(){
       return this.hospitalItemCode;
    }
    public String getHospitalItemName(){
       return this.hospitalItemName;
    }
    public String getIcCard(){
       return this.icCard;
    }
    public String getIfLocalFlag(){
       return this.ifLocalFlag;
    }
    public Double getInScpAmt(){
       return this.inScpAmt;
    }
    public String getInsuranceType(){
       return this.insuranceType;
    }
    public String getIptDays(){
       return this.iptDays;
    }
    public String getIptOtpNo(){
       return this.iptOtpNo;
    }
    public Double getLargeAmountPay(){
       return this.largeAmountPay;
    }
    public Double getMafPay(){
       return this.mafPay;
    }
    public String getMainOp(){
       return this.mainOp;
    }
    public String getMedType(){
       return this.medType;
    }
    public String getMedicalInstitutionCode(){
       return this.medicalInstitutionCode;
    }
    public String getMedicalInstitutionName(){
       return this.medicalInstitutionName;
    }
    public Double getMedicalInsuranceAmt(){
       return this.medicalInsuranceAmt;
    }
    public String getMedicalInsuranceItemCode(){
       return this.medicalInsuranceItemCode;
    }
    public String getMedicalInsuranceItemName(){
       return this.medicalInsuranceItemName;
    }
    public String getMedicalInsurancePayType(){
       return this.medicalInsurancePayType;
    }
    public String getMonth(){
       return this.month;
    }
    public String getPackageUnit(){
       return this.packageUnit;
    }
    public String getPayType(){
       return this.payType;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public Double getPrice(){
       return this.price;
    }
    public String getPsnName(){
       return this.psnName;
    }
    public Double getPsnPay(){
       return this.psnPay;
    }
    public Double getPsnPayAmt(){
       return this.psnPayAmt;
    }
    public String getPsnType(){
       return this.psnType;
    }
    public String getPsoNo(){
       return this.psoNo;
    }
    public String getReimbursement(){
       return this.reimbursement;
    }
    public String getRuleConnotation(){
       return this.ruleConnotation;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSetlId(){
       return this.setlId;
    }
    public LocalDateTime getSetlTime(){
       return this.setlTime;
    }
    public String getSpec(){
       return this.spec;
    }
    public String getSubOp(){
       return this.subOp;
    }
    public Double getTotalMedicalExpenses(){
       return this.totalMedicalExpenses;
    }
    public String getTransferDeptName(){
       return this.transferDeptName;
    }
    public Double getUseAmt(){
       return this.useAmt;
    }
    public Double getUseCnt(){
       return this.useCnt;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $bridgeId;
       int PRIME = 59;
       int result = 1;
       int i = (($bridgeId = this.getBridgeId()) == null)? 43: $bridgeId.hashCode();
       result = i + 59;
       String $setlId = this.getSetlId();
       int i1 = result * 59;
       i = ($setlId == null)? 43: $setlId.hashCode();
       result = i1 + i;
       String $medicalInstitutionCode = this.getMedicalInstitutionCode();
       i1 = result * 59;
       i = ($medicalInstitutionCode == null)? 43: $medicalInstitutionCode.hashCode();
       result = i1 + i;
       String $medicalInstitutionName = this.getMedicalInstitutionName();
       i1 = result * 59;
       i = ($medicalInstitutionName == null)? 43: $medicalInstitutionName.hashCode();
       result = i1 + i;
       String $hospLv = this.getHospLv();
       i1 = result * 59;
       i = ($hospLv == null)? 43: $hospLv.hashCode();
       result = i1 + i;
       LocalDateTime setlTime = this.getSetlTime();
       i1 = result * 59;
       i = (setlTime == null)? 43: setlTime.hashCode();
       String year = this.getYear();
       i1 = (i1 + i) * 59;
       i = (year == null)? 43: year.hashCode();
       String month = this.getMonth();
       i1 = (i1 + i) * 59;
       i = (month == null)? 43: month.hashCode();
       String iptOtpNo = this.getIptOtpNo();
       i1 = (i1 + i) * 59;
       i = (iptOtpNo == null)? 43: iptOtpNo.hashCode();
       String psoNo = this.getPsoNo();
       i1 = (i1 + i) * 59;
       i = (psoNo == null)? 43: psoNo.hashCode();
       String certNo = this.getCertNo();
       i1 = (i1 + i) * 59;
       i = (certNo == null)? 43: certNo.hashCode();
       String caseNum = this.getCaseNum();
       i1 = (i1 + i) * 59;
       i = (caseNum == null)? 43: caseNum.hashCode();
       String insuranceTyp = this.getInsuranceType();
       i1 = (i1 + i) * 59;
       i = (insuranceTyp == null)? 43: insuranceTyp.hashCode();
       String psnType = this.getPsnType();
       i1 = (i1 + i) * 59;
       i = (psnType == null)? 43: psnType.hashCode();
       String admDeptName = this.getAdmDeptName();
       i1 = (i1 + i) * 59;
       i = (admDeptName == null)? 43: admDeptName.hashCode();
       String transferDept = this.getTransferDeptName();
       i1 = (i1 + i) * 59;
       i = (transferDept == null)? 43: transferDept.hashCode();
       String dsgDeptName = this.getDsgDeptName();
       i1 = (i1 + i) * 59;
       i = (dsgDeptName == null)? 43: dsgDeptName.hashCode();
       String chfPdrCode = this.getChfPdrCode();
       i1 = (i1 + i) * 59;
       i = (chfPdrCode == null)? 43: chfPdrCode.hashCode();
       String chfPdrName = this.getChfPdrName();
       i1 = (i1 + i) * 59;
       i = (chfPdrName == null)? 43: chfPdrName.hashCode();
       String psnName = this.getPsnName();
       i1 = (i1 + i) * 59;
       i = (psnName == null)? 43: psnName.hashCode();
       String gender = this.getGender();
       i1 = (i1 + i) * 59;
       i = (gender == null)? 43: gender.hashCode();
       String birthDate = this.getBirthDate();
       i1 = (i1 + i) * 59;
       i = (birthDate == null)? 43: birthDate.hashCode();
       String age = this.getAge();
       i1 = (i1 + i) * 59;
       i = (age == null)? 43: age.hashCode();
       String empName = this.getEmpName();
       i1 = (i1 + i) * 59;
       i = (empName == null)? 43: empName.hashCode();
       String claimType = this.getClaimType();
       i1 = (i1 + i) * 59;
       i = (claimType == null)? 43: claimType.hashCode();
       String ifLocalFlag = this.getIfLocalFlag();
       i1 = (i1 + i) * 59;
       i = (ifLocalFlag == null)? 43: ifLocalFlag.hashCode();
       LocalDateTime admDate = this.getAdmDate();
       i1 = (i1 + i) * 59;
       i = (admDate == null)? 43: admDate.hashCode();
       LocalDateTime dsgDate = this.getDsgDate();
       i1 = (i1 + i) * 59;
       i = (dsgDate == null)? 43: dsgDate.hashCode();
       String iptDays = this.getIptDays();
       i1 = (i1 + i) * 59;
       i = (iptDays == null)? 43: iptDays.hashCode();
       String dsgWay = this.getDsgWay();
       i1 = (i1 + i) * 59;
       i = (dsgWay == null)? 43: dsgWay.hashCode();
       Double totalMedical = this.getTotalMedicalExpenses();
       i1 = (i1 + i) * 59;
       i = (totalMedical == null)? 43: totalMedical.hashCode();
       Double coordinatePa = this.getCoordinatePay();
       i1 = (i1 + i) * 59;
       i = (coordinatePa == null)? 43: coordinatePa.hashCode();
       Double criticalIlln = this.getCriticalIllnessInsurance();
       i1 = (i1 + i) * 59;
       i = (criticalIlln == null)? 43: criticalIlln.hashCode();
       Double mafPay = this.getMafPay();
       i1 = (i1 + i) * 59;
       i = (mafPay == null)? 43: mafPay.hashCode();
       Double civilServant = this.getCivilServantMafPay();
       i1 = (i1 + i) * 59;
       i = (civilServant == null)? 43: civilServant.hashCode();
       Double largeAmountP = this.getLargeAmountPay();
       i1 = (i1 + i) * 59;
       i = (largeAmountP == null)? 43: largeAmountP.hashCode();
       Double enterprisePa = this.getEnterprisePay();
       i1 = (i1 + i) * 59;
       i = (enterprisePa == null)? 43: enterprisePa.hashCode();
       Double cashPayAmt = this.getCashPayAmt();
       i1 = (i1 + i) * 59;
       i = (cashPayAmt == null)? 43: cashPayAmt.hashCode();
       Double acctPay = this.getAcctPay();
       i1 = (i1 + i) * 59;
       i = (acctPay == null)? 43: acctPay.hashCode();
       Double psnPay = this.getPsnPay();
       i1 = (i1 + i) * 59;
       i = (psnPay == null)? 43: psnPay.hashCode();
       Double psnPayAmt = this.getPsnPayAmt();
       i1 = (i1 + i) * 59;
       i = (psnPayAmt == null)? 43: psnPayAmt.hashCode();
       Double inScpAmt = this.getInScpAmt();
       i1 = (i1 + i) * 59;
       i = (inScpAmt == null)? 43: inScpAmt.hashCode();
       String admDiseaseCo = this.getAdmDiseaseCode();
       i1 = (i1 + i) * 59;
       i = (admDiseaseCo == null)? 43: admDiseaseCo.hashCode();
       String admDiseaseNa = this.getAdmDiseaseName();
       i1 = (i1 + i) * 59;
       i = (admDiseaseNa == null)? 43: admDiseaseNa.hashCode();
       String dsgDiseaseCo = this.getDsgDiseaseCode();
       i1 = (i1 + i) * 59;
       i = (dsgDiseaseCo == null)? 43: dsgDiseaseCo.hashCode();
       String dsgDiseaseNa = this.getDsgDiseaseName();
       i1 = (i1 + i) * 59;
       i = (dsgDiseaseNa == null)? 43: dsgDiseaseNa.hashCode();
       String medicalInsur = this.getMedicalInsurancePayType();
       i1 = (i1 + i) * 59;
       i = (medicalInsur == null)? 43: medicalInsur.hashCode();
       String billingDeptC = this.getBillingDeptCode();
       i1 = (i1 + i) * 59;
       i = (billingDeptC == null)? 43: billingDeptC.hashCode();
       String billingDeptN = this.getBillingDeptName();
       i1 = (i1 + i) * 59;
       i = (billingDeptN == null)? 43: billingDeptN.hashCode();
       String billingPdrCo = this.getBillingPdrCode();
       i1 = (i1 + i) * 59;
       i = (billingPdrCo == null)? 43: billingPdrCo.hashCode();
       String billingPdrNa = this.getBillingPdrName();
       i1 = (i1 + i) * 59;
       i = (billingPdrNa == null)? 43: billingPdrNa.hashCode();
       String medType = this.getMedType();
       i1 = (i1 + i) * 59;
       i = (medType == null)? 43: medType.hashCode();
       LocalDateTime feeTime = this.getFeeTime();
       i1 = (i1 + i) * 59;
       i = (feeTime == null)? 43: feeTime.hashCode();
       String hospitalItem = this.getHospitalItemCode();
       i1 = (i1 + i) * 59;
       i = (hospitalItem == null)? 43: hospitalItem.hashCode();
       String hospitalItem1 = this.getHospitalItemName();
       i1 = (i1 + i) * 59;
       i = (hospitalItem1 == null)? 43: hospitalItem1.hashCode();
       String medicalInsur1 = this.getMedicalInsuranceItemCode();
       i1 = (i1 + i) * 59;
       i = (medicalInsur1 == null)? 43: medicalInsur1.hashCode();
       String medicalInsur2 = this.getMedicalInsuranceItemName();
       i1 = (i1 + i) * 59;
       i = (medicalInsur2 == null)? 43: medicalInsur2.hashCode();
       String spec = this.getSpec();
       i1 = (i1 + i) * 59;
       i = (spec == null)? 43: spec.hashCode();
       String dosageFrom = this.getDosageFrom();
       i1 = (i1 + i) * 59;
       i = (dosageFrom == null)? 43: dosageFrom.hashCode();
       String packageUnit = this.getPackageUnit();
       i1 = (i1 + i) * 59;
       i = (packageUnit == null)? 43: packageUnit.hashCode();
       Double price = this.getPrice();
       i1 = (i1 + i) * 59;
       i = (price == null)? 43: price.hashCode();
       Double cnt = this.getCnt();
       i1 = (i1 + i) * 59;
       i = (cnt == null)? 43: cnt.hashCode();
       Double amt = this.getAmt();
       i1 = (i1 + i) * 59;
       i = (amt == null)? 43: amt.hashCode();
       Double medicalInsur3 = this.getMedicalInsuranceAmt();
       i1 = (i1 + i) * 59;
       i = (medicalInsur3 == null)? 43: medicalInsur3.hashCode();
       String payType = this.getPayType();
       i1 = (i1 + i) * 59;
       i = (payType == null)? 43: payType.hashCode();
       String reimbursemen = this.getReimbursement();
       i1 = (i1 + i) * 59;
       i = (reimbursemen == null)? 43: reimbursemen.hashCode();
       Double useCnt = this.getUseCnt();
       i1 = (i1 + i) * 59;
       i = (useCnt == null)? 43: useCnt.hashCode();
       Double useAmt = this.getUseAmt();
       i1 = (i1 + i) * 59;
       i = (useAmt == null)? 43: useAmt.hashCode();
       Double beyondCnt = this.getBeyondCnt();
       i1 = (i1 + i) * 59;
       i = (beyondCnt == null)? 43: beyondCnt.hashCode();
       Double beyondAmt = this.getBeyondAmt();
       i1 = (i1 + i) * 59;
       i = (beyondAmt == null)? 43: beyondAmt.hashCode();
       String icCard = this.getIcCard();
       i1 = (i1 + i) * 59;
       i = (icCard == null)? 43: icCard.hashCode();
       String mainOp = this.getMainOp();
       i1 = (i1 + i) * 59;
       i = (mainOp == null)? 43: mainOp.hashCode();
       String subOp = this.getSubOp();
       i1 = (i1 + i) * 59;
       i = (subOp == null)? 43: subOp.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String ruleName = this.getRuleName();
       i1 = (i1 + i) * 59;
       i = (ruleName == null)? 43: ruleName.hashCode();
       String datasource = this.getDatasource();
       i1 = (i1 + i) * 59;
       i = (datasource == null)? 43: datasource.hashCode();
       String ruleId = this.getRuleId();
       i1 = (i1 + i) * 59;
       i = (ruleId == null)? 43: ruleId.hashCode();
       String ruleConnotat = this.getRuleConnotation();
       i1 = (i1 + i) * 59;
       i = (ruleConnotat == null)? 43: ruleConnotat.hashCode();
       String ruleSql = this.getRuleSql();
       i1 = (i1 + i) * 59;
       i = (ruleSql == null)? 43: ruleSql.hashCode();
       String alternateFie = this.getAlternateFieldOne();
       i1 = (i1 + i) * 59;
       i = (alternateFie == null)? 43: alternateFie.hashCode();
       String alternateFie1 = this.getAlternateFieldTwo();
       i1 = (i1 + i) * 59;
       i = (alternateFie1 == null)? 43: alternateFie1.hashCode();
       String alternateFie2 = this.getAlternateFieldThree();
       i1 = (i1 + i) * 59;
       i = (alternateFie2 == null)? 43: alternateFie2.hashCode();
       String alternateFie3 = this.getAlternateFieldFour();
       i1 = (i1 + i) * 59;
       i = (alternateFie3 == null)? 43: alternateFie3.hashCode();
       String alternateFie4 = this.getAlternateFieldFive();
       i1 = (i1 + i) * 59;
       i = (alternateFie4 == null)? 43: alternateFie4.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       return (i1 + i);
    }
    public void setAcctPay(Double acctPay){
       this.acctPay = acctPay;
    }
    public void setAdmDate(LocalDateTime admDate){
       this.admDate = admDate;
    }
    public void setAdmDeptName(String admDeptName){
       this.admDeptName = admDeptName;
    }
    public void setAdmDiseaseCode(String admDiseaseCode){
       this.admDiseaseCode = admDiseaseCode;
    }
    public void setAdmDiseaseName(String admDiseaseName){
       this.admDiseaseName = admDiseaseName;
    }
    public void setAge(String age){
       this.age = age;
    }
    public void setAlternateFieldFive(String alternateFieldFive){
       this.alternateFieldFive = alternateFieldFive;
    }
    public void setAlternateFieldFour(String alternateFieldFour){
       this.alternateFieldFour = alternateFieldFour;
    }
    public void setAlternateFieldOne(String alternateFieldOne){
       this.alternateFieldOne = alternateFieldOne;
    }
    public void setAlternateFieldThree(String alternateFieldThree){
       this.alternateFieldThree = alternateFieldThree;
    }
    public void setAlternateFieldTwo(String alternateFieldTwo){
       this.alternateFieldTwo = alternateFieldTwo;
    }
    public void setAmt(Double amt){
       this.amt = amt;
    }
    public void setBeyondAmt(Double beyondAmt){
       this.beyondAmt = beyondAmt;
    }
    public void setBeyondCnt(Double beyondCnt){
       this.beyondCnt = beyondCnt;
    }
    public void setBillingDeptCode(String billingDeptCode){
       this.billingDeptCode = billingDeptCode;
    }
    public void setBillingDeptName(String billingDeptName){
       this.billingDeptName = billingDeptName;
    }
    public void setBillingPdrCode(String billingPdrCode){
       this.billingPdrCode = billingPdrCode;
    }
    public void setBillingPdrName(String billingPdrName){
       this.billingPdrName = billingPdrName;
    }
    public void setBirthDate(String birthDate){
       this.birthDate = birthDate;
    }
    public void setBridgeId(String bridgeId){
       this.bridgeId = bridgeId;
    }
    public void setCaseNum(String caseNum){
       this.caseNum = caseNum;
    }
    public void setCashPayAmt(Double cashPayAmt){
       this.cashPayAmt = cashPayAmt;
    }
    public void setCertNo(String certNo){
       this.certNo = certNo;
    }
    public void setChfPdrCode(String chfPdrCode){
       this.chfPdrCode = chfPdrCode;
    }
    public void setChfPdrName(String chfPdrName){
       this.chfPdrName = chfPdrName;
    }
    public void setCivilServantMafPay(Double civilServantMafPay){
       this.civilServantMafPay = civilServantMafPay;
    }
    public void setClaimType(String claimType){
       this.claimType = claimType;
    }
    public void setCnt(Double cnt){
       this.cnt = cnt;
    }
    public void setCoordinatePay(Double coordinatePay){
       this.coordinatePay = coordinatePay;
    }
    public void setCriticalIllnessInsurance(Double criticalIllnessInsurance){
       this.criticalIllnessInsurance = criticalIllnessInsurance;
    }
    public void setDatasource(String datasource){
       this.datasource = datasource;
    }
    public void setDosageFrom(String dosageFrom){
       this.dosageFrom = dosageFrom;
    }
    public void setDsgDate(LocalDateTime dsgDate){
       this.dsgDate = dsgDate;
    }
    public void setDsgDeptName(String dsgDeptName){
       this.dsgDeptName = dsgDeptName;
    }
    public void setDsgDiseaseCode(String dsgDiseaseCode){
       this.dsgDiseaseCode = dsgDiseaseCode;
    }
    public void setDsgDiseaseName(String dsgDiseaseName){
       this.dsgDiseaseName = dsgDiseaseName;
    }
    public void setDsgWay(String dsgWay){
       this.dsgWay = dsgWay;
    }
    public void setEmpName(String empName){
       this.empName = empName;
    }
    public void setEnterprisePay(Double enterprisePay){
       this.enterprisePay = enterprisePay;
    }
    public void setFeeTime(LocalDateTime feeTime){
       this.feeTime = feeTime;
    }
    public void setGender(String gender){
       this.gender = gender;
    }
    public void setHospLv(String hospLv){
       this.hospLv = hospLv;
    }
    public void setHospitalItemCode(String hospitalItemCode){
       this.hospitalItemCode = hospitalItemCode;
    }
    public void setHospitalItemName(String hospitalItemName){
       this.hospitalItemName = hospitalItemName;
    }
    public void setIcCard(String icCard){
       this.icCard = icCard;
    }
    public void setIfLocalFlag(String ifLocalFlag){
       this.ifLocalFlag = ifLocalFlag;
    }
    public void setInScpAmt(Double inScpAmt){
       this.inScpAmt = inScpAmt;
    }
    public void setInsuranceType(String insuranceType){
       this.insuranceType = insuranceType;
    }
    public void setIptDays(String iptDays){
       this.iptDays = iptDays;
    }
    public void setIptOtpNo(String iptOtpNo){
       this.iptOtpNo = iptOtpNo;
    }
    public void setLargeAmountPay(Double largeAmountPay){
       this.largeAmountPay = largeAmountPay;
    }
    public void setMafPay(Double mafPay){
       this.mafPay = mafPay;
    }
    public void setMainOp(String mainOp){
       this.mainOp = mainOp;
    }
    public void setMedType(String medType){
       this.medType = medType;
    }
    public void setMedicalInstitutionCode(String medicalInstitutionCode){
       this.medicalInstitutionCode = medicalInstitutionCode;
    }
    public void setMedicalInstitutionName(String medicalInstitutionName){
       this.medicalInstitutionName = medicalInstitutionName;
    }
    public void setMedicalInsuranceAmt(Double medicalInsuranceAmt){
       this.medicalInsuranceAmt = medicalInsuranceAmt;
    }
    public void setMedicalInsuranceItemCode(String medicalInsuranceItemCode){
       this.medicalInsuranceItemCode = medicalInsuranceItemCode;
    }
    public void setMedicalInsuranceItemName(String medicalInsuranceItemName){
       this.medicalInsuranceItemName = medicalInsuranceItemName;
    }
    public void setMedicalInsurancePayType(String medicalInsurancePayType){
       this.medicalInsurancePayType = medicalInsurancePayType;
    }
    public void setMonth(String month){
       this.month = month;
    }
    public void setPackageUnit(String packageUnit){
       this.packageUnit = packageUnit;
    }
    public void setPayType(String payType){
       this.payType = payType;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPrice(Double price){
       this.price = price;
    }
    public void setPsnName(String psnName){
       this.psnName = psnName;
    }
    public void setPsnPay(Double psnPay){
       this.psnPay = psnPay;
    }
    public void setPsnPayAmt(Double psnPayAmt){
       this.psnPayAmt = psnPayAmt;
    }
    public void setPsnType(String psnType){
       this.psnType = psnType;
    }
    public void setPsoNo(String psoNo){
       this.psoNo = psoNo;
    }
    public void setReimbursement(String reimbursement){
       this.reimbursement = reimbursement;
    }
    public void setRuleConnotation(String ruleConnotation){
       this.ruleConnotation = ruleConnotation;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSetlId(String setlId){
       this.setlId = setlId;
    }
    public void setSetlTime(LocalDateTime setlTime){
       this.setlTime = setlTime;
    }
    public void setSpec(String spec){
       this.spec = spec;
    }
    public void setSubOp(String subOp){
       this.subOp = subOp;
    }
    public void setTotalMedicalExpenses(Double totalMedicalExpenses){
       this.totalMedicalExpenses = totalMedicalExpenses;
    }
    public void setTransferDeptName(String transferDeptName){
       this.transferDeptName = transferDeptName;
    }
    public void setUseAmt(Double useAmt){
       this.useAmt = useAmt;
    }
    public void setUseCnt(Double useCnt){
       this.useCnt = useCnt;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "MergeInfoVO\(bridgeId="+this.getBridgeId()+", setlId="+this.getSetlId()+", medicalInstitutionCode="+this.getMedicalInstitutionCode()+", medicalInstitutionName="+this.getMedicalInstitutionName()+", hospLv="+this.getHospLv()+", setlTime="+this.getSetlTime()+", year="+this.getYear()+", month="+this.getMonth()+", iptOtpNo="+this.getIptOtpNo()+", psoNo="+this.getPsoNo()+", certNo="+this.getCertNo()+", caseNum="+this.getCaseNum()+", insuranceType="+this.getInsuranceType()+", psnType="+this.getPsnType()+", admDeptName="+this.getAdmDeptName()+", transferDeptName="+this.getTransferDeptName()+", dsgDeptName="+this.getDsgDeptName()+", chfPdrCode="+this.getChfPdrCode()+", chfPdrName="+this.getChfPdrName()+", psnName="+this.getPsnName()+", gender="+this.getGender()+", birthDate="+this.getBirthDate()+", age="+this.getAge()+", empName="+this.getEmpName()+", claimType="+this.getClaimType()+", ifLocalFlag="+this.getIfLocalFlag()+", admDate="+this.getAdmDate()+", dsgDate="+this.getDsgDate()+", iptDays="+this.getIptDays()+", dsgWay="+this.getDsgWay()+", totalMedicalExpenses="+this.getTotalMedicalExpenses()+", coordinatePay="+this.getCoordinatePay()+", criticalIllnessInsurance="+this.getCriticalIllnessInsurance()+", mafPay="+this.getMafPay()+", civilServantMafPay="+this.getCivilServantMafPay()+", largeAmountPay="+this.getLargeAmountPay()+", enterprisePay="+this.getEnterprisePay()+", cashPayAmt="+this.getCashPayAmt()+", acctPay="+this.getAcctPay()+", psnPay="+this.getPsnPay()+", psnPayAmt="+this.getPsnPayAmt()+", inScpAmt="+this.getInScpAmt()+", admDiseaseCode="+this.getAdmDiseaseCode()+", admDiseaseName="+this.getAdmDiseaseName()+", dsgDiseaseCode="+this.getDsgDiseaseCode()+", dsgDiseaseName="+this.getDsgDiseaseName()+", medicalInsurancePayType="+this.getMedicalInsurancePayType()+", billingDeptCode="+this.getBillingDeptCode()+", billingDeptName="+this.getBillingDeptName()+", billingPdrCode="+this.getBillingPdrCode()+", billingPdrName="+this.getBillingPdrName()+", medType="+this.getMedType()+"
    , feeTime="+this.getFeeTime()+", hospitalItemCode="+this.getHospitalItemCode()+", hospitalItemName="+this.getHospitalItemName()+", medicalInsuranceItemCode="+this.getMedicalInsuranceItemCode()+", medicalInsuranceItemName="+this.getMedicalInsuranceItemName()+", spec="+this.getSpec()+", dosageFrom="+this.getDosageFrom()+", packageUnit="+this.getPackageUnit()+", price="+this.getPrice()+", cnt="+this.getCnt()+", amt="+this.getAmt()+", medicalInsuranceAmt="+this.getMedicalInsuranceAmt()+", payType="+this.getPayType()+", reimbursement="+this.getReimbursement()+", useCnt="+this.getUseCnt()+", useAmt="+this.getUseAmt()+", beyondCnt="+this.getBeyondCnt()+", beyondAmt="+this.getBeyondAmt()+", icCard="+this.getIcCard()+", mainOp="+this.getMainOp()+", subOp="+this.getSubOp()+", ruleType="+this.getRuleType()+", ruleName="+this.getRuleName()+", datasource="+this.getDatasource()+", ruleId="+this.getRuleId()+", ruleConnotation="+this.getRuleConnotation()+", ruleSql="+this.getRuleSql()+", alternateFieldOne="+this.getAlternateFieldOne()+", alternateFieldTwo="+this.getAlternateFieldTwo()+", alternateFieldThree="+this.getAlternateFieldThree()+", alternateFieldFour="+this.getAlternateFieldFour()+", alternateFieldFive="+this.getAlternateFieldFive()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
