package com.taikang.fly.check.dto.mapstruct.ClickhouseFlyRuleTemplateMappingImpl;
import com.taikang.fly.check.dto.mapstruct.ClickhouseFlyRuleTemplateMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleTemplate;
import java.lang.String;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.util.Date;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRuleTemplate;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleTemplateRespDto;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZoneId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateRespDto;
import com.taikang.fly.check.utils.SequenceGenerator;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateEditDto;

public class ClickhouseFlyRuleTemplateMappingImpl implements ClickhouseFlyRuleTemplateMapping	// class@000143 from classes.dex
{

    public void ClickhouseFlyRuleTemplateMappingImpl(){
       super();
    }
    public FlyRuleTemplate addDtoToDomain(FlyRuleTemplateAddDto addDto){
       FlyRuleTemplate uFlyRuleTemp;
       if (addDto == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplate();
          uFlyRuleTemp.setRuleLevel(addDto.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(addDto.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(addDto.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(addDto.getDiagnosisType());
          uFlyRuleTemp.setRuleName(addDto.getRuleName());
          uFlyRuleTemp.setRuleDescribe(addDto.getRuleDescribe());
          uFlyRuleTemp.setPs(addDto.getPs());
          uFlyRuleTemp.setSqlTemplate(addDto.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(addDto.getSqlExample());
          uFlyRuleTemp.setParameter(addDto.getParameter());
          uFlyRuleTemp.setPolicyBasis(addDto.getPolicyBasis());
          uFlyRuleTemp.setCreater(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRuleTemp.setCreatedTime(new Date());
          uFlyRuleTemp.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRuleTemp.setOperateTime(new Date());
       }
       return uFlyRuleTemp;
    }
    public ClickhouseFlyRuleTemplateRespDto domainToInfoDto(ClickhouseFlyRuleTemplate domain){
       ClickhouseFlyRuleTemplateRespDto uClickhouseF;
       if (domain == null) {
          uClickhouseF = null;
       }else {
          uClickhouseF = new ClickhouseFlyRuleTemplateRespDto();
          uClickhouseF.setRuleName(domain.getRuleName());
          uClickhouseF.setRuleLevel(domain.getRuleLevel());
          uClickhouseF.setRuleCategory1(domain.getRuleCategory1());
          uClickhouseF.setRuleCategory2(domain.getRuleCategory2());
          uClickhouseF.setDiagnosisType(domain.getDiagnosisType());
          uClickhouseF.setRuleDescribe(domain.getRuleDescribe());
          uClickhouseF.setPs(domain.getPs());
          uClickhouseF.setSqlTemplate(domain.getSqlTemplate());
          uClickhouseF.setSqlExample(domain.getSqlExample());
          uClickhouseF.setParameter(domain.getParameter());
          uClickhouseF.setCreater(domain.getCreater());
          uClickhouseF.setOperator(domain.getOperator());
          if (domain.getOperateTime() != null) {
             uClickhouseF.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             uClickhouseF.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          uClickhouseF.setId(domain.getId());
          uClickhouseF.setRemoved(domain.getRemoved());
       }
       return uClickhouseF;
    }
    public FlyRuleTemplateAddDto domainToaddDto(FlyRuleTemplate flyRuleTemplate){
       FlyRuleTemplateAddDto uFlyRuleTemp;
       if (flyRuleTemplate == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplateAddDto();
          uFlyRuleTemp.setRuleName(flyRuleTemplate.getRuleName());
          uFlyRuleTemp.setRuleLevel(flyRuleTemplate.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(flyRuleTemplate.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(flyRuleTemplate.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(flyRuleTemplate.getDiagnosisType());
          uFlyRuleTemp.setRuleDescribe(flyRuleTemplate.getRuleDescribe());
          uFlyRuleTemp.setPs(flyRuleTemplate.getPs());
          uFlyRuleTemp.setSqlTemplate(flyRuleTemplate.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(flyRuleTemplate.getSqlExample());
          uFlyRuleTemp.setParameter(flyRuleTemplate.getParameter());
          uFlyRuleTemp.setPolicyBasis(flyRuleTemplate.getPolicyBasis());
       }
       return uFlyRuleTemp;
    }
    public FlyRuleTemplate dtoToDomain(FlyRuleTemplateRespDto flyRuleTemplateRespDto){
       FlyRuleTemplate uFlyRuleTemp;
       if (flyRuleTemplateRespDto == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplate();
          uFlyRuleTemp.setRuleLevel(flyRuleTemplateRespDto.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(flyRuleTemplateRespDto.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(flyRuleTemplateRespDto.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(flyRuleTemplateRespDto.getDiagnosisType());
          uFlyRuleTemp.setRuleName(flyRuleTemplateRespDto.getRuleName());
          uFlyRuleTemp.setRuleDescribe(flyRuleTemplateRespDto.getRuleDescribe());
          uFlyRuleTemp.setPs(flyRuleTemplateRespDto.getPs());
          uFlyRuleTemp.setSqlTemplate(flyRuleTemplateRespDto.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(flyRuleTemplateRespDto.getSqlExample());
          uFlyRuleTemp.setParameter(flyRuleTemplateRespDto.getParameter());
          uFlyRuleTemp.setCreater(flyRuleTemplateRespDto.getCreater());
          uFlyRuleTemp.setOperator(flyRuleTemplateRespDto.getOperator());
          uFlyRuleTemp.setPolicyBasis(flyRuleTemplateRespDto.getPolicyBasis());
          uFlyRuleTemp.setRuleType(flyRuleTemplateRespDto.getRuleType());
          uFlyRuleTemp.setIsSpecial(flyRuleTemplateRespDto.getIsSpecial());
          uFlyRuleTemp.setRuleSuitTimeStart(flyRuleTemplateRespDto.getRuleSuitTimeStart());
          uFlyRuleTemp.setRuleSuitTimeEnd(flyRuleTemplateRespDto.getRuleSuitTimeEnd());
          uFlyRuleTemp.setRuleDimension(flyRuleTemplateRespDto.getRuleDimension());
          uFlyRuleTemp.setCreatedTime(new Date());
          uFlyRuleTemp.setId(SequenceGenerator.getId());
          uFlyRuleTemp.setRemoved("1");
          uFlyRuleTemp.setOperateTime(new Date());
       }
       return uFlyRuleTemp;
    }
    public List dtoToDomains(List flyRuleTemplateRespDtos){
       List list;
       if (flyRuleTemplateRespDtos == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplateRespDtos.size());
          Iterator iterator = flyRuleTemplateRespDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.dtoToDomain(iterator.next()));
          }
       }
       return list;
    }
    public FlyRuleTemplate editDtoToDomain(FlyRuleTemplateEditDto editDto,FlyRuleTemplate domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleName(editDto.getRuleName());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setPs(editDto.getPs());
          domain.setSqlTemplate(editDto.getSqlTemplate());
          domain.setSqlExample(editDto.getSqlExample());
          domain.setParameter(editDto.getParameter());
          domain.setPolicyBasis(editDto.getPolicyBasis());
          domain.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public List entityToDtos(List flyRuleTemplate){
       List list;
       if (flyRuleTemplate == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplate.size());
          Iterator iterator = flyRuleTemplate.iterator();
          while (iterator.hasNext()) {
             list.add(this.flyRuleTemplateToFlyRuleTemplateRespDto(iterator.next()));
          }
       }
       return list;
    }
    protected FlyRuleTemplateRespDto flyRuleTemplateToFlyRuleTemplateRespDto(FlyRuleTemplate flyRuleTemplate){
       FlyRuleTemplateRespDto uFlyRuleTemp;
       if (flyRuleTemplate == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplateRespDto();
          uFlyRuleTemp.setRuleName(flyRuleTemplate.getRuleName());
          uFlyRuleTemp.setRuleLevel(flyRuleTemplate.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(flyRuleTemplate.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(flyRuleTemplate.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(flyRuleTemplate.getDiagnosisType());
          uFlyRuleTemp.setRuleDescribe(flyRuleTemplate.getRuleDescribe());
          uFlyRuleTemp.setPs(flyRuleTemplate.getPs());
          uFlyRuleTemp.setSqlTemplate(flyRuleTemplate.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(flyRuleTemplate.getSqlExample());
          uFlyRuleTemp.setParameter(flyRuleTemplate.getParameter());
          uFlyRuleTemp.setCreater(flyRuleTemplate.getCreater());
          uFlyRuleTemp.setOperator(flyRuleTemplate.getOperator());
          if (flyRuleTemplate.getOperateTime() != null) {
             uFlyRuleTemp.setOperateTime(LocalDateTime.ofInstant(flyRuleTemplate.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (flyRuleTemplate.getCreatedTime() != null) {
             uFlyRuleTemp.setCreatedTime(LocalDateTime.ofInstant(flyRuleTemplate.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          uFlyRuleTemp.setId(flyRuleTemplate.getId());
          uFlyRuleTemp.setRemoved(flyRuleTemplate.getRemoved());
          uFlyRuleTemp.setPolicyBasis(flyRuleTemplate.getPolicyBasis());
          uFlyRuleTemp.setRuleType(flyRuleTemplate.getRuleType());
          uFlyRuleTemp.setIsSpecial(flyRuleTemplate.getIsSpecial());
          uFlyRuleTemp.setRuleSuitTimeStart(flyRuleTemplate.getRuleSuitTimeStart());
          uFlyRuleTemp.setRuleSuitTimeEnd(flyRuleTemplate.getRuleSuitTimeEnd());
          uFlyRuleTemp.setRuleDimension(flyRuleTemplate.getRuleDimension());
       }
       return uFlyRuleTemp;
    }
}
