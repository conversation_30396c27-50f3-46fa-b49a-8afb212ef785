package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseMultiTemplateParamXRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ClickhouseMultiTemplateParamXRespDto implements Serializable	// class@0000cd from classes.dex
{
    private String paramCode;
    private String paramDesc;
    private String paramName;
    private String paramRuleName;
    private String paramType;
    private String ruleSql;
    private String ruleparam;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseMultiTemplateParamXRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseMultiTemplateParamXRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseMultiTemplateParamXRespDto){
          b = false;
       }else {
          ClickhouseMultiTemplateParamXRespDto uClickhouseM = o;
          if (!uClickhouseM.canEqual(this)) {
             b = false;
          }else {
             String paramCode = this.getParamCode();
             String paramCode1 = uClickhouseM.getParamCode();
             if (paramCode == null) {
                if (paramCode1 != null) {
                   b = false;
                }
             }else if(paramCode.equals(paramCode1)){
             }
             String paramName = this.getParamName();
             String paramName1 = uClickhouseM.getParamName();
             if (paramName == null) {
                if (paramName1 != null) {
                   b = false;
                }
             }else if(paramName.equals(paramName1)){
             }
             String paramDesc = this.getParamDesc();
             String paramDesc1 = uClickhouseM.getParamDesc();
             if (paramDesc == null) {
                if (paramDesc1 != null) {
                   b = false;
                }
             }else if(paramDesc.equals(paramDesc1)){
             }
             String paramType = this.getParamType();
             String paramType1 = uClickhouseM.getParamType();
             if (paramType == null) {
                if (paramType1 != null) {
                   b = false;
                }
             }else if(paramType.equals(paramType1)){
             }
             String paramRuleNam = this.getParamRuleName();
             String paramRuleNam1 = uClickhouseM.getParamRuleName();
             if (paramRuleNam == null) {
                if (paramRuleNam1 != null) {
                   b = false;
                }
             }else if(paramRuleNam.equals(paramRuleNam1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = uClickhouseM.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             String ruleparam = this.getRuleparam();
             String ruleparam1 = uClickhouseM.getRuleparam();
             if (ruleparam == null) {
                if (ruleparam1 != null) {
                   b = false;
                }
             }else if(ruleparam.equals(ruleparam1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getParamCode(){
       return this.paramCode;
    }
    public String getParamDesc(){
       return this.paramDesc;
    }
    public String getParamName(){
       return this.paramName;
    }
    public String getParamRuleName(){
       return this.paramRuleName;
    }
    public String getParamType(){
       return this.paramType;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleparam(){
       return this.ruleparam;
    }
    public int hashCode(){
       String $paramCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($paramCode = this.getParamCode()) == null)? i: $paramCode.hashCode();
       result = i1 + 59;
       String $paramName = this.getParamName();
       int i2 = result * 59;
       i1 = ($paramName == null)? i: $paramName.hashCode();
       result = i2 + i1;
       String $paramDesc = this.getParamDesc();
       i2 = result * 59;
       i1 = ($paramDesc == null)? i: $paramDesc.hashCode();
       result = i2 + i1;
       String $paramType = this.getParamType();
       i2 = result * 59;
       i1 = ($paramType == null)? i: $paramType.hashCode();
       result = i2 + i1;
       String $paramRuleName = this.getParamRuleName();
       i2 = result * 59;
       i1 = ($paramRuleName == null)? i: $paramRuleName.hashCode();
       result = i2 + i1;
       String ruleSql = this.getRuleSql();
       i2 = result * 59;
       i1 = (ruleSql == null)? i: ruleSql.hashCode();
       String ruleparam = this.getRuleparam();
       i1 = (i2 + i1) * 59;
       if (ruleparam != null) {
          i = ruleparam.hashCode();
       }
       return (i1 + i);
    }
    public void setParamCode(String paramCode){
       this.paramCode = paramCode;
    }
    public void setParamDesc(String paramDesc){
       this.paramDesc = paramDesc;
    }
    public void setParamName(String paramName){
       this.paramName = paramName;
    }
    public void setParamRuleName(String paramRuleName){
       this.paramRuleName = paramRuleName;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleparam(String ruleparam){
       this.ruleparam = ruleparam;
    }
    public String toString(){
       return "ClickhouseMultiTemplateParamXRespDto\(paramCode="+this.getParamCode()+", paramName="+this.getParamName()+", paramDesc="+this.getParamDesc()+", paramType="+this.getParamType()+", paramRuleName="+this.getParamRuleName()+", ruleSql="+this.getRuleSql()+", ruleparam="+this.getRuleparam()+"\)";
    }
}
