package org.springframework.boot.loader.archive.ExplodedArchive$FileEntry;
import org.springframework.boot.loader.archive.Archive$Entry;
import java.lang.String;
import java.io.File;
import java.lang.Object;

class ExplodedArchive$FileEntry implements Archive$Entry	// class@00053d from classes.dex
{
    private final File file;
    private final String name;

    void ExplodedArchive$FileEntry(String name,File file){
       super();
       this.name = name;
       this.file = file;
    }
    public File getFile(){
       return this.file;
    }
    public String getName(){
       return this.name;
    }
    public boolean isDirectory(){
       return this.file.isDirectory();
    }
}
