package com.taikang.fly.check.rest.ResourceController;
import java.lang.Object;
import com.taikang.fly.check.dto.resource.ResourceAddDto;
import javax.servlet.http.HttpServletRequest;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.service.ResourceService;
import java.lang.String;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import com.taikang.fly.check.dto.resource.ResourceEditDto;
import java.util.List;
import java.lang.Boolean;

public class ResourceController	// class@0002aa from classes.dex
{
    private ResourceService resourceService;

    public void ResourceController(){
       super();
    }
    public RmpResponse addResource(ResourceAddDto resourceAddDto,HttpServletRequest request){
       this.resourceService.addResource(resourceAddDto);
       return RmpResponse.success();
    }
    public RmpResponse delResource(String resourceId){
       if (StringUtils.isBlank(resourceId)) {
          throw new BizException(ResponseCodeEnum.PARAMETER_ISNULL_ERROR);
       }
       this.resourceService.delResource1(resourceId);
       return RmpResponse.success();
    }
    public RmpResponse editResource(ResourceEditDto resourceEditDto,HttpServletRequest request){
       this.resourceService.editResource1(resourceEditDto);
       return RmpResponse.success();
    }
    public RmpResponse getAllResourceTrees(){
       List treeResDtos = this.resourceService.getAllResourceTrees();
       return RmpResponse.success(treeResDtos);
    }
    public RmpResponse getAllResourceTreesList(){
       List treeResDtos = this.resourceService.getAllResourceTreesList();
       return RmpResponse.success(treeResDtos);
    }
    public RmpResponse getUserResources(){
       List dtos = this.resourceService.getUserResources();
       return RmpResponse.success(dtos);
    }
    public RmpResponse getUserRoleResources(String path){
       boolean resourceExit = this.resourceService.getUserRoleResources(path);
       return RmpResponse.success(Boolean.valueOf(resourceExit));
    }
    public RmpResponse resourceIndex(){
       List resourceIndexDto = this.resourceService.getResourceTreeList();
       return RmpResponse.success(resourceIndexDto);
    }
    public RmpResponse resourceRIndex(){
       List resourceIndexRDto = this.resourceService.getResourceRTreeList();
       return RmpResponse.success(resourceIndexRDto);
    }
}
