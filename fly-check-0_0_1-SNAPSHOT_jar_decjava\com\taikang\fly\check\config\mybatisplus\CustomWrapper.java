package com.taikang.fly.check.config.mybatisplus.CustomWrapper;
import org.apache.ibatis.reflection.wrapper.MapWrapper;
import org.apache.ibatis.reflection.MetaObject;
import java.util.Map;
import java.lang.String;
import com.google.common.base.CaseFormat;

public class CustomWrapper extends MapWrapper	// class@000095 from classes.dex
{

    public void CustomWrapper(MetaObject metaObject,Map map){
       super(metaObject, map);
    }
    public String findProperty(String name,boolean useCamelCaseMapping){
       if (useCamelCaseMapping) {
          name = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, name);
       }
       return name;
    }
}
