package com.taikang.fly.check.dto.diagnosticencoding.DiagnosticSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DiagnosticSearchDto implements Serializable	// class@0000e3 from classes.dex
{
    private String diagnosisCode;
    private String diagnosisName;
    private static final long serialVersionUID = 0x1;

    public void DiagnosticSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DiagnosticSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DiagnosticSearchDto) {
             b = false;
          }else {
             DiagnosticSearchDto uDiagnosticS = o;
             if (!uDiagnosticS.canEqual(this)) {
                b = false;
             }else {
                String diagnosisCod = this.getDiagnosisCode();
                String diagnosisCod1 = uDiagnosticS.getDiagnosisCode();
                if (diagnosisCod == null) {
                   if (diagnosisCod1 != null) {
                      b = false;
                   }
                }else if(diagnosisCod.equals(diagnosisCod1)){
                }
                String diagnosisNam = this.getDiagnosisName();
                String diagnosisNam1 = uDiagnosticS.getDiagnosisName();
                if (diagnosisNam == null) {
                   if (diagnosisNam1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!diagnosisNam.equals(diagnosisNam1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDiagnosisCode(){
       return this.diagnosisCode;
    }
    public String getDiagnosisName(){
       return this.diagnosisName;
    }
    public int hashCode(){
       String $diagnosisCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($diagnosisCode = this.getDiagnosisCode()) == null)? i: $diagnosisCode.hashCode();
       result = i1 + 59;
       String $diagnosisName = this.getDiagnosisName();
       i1 = result * 59;
       if ($diagnosisName != null) {
          i = $diagnosisName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDiagnosisCode(String diagnosisCode){
       this.diagnosisCode = diagnosisCode;
    }
    public void setDiagnosisName(String diagnosisName){
       this.diagnosisName = diagnosisName;
    }
    public String toString(){
       return "DiagnosticSearchDto\(diagnosisCode="+this.getDiagnosisCode()+", diagnosisName="+this.getDiagnosisName()+"\)";
    }
}
