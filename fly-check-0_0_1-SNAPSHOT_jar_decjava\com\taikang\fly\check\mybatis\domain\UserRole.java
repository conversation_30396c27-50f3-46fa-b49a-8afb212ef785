package com.taikang.fly.check.mybatis.domain.UserRole;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class UserRole implements Serializable	// class@000274 from classes.dex
{
    private Date createTime;
    private String creator;
    private String id;
    private String isValid;
    private String modby;
    private Date modifyTime;
    private String roleCode;
    private String signature;
    private String userCode;

    public void UserRole(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserRole;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof UserRole){
          b = false;
       }else {
          UserRole userRole = o;
          if (!userRole.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = userRole.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String userCode = this.getUserCode();
             String userCode1 = userRole.getUserCode();
             if (userCode == null) {
                if (userCode1 != null) {
                   b = false;
                }
             }else if(userCode.equals(userCode1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = userRole.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String creator = this.getCreator();
             String creator1 = userRole.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = userRole.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_0087 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = userRole.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = userRole.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = userRole.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = userRole.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $userCode = this.getUserCode();
       int i2 = result * 59;
       i1 = ($userCode == null)? i: $userCode.hashCode();
       result = i2 + i1;
       String $roleCode = this.getRoleCode();
       i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i2 = (i2 + i1) * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String isValid = this.getIsValid();
       i1 = (i2 + i1) * 59;
       if (isValid != null) {
          i = isValid.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserRole\(id="+this.getId()+", userCode="+this.getUserCode()+", roleCode="+this.getRoleCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", isValid="+this.getIsValid()+"\)";
    }
}
