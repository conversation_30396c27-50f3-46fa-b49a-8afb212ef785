package com.taikang.fly.check.dto.mapstruct.ModuleMappingImpl;
import com.taikang.fly.check.dto.mapstruct.ModuleMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.module.ModuleRespAddDto;
import com.taikang.fly.check.mybatis.domain.Module;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.time.LocalDateTime;
import com.taikang.fly.check.dto.module.ModuleRespEditDto;
import com.taikang.fly.check.dto.module.ModuleIgnoreFiledRespDto;
import com.taikang.fly.check.utils.DateUtils;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.module.ModuleRespDto;

public class ModuleMappingImpl implements ModuleMapping	// class@00016c from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void ModuleMappingImpl(){
       super();
    }
    public Module moduleRespAddDtoToModule(ModuleRespAddDto moduleRespAddDto){
       Module module;
       if (moduleRespAddDto == null) {
          module = null;
       }else {
          module = new Module();
          module.setModuleOrder(this.typeConversionMapper.String2Integer(moduleRespAddDto.getModuleOrder()));
          module.setModuleType(moduleRespAddDto.getModuleType());
          module.setModuleCode(moduleRespAddDto.getModuleCode());
          module.setModuleName(moduleRespAddDto.getModuleName());
          module.setDescription(moduleRespAddDto.getDescription());
          module.setIcon(moduleRespAddDto.getIcon());
          module.setUrl(moduleRespAddDto.getUrl());
          module.setCreator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          module.setModifyTime(LocalDateTime.now());
          module.setModifier(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          module.setIsValid("1");
          module.setCreatedTime(LocalDateTime.now());
       }
       return module;
    }
    public Module moduleRespEditDtoToModule(ModuleRespEditDto moduleRespEditDto){
       Module module;
       if (moduleRespEditDto == null) {
          module = null;
       }else {
          module = new Module();
          module.setModuleType(moduleRespEditDto.getModuleType());
          module.setModuleCode(moduleRespEditDto.getModuleCode());
          module.setModuleName(moduleRespEditDto.getModuleName());
          if (moduleRespEditDto.getModuleOrder() != null) {
             module.setModuleOrder(Integer.valueOf(Integer.parseInt(moduleRespEditDto.getModuleOrder())));
          }
          module.setDescription(moduleRespEditDto.getDescription());
          module.setIcon(moduleRespEditDto.getIcon());
          module.setUrl(moduleRespEditDto.getUrl());
          module.setModifyTime(LocalDateTime.now());
          module.setModifier(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          module.setIsValid("1");
       }
       return module;
    }
    public ModuleIgnoreFiledRespDto moduleToModuleIgnoreFieldRespDto(Module module){
       ModuleIgnoreFiledRespDto moduleIgnore;
       if (module == null) {
          moduleIgnore = null;
       }else {
          moduleIgnore = new ModuleIgnoreFiledRespDto();
          moduleIgnore.setModuleCode(module.getModuleCode());
          moduleIgnore.setModuleName(module.getModuleName());
          if (module.getModuleOrder() != null) {
             moduleIgnore.setModuleOrder(String.valueOf(module.getModuleOrder()));
          }
          moduleIgnore.setModuleType(module.getModuleType());
          moduleIgnore.setDescription(module.getDescription());
          moduleIgnore.setIcon(module.getIcon());
          moduleIgnore.setUrl(module.getUrl());
          moduleIgnore.setCreator(module.getCreator());
          moduleIgnore.setModifier(module.getModifier());
          moduleIgnore.setIsValid(module.getIsValid());
          moduleIgnore.setCreatedTime(DateUtils.formatLocalDateTime(module.getCreatedTime()));
          moduleIgnore.setModifyTime(DateUtils.formatLocalDateTime(module.getModifyTime()));
       }
       return moduleIgnore;
    }
    public List moduleToModuleIgnoreFieldRespDtos(List module){
       List list;
       if (module == null) {
          list = null;
       }else {
          list = new ArrayList(module.size());
          Iterator iterator = module.iterator();
          while (iterator.hasNext()) {
             list.add(this.moduleToModuleIgnoreFieldRespDto(iterator.next()));
          }
       }
       return list;
    }
    public ModuleRespDto moduleToModuleRespDto(Module module){
       ModuleRespDto moduleRespDt;
       if (module == null) {
          moduleRespDt = null;
       }else {
          moduleRespDt = new ModuleRespDto();
          moduleRespDt.setModuleCode(module.getModuleCode());
          moduleRespDt.setModuleName(module.getModuleName());
          if (module.getModuleOrder() != null) {
             moduleRespDt.setModuleOrder(String.valueOf(module.getModuleOrder()));
          }
          moduleRespDt.setModuleType(module.getModuleType());
          moduleRespDt.setDescription(module.getDescription());
          moduleRespDt.setIcon(module.getIcon());
          moduleRespDt.setUrl(module.getUrl());
          moduleRespDt.setCreator(module.getCreator());
          moduleRespDt.setModifier(module.getModifier());
          moduleRespDt.setIsValid(module.getIsValid());
          moduleRespDt.setCreatedTime(DateUtils.formatLocalDateTime(module.getCreatedTime()));
          moduleRespDt.setModifyTime(DateUtils.formatLocalDateTime(module.getModifyTime()));
       }
       return moduleRespDt;
    }
    public List usersToUserDtos(List module){
       List list;
       if (module == null) {
          list = null;
       }else {
          list = new ArrayList(module.size());
          Iterator iterator = module.iterator();
          while (iterator.hasNext()) {
             list.add(this.moduleToModuleRespDto(iterator.next()));
          }
       }
       return list;
    }
}
