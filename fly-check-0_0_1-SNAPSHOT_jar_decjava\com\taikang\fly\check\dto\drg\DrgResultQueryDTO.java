package com.taikang.fly.check.dto.drg.DrgResultQueryDTO;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgResultQueryDTO	// class@0000ee from classes.dex
{
    private String departmentName;
    private String drgCode;
    private String ruleName;
    private String setlDateEnd;
    private String setlDateStart;

    public void DrgResultQueryDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgResultQueryDTO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgResultQueryDTO) {
             b = false;
          }else {
             DrgResultQueryDTO uDrgResultQu = o;
             if (!uDrgResultQu.canEqual(this)) {
                b = false;
             }else {
                String ruleName = this.getRuleName();
                String ruleName1 = uDrgResultQu.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgResultQu.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String departmentNa = this.getDepartmentName();
                String departmentNa1 = uDrgResultQu.getDepartmentName();
                if (departmentNa == null) {
                   if (departmentNa1 != null) {
                      b = false;
                   }
                }else if(departmentNa.equals(departmentNa1)){
                }
                String setlDateStar = this.getSetlDateStart();
                String setlDateStar1 = uDrgResultQu.getSetlDateStart();
                if (setlDateStar == null) {
                   if (setlDateStar1 != null) {
                      b = false;
                   }
                }else if(setlDateStar.equals(setlDateStar1)){
                }
                String setlDateEnd = this.getSetlDateEnd();
                String setlDateEnd1 = uDrgResultQu.getSetlDateEnd();
                if (setlDateEnd == null) {
                   if (setlDateEnd1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!setlDateEnd.equals(setlDateEnd1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDepartmentName(){
       return this.departmentName;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSetlDateEnd(){
       return this.setlDateEnd;
    }
    public String getSetlDateStart(){
       return this.setlDateStart;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $drgCode = this.getDrgCode();
       int i2 = result * 59;
       i1 = ($drgCode == null)? i: $drgCode.hashCode();
       result = i2 + i1;
       String $departmentName = this.getDepartmentName();
       i2 = result * 59;
       i1 = ($departmentName == null)? i: $departmentName.hashCode();
       result = i2 + i1;
       String $setlDateStart = this.getSetlDateStart();
       i2 = result * 59;
       i1 = ($setlDateStart == null)? i: $setlDateStart.hashCode();
       result = i2 + i1;
       String $setlDateEnd = this.getSetlDateEnd();
       i1 = result * 59;
       if ($setlDateEnd != null) {
          i = $setlDateEnd.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDepartmentName(String departmentName){
       this.departmentName = departmentName;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSetlDateEnd(String setlDateEnd){
       this.setlDateEnd = setlDateEnd;
    }
    public void setSetlDateStart(String setlDateStart){
       this.setlDateStart = setlDateStart;
    }
    public String toString(){
       return "DrgResultQueryDTO\(ruleName="+this.getRuleName()+", drgCode="+this.getDrgCode()+", departmentName="+this.getDepartmentName()+", setlDateStart="+this.getSetlDateStart()+", setlDateEnd="+this.getSetlDateEnd()+"\)";
    }
}
