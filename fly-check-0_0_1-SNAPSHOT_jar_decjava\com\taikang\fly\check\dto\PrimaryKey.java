package com.taikang.fly.check.dto.PrimaryKey;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PrimaryKey	// class@0000a8 from classes.dex
{
    private String id;

    public void PrimaryKey(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PrimaryKey;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof PrimaryKey) {
             b = false;
          }else {
             PrimaryKey primaryKey = o;
             if (!primaryKey.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = primaryKey.getId();
                if (id == null) {
                   if (id1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!id.equals(id1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public String toString(){
       return "PrimaryKey\(id="+this.getId()+"\)";
    }
}
