package com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleTemplateSearchDto implements Serializable	// class@00011b from classes.dex
{
    private String diagnosisType;
    private String policyBasis;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleTemplateSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleTemplateSearchDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleTemplateSearchDto){
          b = false;
       }else {
          FlyRuleTemplateSearchDto uFlyRuleTemp = o;
          if (!uFlyRuleTemp.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleTemp.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleTemp.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uFlyRuleTemp.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uFlyRuleTemp.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleTemp.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uFlyRuleTemp.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleTemp.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_00b0 :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public int hashCode(){
       String $ruleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleName = this.getRuleName()) == null)? i: $ruleName.hashCode();
       result = i1 + 59;
       String $ruleLevel = this.getRuleLevel();
       int i2 = result * 59;
       i1 = ($ruleLevel == null)? i: $ruleLevel.hashCode();
       result = i2 + i1;
       String $ruleCategory1 = this.getRuleCategory1();
       i2 = result * 59;
       i1 = ($ruleCategory1 == null)? i: $ruleCategory1.hashCode();
       result = i2 + i1;
       String $ruleCategory2 = this.getRuleCategory2();
       i2 = result * 59;
       i1 = ($ruleCategory2 == null)? i: $ruleCategory2.hashCode();
       result = i2 + i1;
       String $diagnosisType = this.getDiagnosisType();
       i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       String ruleDescribe = this.getRuleDescribe();
       i2 = result * 59;
       i1 = (ruleDescribe == null)? i: ruleDescribe.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i2 + i1) * 59;
       if (policyBasis != null) {
          i = policyBasis.hashCode();
       }
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public String toString(){
       return "FlyRuleTemplateSearchDto\(ruleName="+this.getRuleName()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
