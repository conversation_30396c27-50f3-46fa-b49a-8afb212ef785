"""
创建示例数据
"""

from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from flycheck.database import get_db
from flycheck.models import (
    FlyRule, Plan, PlanRule, 
    DrugCatalogue, DiagnosisTreatment, ConsumablesList,
    SystemConfig
)


def create_seed_data():
    """创建示例数据"""
    db = next(get_db())
    
    try:
        # 创建示例规则
        create_sample_rules(db)
        
        # 创建示例计划
        create_sample_plans(db)
        
        # 创建医保基础数据
        create_medical_data(db)
        
        # 创建系统配置
        create_system_configs(db)
        
        db.commit()
        print("示例数据创建成功")
        
    except Exception as e:
        db.rollback()
        print(f"示例数据创建失败: {e}")
        raise
    finally:
        db.close()


def create_sample_rules(db: Session):
    """创建示例规则"""
    rules = [
        {
            "rule_name": "药品费用异常检查",
            "rule_describe": "检查单次药品费用是否超过合理范围",
            "rule_sql": """
                SELECT patient_id, drug_code, drug_name, amount, unit_price, total_fee
                FROM medical_records 
                WHERE total_fee > 1000 
                AND drug_type = '西药'
            """,
            "rule_category1": "费用检查",
            "rule_category2": "药品费用",
            "rule_type": "sql",
            "rule_level": "高",
            "policy_basis": "医保基金监管相关规定",
            "problem_description": "单次药品费用过高可能存在过度医疗",
            "state": "1"
        },
        {
            "rule_name": "重复用药检查",
            "rule_describe": "检查同一患者是否存在重复开药情况",
            "rule_sql": """
                SELECT patient_id, drug_code, drug_name, COUNT(*) as count
                FROM medical_records 
                WHERE visit_date >= CURRENT_DATE - INTERVAL 30 DAY
                GROUP BY patient_id, drug_code
                HAVING COUNT(*) > 3
            """,
            "rule_category1": "合理用药",
            "rule_category2": "重复用药",
            "rule_type": "sql",
            "rule_level": "中",
            "policy_basis": "合理用药管理办法",
            "problem_description": "短期内重复开药可能存在不合理用药",
            "state": "1"
        },
        {
            "rule_name": "诊断与用药匹配性检查",
            "rule_describe": "检查诊断与用药是否匹配",
            "rule_logic": """
# Python规则示例
import pandas as pd

# 获取诊断用药数据
df = pd.read_sql('''
    SELECT patient_id, diagnosis_code, drug_code, drug_name
    FROM medical_records mr
    JOIN diagnosis_drug_mapping ddm ON mr.diagnosis_code = ddm.diagnosis_code
    WHERE ddm.is_contraindicated = 1
''', duckdb_conn)

# 返回不匹配的记录
result = df[df['drug_code'].notna()]
            """,
            "rule_category1": "合理用药",
            "rule_category2": "诊药匹配",
            "rule_type": "python",
            "rule_level": "高",
            "policy_basis": "临床用药指南",
            "problem_description": "诊断与用药不匹配可能存在用药错误",
            "state": "1"
        }
    ]
    
    for rule_data in rules:
        existing = db.query(FlyRule).filter(FlyRule.rule_name == rule_data["rule_name"]).first()
        if not existing:
            rule = FlyRule(**rule_data)
            db.add(rule)


def create_sample_plans(db: Session):
    """创建示例计划"""
    plans = [
        {
            "plan_name": "月度飞行检查计划",
            "plan_description": "每月定期执行的飞行检查计划",
            "plan_type": "定期检查",
            "status": "active",
            "start_time": datetime.now(),
            "end_time": datetime.now() + timedelta(days=30),
            "data_source": "医保数据库",
            "data_range": "全市医疗机构"
        },
        {
            "plan_name": "专项药品检查计划",
            "plan_description": "针对高值药品的专项检查",
            "plan_type": "专项检查",
            "status": "draft",
            "data_source": "医保数据库",
            "data_range": "三级医院"
        }
    ]
    
    for plan_data in plans:
        existing = db.query(Plan).filter(Plan.plan_name == plan_data["plan_name"]).first()
        if not existing:
            plan = Plan(**plan_data)
            db.add(plan)


def create_medical_data(db: Session):
    """创建医保基础数据"""
    # 药品目录示例
    drugs = [
        {
            "drug_code": "A01AA01",
            "drug_name": "阿司匹林肠溶片",
            "drug_type": "西药",
            "specification": "100mg*30片",
            "dosage_form": "片剂",
            "manufacturer": "拜耳医药",
            "medical_insurance_code": "YB001",
            "payment_category": "甲类",
            "limit_price": 15.50,
            "status": "1"
        },
        {
            "drug_code": "A02BC01",
            "drug_name": "奥美拉唑肠溶胶囊",
            "drug_type": "西药",
            "specification": "20mg*14粒",
            "dosage_form": "胶囊",
            "manufacturer": "阿斯利康",
            "medical_insurance_code": "YB002",
            "payment_category": "乙类",
            "limit_price": 28.00,
            "status": "1"
        }
    ]
    
    for drug_data in drugs:
        existing = db.query(DrugCatalogue).filter(
            DrugCatalogue.drug_code == drug_data["drug_code"]
        ).first()
        if not existing:
            drug = DrugCatalogue(**drug_data)
            db.add(drug)
    
    # 诊疗项目示例
    treatments = [
        {
            "item_code": "T01001",
            "item_name": "血常规检查",
            "item_type": "检验",
            "unit": "次",
            "price": 25.00,
            "medical_insurance_code": "ZL001",
            "payment_category": "甲类",
            "status": "1"
        },
        {
            "item_code": "T02001",
            "item_name": "胸部CT检查",
            "item_type": "影像",
            "unit": "次",
            "price": 280.00,
            "medical_insurance_code": "ZL002",
            "payment_category": "乙类",
            "status": "1"
        }
    ]
    
    for treatment_data in treatments:
        existing = db.query(DiagnosisTreatment).filter(
            DiagnosisTreatment.item_code == treatment_data["item_code"]
        ).first()
        if not existing:
            treatment = DiagnosisTreatment(**treatment_data)
            db.add(treatment)


def create_system_configs(db: Session):
    """创建系统配置"""
    configs = [
        {
            "config_key": "system.name",
            "config_value": "FlyCheck Python",
            "config_type": "string",
            "description": "系统名称",
            "status": "1"
        },
        {
            "config_key": "rule.timeout",
            "config_value": "300",
            "config_type": "integer",
            "description": "规则执行超时时间(秒)",
            "status": "1"
        },
        {
            "config_key": "plan.max_concurrent",
            "config_value": "10",
            "config_type": "integer",
            "description": "最大并发执行计划数",
            "status": "1"
        }
    ]
    
    for config_data in configs:
        existing = db.query(SystemConfig).filter(
            SystemConfig.config_key == config_data["config_key"]
        ).first()
        if not existing:
            config = SystemConfig(**config_data)
            db.add(config)
