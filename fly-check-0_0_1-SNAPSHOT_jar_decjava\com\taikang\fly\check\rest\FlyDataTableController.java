package com.taikang.fly.check.rest.FlyDataTableController;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.TableResultService;
import org.springframework.util.StringUtils;
import java.util.List;

public class FlyDataTableController	// class@00028e from classes.dex
{
    private TableResultService tableResultService;

    public void FlyDataTableController(){
       super();
    }
    public CommResponse generate(String ids){
       String errorMsg = this.tableResultService.exec(ids);
       CommResponse uCommRespons = (StringUtils.isEmpty(errorMsg))? CommResponse.success(): CommResponse.error("-21", errorMsg, null);
       return uCommRespons;
    }
    public CommResponse list(){
       List tableResults = this.tableResultService.find();
       return CommResponse.success(tableResults);
    }
}
