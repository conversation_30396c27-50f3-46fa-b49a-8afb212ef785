package com.taikang.fly.check.vo.drg.DrgGroupTableFieldsVo;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class DrgGroupTableFieldsVo implements Serializable	// class@00036b from classes.dex
{
    private String DRGCode;
    private String DRGName;
    private String DRGPaySettleFee;
    private String DRGSLimitHigh;
    private String DRGSLimitLow;
    private String DRGSMainDiagCode;
    private String DRGSMainDiagName;
    private String RW;
    private String admDeptCode;
    private String admDeptName;
    private String age;
    private LocalDateTime beginTime;
    private String desgDeptCode;
    private String desgDeptName;
    private LocalDateTime endTime;
    private String fpxs;
    private String fyqz;
    private String gend;
    private String hifmiPay;
    private String insuType;
    private String iptDays;
    private String iptOtpNo;
    private String jzds;
    private String medFeeSumamt;
    private String medcasNo;
    private String oprnOprtCode;
    private String oprnOprtName;
    private String otherDiagnosticName;
    private String otherSurgicalName;
    private String perPointsFee;
    private String pointsAdditional;
    private String pointsDeduction;
    private String pointsMedical;
    private String pointsRate;
    private String psnName;
    private String psnNo;
    private String qzfl;
    private String setlId;
    private String socialId;
    private String totalProfitAndLoss;
    private String tzxs;
    private static final long serialVersionUID = 0x1;

    public void DrgGroupTableFieldsVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgGroupTableFieldsVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgGroupTableFieldsVo){
          b = false;
       }else {
          DrgGroupTableFieldsVo uDrgGroupTab = o;
          if (!uDrgGroupTab.canEqual(this)) {
             b = false;
          }else {
             String setlId = this.getSetlId();
             String setlId1 = uDrgGroupTab.getSetlId();
             if (setlId == null) {
                if (setlId1 != null) {
                   b = false;
                }
             }else if(setlId.equals(setlId1)){
             }
             String medcasNo = this.getMedcasNo();
             String medcasNo1 = uDrgGroupTab.getMedcasNo();
             if (medcasNo == null) {
                if (medcasNo1 != null) {
                   b = false;
                }
             }else if(medcasNo.equals(medcasNo1)){
             }
             String iptOtpNo = this.getIptOtpNo();
             String iptOtpNo1 = uDrgGroupTab.getIptOtpNo();
             if (iptOtpNo == null) {
                if (iptOtpNo1 != null) {
                   b = false;
                }
             }else if(iptOtpNo.equals(iptOtpNo1)){
             }
             String dRGPaySettle = this.getDRGPaySettleFee();
             String dRGPaySettle1 = uDrgGroupTab.getDRGPaySettleFee();
             if (dRGPaySettle == null) {
                if (dRGPaySettle1 != null) {
                   b = false;
                }
             }else if(dRGPaySettle.equals(dRGPaySettle1)){
             }
             String medFeeSumamt = this.getMedFeeSumamt();
             String medFeeSumamt1 = uDrgGroupTab.getMedFeeSumamt();
             if (medFeeSumamt == null) {
                if (medFeeSumamt1 != null) {
                   b = false;
                }
             }else if(medFeeSumamt.equals(medFeeSumamt1)){
             }
             String totalProfitA = this.getTotalProfitAndLoss();
             String totalProfitA1 = uDrgGroupTab.getTotalProfitAndLoss();
             if (totalProfitA == null) {
                if (totalProfitA1 != null) {
                   b = false;
                }
             }else if(totalProfitA.equals(totalProfitA1)){
             }
             String psnNo = this.getPsnNo();
             String psnNo1 = uDrgGroupTab.getPsnNo();
             if (psnNo == null) {
                if (psnNo1 != null) {
                label_00c5 :
                   b = false;
                }
             }else if(psnNo.equals(psnNo1)){
             }
             String psnName = this.getPsnName();
             String psnName1 = uDrgGroupTab.getPsnName();
             if (psnName == null) {
                if (psnName1 != null) {
                   b = false;
                }
             }else if(psnName.equals(psnName1)){
             }
             String gend = this.getGend();
             String gend1 = uDrgGroupTab.getGend();
             if (gend == null) {
                if (gend1 != null) {
                label_00f9 :
                   b = false;
                }
             }else if(gend.equals(gend1)){
             }
             String age = this.getAge();
             String age1 = uDrgGroupTab.getAge();
             if (age == null) {
                if (age1 != null) {
                   b = false;
                }
             }else if(age.equals(age1)){
             }
             LocalDateTime beginTime = this.getBeginTime();
             LocalDateTime beginTime1 = uDrgGroupTab.getBeginTime();
             if (beginTime == null) {
                if (beginTime1 != null) {
                   b = false;
                }
             }else if(beginTime.equals(beginTime1)){
             }
             LocalDateTime endTime = this.getEndTime();
             LocalDateTime endTime1 = uDrgGroupTab.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             String iptDays = this.getIptDays();
             String iptDays1 = uDrgGroupTab.getIptDays();
             if (iptDays == null) {
                if (iptDays1 != null) {
                label_015d :
                   b = false;
                }
             }else if(iptDays.equals(iptDays1)){
             }
             String admDeptCode = this.getAdmDeptCode();
             String admDeptCode1 = uDrgGroupTab.getAdmDeptCode();
             if (admDeptCode == null) {
                if (admDeptCode1 != null) {
                   b = false;
                }
             }else if(admDeptCode.equals(admDeptCode1)){
             }
             String admDeptName = this.getAdmDeptName();
             String admDeptName1 = uDrgGroupTab.getAdmDeptName();
             if (admDeptName == null) {
                if (admDeptName1 != null) {
                   b = false;
                }
             }else if(admDeptName.equals(admDeptName1)){
             }
             String desgDeptCode = this.getDesgDeptCode();
             String desgDeptCode1 = uDrgGroupTab.getDesgDeptCode();
             if (desgDeptCode == null) {
                if (desgDeptCode1 != null) {
                label_01a7 :
                   b = false;
                }
             }else if(desgDeptCode.equals(desgDeptCode1)){
             }
             String desgDeptName = this.getDesgDeptName();
             String desgDeptName1 = uDrgGroupTab.getDesgDeptName();
             if (desgDeptName == null) {
                if (desgDeptName1 != null) {
                   b = false;
                }
             }else if(desgDeptName.equals(desgDeptName1)){
             }
             String dRGSMainDiag = this.getDRGSMainDiagCode();
             String dRGSMainDiag1 = uDrgGroupTab.getDRGSMainDiagCode();
             if (dRGSMainDiag == null) {
                if (dRGSMainDiag1 != null) {
                label_01d9 :
                   b = false;
                }
             }else if(dRGSMainDiag.equals(dRGSMainDiag1)){
             }
             String dRGSMainDiag2 = this.getDRGSMainDiagName();
             String dRGSMainDiag3 = uDrgGroupTab.getDRGSMainDiagName();
             if (dRGSMainDiag2 == null) {
                if (dRGSMainDiag3 != null) {
                   b = false;
                }
             }else if(dRGSMainDiag2.equals(dRGSMainDiag3)){
             }
             String otherDiagnos = this.getOtherDiagnosticName();
             String otherDiagnos1 = uDrgGroupTab.getOtherDiagnosticName();
             if (otherDiagnos == null) {
                if (otherDiagnos1 != null) {
                label_0209 :
                   b = false;
                }
             }else if(otherDiagnos.equals(otherDiagnos1)){
             }
             String oprnOprtCode = this.getOprnOprtCode();
             String oprnOprtCode1 = uDrgGroupTab.getOprnOprtCode();
             if (oprnOprtCode == null) {
                if (oprnOprtCode1 != null) {
                   b = false;
                }
             }else if(oprnOprtCode.equals(oprnOprtCode1)){
             }
             String oprnOprtName = this.getOprnOprtName();
             String oprnOprtName1 = uDrgGroupTab.getOprnOprtName();
             if (oprnOprtName == null) {
                if (oprnOprtName1 != null) {
                label_023d :
                   b = false;
                }
             }else if(oprnOprtName.equals(oprnOprtName1)){
             }
             String otherSurgica = this.getOtherSurgicalName();
             String otherSurgica1 = uDrgGroupTab.getOtherSurgicalName();
             if (otherSurgica == null) {
                if (otherSurgica1 != null) {
                   b = false;
                }
             }else if(otherSurgica.equals(otherSurgica1)){
             }
             String socialId = this.getSocialId();
             String socialId1 = uDrgGroupTab.getSocialId();
             if (socialId == null) {
                if (socialId1 != null) {
                label_0271 :
                   b = false;
                }
             }else if(socialId.equals(socialId1)){
             }
             String insuType = this.getInsuType();
             String insuType1 = uDrgGroupTab.getInsuType();
             if (insuType == null) {
                if (insuType1 != null) {
                   b = false;
                }
             }else if(insuType.equals(insuType1)){
             }
             String dRGCode = this.getDRGCode();
             String dRGCode1 = uDrgGroupTab.getDRGCode();
             if (dRGCode == null) {
                if (dRGCode1 != null) {
                label_02a5 :
                   b = false;
                }
             }else if(dRGCode.equals(dRGCode1)){
             }
             String dRGName = this.getDRGName();
             String dRGName1 = uDrgGroupTab.getDRGName();
             if (dRGName == null) {
                if (dRGName1 != null) {
                   b = false;
                }
             }else if(dRGName.equals(dRGName1)){
             }
             String dRGSLimitHig = this.getDRGSLimitHigh();
             String dRGSLimitHig1 = uDrgGroupTab.getDRGSLimitHigh();
             if (dRGSLimitHig == null) {
                if (dRGSLimitHig1 != null) {
                   b = false;
                }
             }else if(dRGSLimitHig.equals(dRGSLimitHig1)){
             }
             String dRGSLimitLow = this.getDRGSLimitLow();
             String dRGSLimitLow1 = uDrgGroupTab.getDRGSLimitLow();
             if (dRGSLimitLow == null) {
                if (dRGSLimitLow1 != null) {
                label_02ed :
                   b = false;
                }
             }else if(dRGSLimitLow.equals(dRGSLimitLow1)){
             }
             String fyqz = this.getFyqz();
             String fyqz1 = uDrgGroupTab.getFyqz();
             if (fyqz == null) {
                if (fyqz1 != null) {
                   b = false;
                }
             }else if(fyqz.equals(fyqz1)){
             }
             String fpxs = this.getFpxs();
             String fpxs1 = uDrgGroupTab.getFpxs();
             if (fpxs == null) {
                if (fpxs1 != null) {
                label_031f :
                   b = false;
                }
             }else if(fpxs.equals(fpxs1)){
             }
             String qzfl = this.getQzfl();
             String qzfl1 = uDrgGroupTab.getQzfl();
             if (qzfl == null) {
                if (qzfl1 != null) {
                   b = false;
                }
             }else if(qzfl.equals(qzfl1)){
             }
             String jzds = this.getJzds();
             String jzds1 = uDrgGroupTab.getJzds();
             if (jzds == null) {
                if (jzds1 != null) {
                label_0353 :
                   b = false;
                }
             }else if(jzds.equals(jzds1)){
             }
             String rW = this.getRW();
             String rW1 = uDrgGroupTab.getRW();
             if (rW == null) {
                if (rW1 != null) {
                   b = false;
                }
             }else if(rW.equals(rW1)){
             }
             String pointsMedica = this.getPointsMedical();
             String pointsMedica1 = uDrgGroupTab.getPointsMedical();
             if (pointsMedica == null) {
                if (pointsMedica1 != null) {
                label_0385 :
                   b = false;
                }
             }else if(pointsMedica.equals(pointsMedica1)){
             }
             String pointsDeduct = this.getPointsDeduction();
             String pointsDeduct1 = uDrgGroupTab.getPointsDeduction();
             if (pointsDeduct == null) {
                if (pointsDeduct1 != null) {
                   b = false;
                }
             }else if(pointsDeduct.equals(pointsDeduct1)){
             }
             String pointsRate = this.getPointsRate();
             String pointsRate1 = uDrgGroupTab.getPointsRate();
             if (pointsRate == null) {
                if (pointsRate1 != null) {
                label_03b9 :
                   b = false;
                }
             }else if(pointsRate.equals(pointsRate1)){
             }
             String pointsAdditi = this.getPointsAdditional();
             String pointsAdditi1 = uDrgGroupTab.getPointsAdditional();
             if (pointsAdditi == null) {
                if (pointsAdditi1 != null) {
                   b = false;
                }
             }else if(pointsAdditi.equals(pointsAdditi1)){
             }
             String tzxs = this.getTzxs();
             String tzxs1 = uDrgGroupTab.getTzxs();
             if (tzxs == null) {
                if (tzxs1 != null) {
                label_03ed :
                   b = false;
                }
             }else if(tzxs.equals(tzxs1)){
             }
             String perPointsFee = this.getPerPointsFee();
             String perPointsFee1 = uDrgGroupTab.getPerPointsFee();
             if (perPointsFee == null) {
                if (perPointsFee1 != null) {
                   b = false;
                }
             }else if(perPointsFee.equals(perPointsFee1)){
             }
             String hifmiPay = this.getHifmiPay();
             String hifmiPay1 = uDrgGroupTab.getHifmiPay();
             if (hifmiPay == null) {
                if (hifmiPay1 != null) {
                   b = false;
                }
             }else if(hifmiPay.equals(hifmiPay1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAdmDeptCode(){
       return this.admDeptCode;
    }
    public String getAdmDeptName(){
       return this.admDeptName;
    }
    public String getAge(){
       return this.age;
    }
    public LocalDateTime getBeginTime(){
       return this.beginTime;
    }
    public String getDRGCode(){
       return this.DRGCode;
    }
    public String getDRGName(){
       return this.DRGName;
    }
    public String getDRGPaySettleFee(){
       return this.DRGPaySettleFee;
    }
    public String getDRGSLimitHigh(){
       return this.DRGSLimitHigh;
    }
    public String getDRGSLimitLow(){
       return this.DRGSLimitLow;
    }
    public String getDRGSMainDiagCode(){
       return this.DRGSMainDiagCode;
    }
    public String getDRGSMainDiagName(){
       return this.DRGSMainDiagName;
    }
    public String getDesgDeptCode(){
       return this.desgDeptCode;
    }
    public String getDesgDeptName(){
       return this.desgDeptName;
    }
    public LocalDateTime getEndTime(){
       return this.endTime;
    }
    public String getFpxs(){
       return this.fpxs;
    }
    public String getFyqz(){
       return this.fyqz;
    }
    public String getGend(){
       return this.gend;
    }
    public String getHifmiPay(){
       return this.hifmiPay;
    }
    public String getInsuType(){
       return this.insuType;
    }
    public String getIptDays(){
       return this.iptDays;
    }
    public String getIptOtpNo(){
       return this.iptOtpNo;
    }
    public String getJzds(){
       return this.jzds;
    }
    public String getMedFeeSumamt(){
       return this.medFeeSumamt;
    }
    public String getMedcasNo(){
       return this.medcasNo;
    }
    public String getOprnOprtCode(){
       return this.oprnOprtCode;
    }
    public String getOprnOprtName(){
       return this.oprnOprtName;
    }
    public String getOtherDiagnosticName(){
       return this.otherDiagnosticName;
    }
    public String getOtherSurgicalName(){
       return this.otherSurgicalName;
    }
    public String getPerPointsFee(){
       return this.perPointsFee;
    }
    public String getPointsAdditional(){
       return this.pointsAdditional;
    }
    public String getPointsDeduction(){
       return this.pointsDeduction;
    }
    public String getPointsMedical(){
       return this.pointsMedical;
    }
    public String getPointsRate(){
       return this.pointsRate;
    }
    public String getPsnName(){
       return this.psnName;
    }
    public String getPsnNo(){
       return this.psnNo;
    }
    public String getQzfl(){
       return this.qzfl;
    }
    public String getRW(){
       return this.RW;
    }
    public String getSetlId(){
       return this.setlId;
    }
    public String getSocialId(){
       return this.socialId;
    }
    public String getTotalProfitAndLoss(){
       return this.totalProfitAndLoss;
    }
    public String getTzxs(){
       return this.tzxs;
    }
    public int hashCode(){
       String $setlId;
       int PRIME = 59;
       int result = 1;
       int i = (($setlId = this.getSetlId()) == null)? 43: $setlId.hashCode();
       result = i + 59;
       String $medcasNo = this.getMedcasNo();
       int i1 = result * 59;
       i = ($medcasNo == null)? 43: $medcasNo.hashCode();
       result = i1 + i;
       String $iptOtpNo = this.getIptOtpNo();
       i1 = result * 59;
       i = ($iptOtpNo == null)? 43: $iptOtpNo.hashCode();
       result = i1 + i;
       String $DRGPaySettleFee = this.getDRGPaySettleFee();
       i1 = result * 59;
       i = ($DRGPaySettleFee == null)? 43: $DRGPaySettleFee.hashCode();
       result = i1 + i;
       String $medFeeSumamt = this.getMedFeeSumamt();
       i1 = result * 59;
       i = ($medFeeSumamt == null)? 43: $medFeeSumamt.hashCode();
       result = i1 + i;
       String totalProfitA = this.getTotalProfitAndLoss();
       i1 = result * 59;
       i = (totalProfitA == null)? 43: totalProfitA.hashCode();
       String psnNo = this.getPsnNo();
       i1 = (i1 + i) * 59;
       i = (psnNo == null)? 43: psnNo.hashCode();
       String psnName = this.getPsnName();
       i1 = (i1 + i) * 59;
       i = (psnName == null)? 43: psnName.hashCode();
       String gend = this.getGend();
       i1 = (i1 + i) * 59;
       i = (gend == null)? 43: gend.hashCode();
       String age = this.getAge();
       i1 = (i1 + i) * 59;
       i = (age == null)? 43: age.hashCode();
       LocalDateTime beginTime = this.getBeginTime();
       i1 = (i1 + i) * 59;
       i = (beginTime == null)? 43: beginTime.hashCode();
       LocalDateTime endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       String iptDays = this.getIptDays();
       i1 = (i1 + i) * 59;
       i = (iptDays == null)? 43: iptDays.hashCode();
       String admDeptCode = this.getAdmDeptCode();
       i1 = (i1 + i) * 59;
       i = (admDeptCode == null)? 43: admDeptCode.hashCode();
       String admDeptName = this.getAdmDeptName();
       i1 = (i1 + i) * 59;
       i = (admDeptName == null)? 43: admDeptName.hashCode();
       String desgDeptCode = this.getDesgDeptCode();
       i1 = (i1 + i) * 59;
       i = (desgDeptCode == null)? 43: desgDeptCode.hashCode();
       String desgDeptName = this.getDesgDeptName();
       i1 = (i1 + i) * 59;
       i = (desgDeptName == null)? 43: desgDeptName.hashCode();
       String dRGSMainDiag = this.getDRGSMainDiagCode();
       i1 = (i1 + i) * 59;
       i = (dRGSMainDiag == null)? 43: dRGSMainDiag.hashCode();
       String dRGSMainDiag1 = this.getDRGSMainDiagName();
       i1 = (i1 + i) * 59;
       i = (dRGSMainDiag1 == null)? 43: dRGSMainDiag1.hashCode();
       String otherDiagnos = this.getOtherDiagnosticName();
       i1 = (i1 + i) * 59;
       i = (otherDiagnos == null)? 43: otherDiagnos.hashCode();
       String oprnOprtCode = this.getOprnOprtCode();
       i1 = (i1 + i) * 59;
       i = (oprnOprtCode == null)? 43: oprnOprtCode.hashCode();
       String oprnOprtName = this.getOprnOprtName();
       i1 = (i1 + i) * 59;
       i = (oprnOprtName == null)? 43: oprnOprtName.hashCode();
       String otherSurgica = this.getOtherSurgicalName();
       i1 = (i1 + i) * 59;
       i = (otherSurgica == null)? 43: otherSurgica.hashCode();
       String socialId = this.getSocialId();
       i1 = (i1 + i) * 59;
       i = (socialId == null)? 43: socialId.hashCode();
       String insuType = this.getInsuType();
       i1 = (i1 + i) * 59;
       i = (insuType == null)? 43: insuType.hashCode();
       String dRGCode = this.getDRGCode();
       i1 = (i1 + i) * 59;
       i = (dRGCode == null)? 43: dRGCode.hashCode();
       String dRGName = this.getDRGName();
       i1 = (i1 + i) * 59;
       i = (dRGName == null)? 43: dRGName.hashCode();
       String dRGSLimitHig = this.getDRGSLimitHigh();
       i1 = (i1 + i) * 59;
       i = (dRGSLimitHig == null)? 43: dRGSLimitHig.hashCode();
       String dRGSLimitLow = this.getDRGSLimitLow();
       i1 = (i1 + i) * 59;
       i = (dRGSLimitLow == null)? 43: dRGSLimitLow.hashCode();
       String fyqz = this.getFyqz();
       i1 = (i1 + i) * 59;
       i = (fyqz == null)? 43: fyqz.hashCode();
       String fpxs = this.getFpxs();
       i1 = (i1 + i) * 59;
       i = (fpxs == null)? 43: fpxs.hashCode();
       String qzfl = this.getQzfl();
       i1 = (i1 + i) * 59;
       i = (qzfl == null)? 43: qzfl.hashCode();
       String jzds = this.getJzds();
       i1 = (i1 + i) * 59;
       i = (jzds == null)? 43: jzds.hashCode();
       String rW = this.getRW();
       i1 = (i1 + i) * 59;
       i = (rW == null)? 43: rW.hashCode();
       String pointsMedica = this.getPointsMedical();
       i1 = (i1 + i) * 59;
       i = (pointsMedica == null)? 43: pointsMedica.hashCode();
       String pointsDeduct = this.getPointsDeduction();
       i1 = (i1 + i) * 59;
       i = (pointsDeduct == null)? 43: pointsDeduct.hashCode();
       String pointsRate = this.getPointsRate();
       i1 = (i1 + i) * 59;
       i = (pointsRate == null)? 43: pointsRate.hashCode();
       String pointsAdditi = this.getPointsAdditional();
       i1 = (i1 + i) * 59;
       i = (pointsAdditi == null)? 43: pointsAdditi.hashCode();
       String tzxs = this.getTzxs();
       i1 = (i1 + i) * 59;
       i = (tzxs == null)? 43: tzxs.hashCode();
       String perPointsFee = this.getPerPointsFee();
       i1 = (i1 + i) * 59;
       i = (perPointsFee == null)? 43: perPointsFee.hashCode();
       String hifmiPay = this.getHifmiPay();
       i1 = (i1 + i) * 59;
       i = (hifmiPay == null)? 43: hifmiPay.hashCode();
       return (i1 + i);
    }
    public void setAdmDeptCode(String admDeptCode){
       this.admDeptCode = admDeptCode;
    }
    public void setAdmDeptName(String admDeptName){
       this.admDeptName = admDeptName;
    }
    public void setAge(String age){
       this.age = age;
    }
    public void setBeginTime(LocalDateTime beginTime){
       this.beginTime = beginTime;
    }
    public void setDRGCode(String DRGCode){
       this.DRGCode = DRGCode;
    }
    public void setDRGName(String DRGName){
       this.DRGName = DRGName;
    }
    public void setDRGPaySettleFee(String DRGPaySettleFee){
       this.DRGPaySettleFee = DRGPaySettleFee;
    }
    public void setDRGSLimitHigh(String DRGSLimitHigh){
       this.DRGSLimitHigh = DRGSLimitHigh;
    }
    public void setDRGSLimitLow(String DRGSLimitLow){
       this.DRGSLimitLow = DRGSLimitLow;
    }
    public void setDRGSMainDiagCode(String DRGSMainDiagCode){
       this.DRGSMainDiagCode = DRGSMainDiagCode;
    }
    public void setDRGSMainDiagName(String DRGSMainDiagName){
       this.DRGSMainDiagName = DRGSMainDiagName;
    }
    public void setDesgDeptCode(String desgDeptCode){
       this.desgDeptCode = desgDeptCode;
    }
    public void setDesgDeptName(String desgDeptName){
       this.desgDeptName = desgDeptName;
    }
    public void setEndTime(LocalDateTime endTime){
       this.endTime = endTime;
    }
    public void setFpxs(String fpxs){
       this.fpxs = fpxs;
    }
    public void setFyqz(String fyqz){
       this.fyqz = fyqz;
    }
    public void setGend(String gend){
       this.gend = gend;
    }
    public void setHifmiPay(String hifmiPay){
       this.hifmiPay = hifmiPay;
    }
    public void setInsuType(String insuType){
       this.insuType = insuType;
    }
    public void setIptDays(String iptDays){
       this.iptDays = iptDays;
    }
    public void setIptOtpNo(String iptOtpNo){
       this.iptOtpNo = iptOtpNo;
    }
    public void setJzds(String jzds){
       this.jzds = jzds;
    }
    public void setMedFeeSumamt(String medFeeSumamt){
       this.medFeeSumamt = medFeeSumamt;
    }
    public void setMedcasNo(String medcasNo){
       this.medcasNo = medcasNo;
    }
    public void setOprnOprtCode(String oprnOprtCode){
       this.oprnOprtCode = oprnOprtCode;
    }
    public void setOprnOprtName(String oprnOprtName){
       this.oprnOprtName = oprnOprtName;
    }
    public void setOtherDiagnosticName(String otherDiagnosticName){
       this.otherDiagnosticName = otherDiagnosticName;
    }
    public void setOtherSurgicalName(String otherSurgicalName){
       this.otherSurgicalName = otherSurgicalName;
    }
    public void setPerPointsFee(String perPointsFee){
       this.perPointsFee = perPointsFee;
    }
    public void setPointsAdditional(String pointsAdditional){
       this.pointsAdditional = pointsAdditional;
    }
    public void setPointsDeduction(String pointsDeduction){
       this.pointsDeduction = pointsDeduction;
    }
    public void setPointsMedical(String pointsMedical){
       this.pointsMedical = pointsMedical;
    }
    public void setPointsRate(String pointsRate){
       this.pointsRate = pointsRate;
    }
    public void setPsnName(String psnName){
       this.psnName = psnName;
    }
    public void setPsnNo(String psnNo){
       this.psnNo = psnNo;
    }
    public void setQzfl(String qzfl){
       this.qzfl = qzfl;
    }
    public void setRW(String RW){
       this.RW = RW;
    }
    public void setSetlId(String setlId){
       this.setlId = setlId;
    }
    public void setSocialId(String socialId){
       this.socialId = socialId;
    }
    public void setTotalProfitAndLoss(String totalProfitAndLoss){
       this.totalProfitAndLoss = totalProfitAndLoss;
    }
    public void setTzxs(String tzxs){
       this.tzxs = tzxs;
    }
    public String toString(){
       return "DrgGroupTableFieldsVo\(setlId="+this.getSetlId()+", medcasNo="+this.getMedcasNo()+", iptOtpNo="+this.getIptOtpNo()+", DRGPaySettleFee="+this.getDRGPaySettleFee()+", medFeeSumamt="+this.getMedFeeSumamt()+", totalProfitAndLoss="+this.getTotalProfitAndLoss()+", psnNo="+this.getPsnNo()+", psnName="+this.getPsnName()+", gend="+this.getGend()+", age="+this.getAge()+", beginTime="+this.getBeginTime()+", endTime="+this.getEndTime()+", iptDays="+this.getIptDays()+", admDeptCode="+this.getAdmDeptCode()+", admDeptName="+this.getAdmDeptName()+", desgDeptCode="+this.getDesgDeptCode()+", desgDeptName="+this.getDesgDeptName()+", DRGSMainDiagCode="+this.getDRGSMainDiagCode()+", DRGSMainDiagName="+this.getDRGSMainDiagName()+", otherDiagnosticName="+this.getOtherDiagnosticName()+", oprnOprtCode="+this.getOprnOprtCode()+", oprnOprtName="+this.getOprnOprtName()+", otherSurgicalName="+this.getOtherSurgicalName()+", socialId="+this.getSocialId()+", insuType="+this.getInsuType()+", DRGCode="+this.getDRGCode()+", DRGName="+this.getDRGName()+", DRGSLimitHigh="+this.getDRGSLimitHigh()+", DRGSLimitLow="+this.getDRGSLimitLow()+", fyqz="+this.getFyqz()+", fpxs="+this.getFpxs()+", qzfl="+this.getQzfl()+", jzds="+this.getJzds()+", RW="+this.getRW()+", pointsMedical="+this.getPointsMedical()+", pointsDeduction="+this.getPointsDeduction()+", pointsRate="+this.getPointsRate()+", pointsAdditional="+this.getPointsAdditional()+", tzxs="+this.getTzxs()+", perPointsFee="+this.getPerPointsFee()+", hifmiPay="+this.getHifmiPay()+"\)";
    }
}
