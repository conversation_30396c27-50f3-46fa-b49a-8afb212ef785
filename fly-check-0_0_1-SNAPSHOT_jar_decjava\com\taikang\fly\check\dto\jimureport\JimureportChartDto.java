package com.taikang.fly.check.dto.jimureport.JimureportChartDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class JimureportChartDto implements Serializable	// class@000137 from classes.dex
{
    private String ruleName;
    private String tableName;
    private String type;
    private String urlData;
    private static final long serialVersionUID = 0xe718edf2ae79f35f;

    public void JimureportChartDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof JimureportChartDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof JimureportChartDto) {
             b = false;
          }else {
             JimureportChartDto jimureportCh = o;
             if (!jimureportCh.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = jimureportCh.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = jimureportCh.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String urlData = this.getUrlData();
                String urlData1 = jimureportCh.getUrlData();
                if (urlData == null) {
                   if (urlData1 != null) {
                      b = false;
                   }
                }else if(urlData.equals(urlData1)){
                }
                String type = this.getType();
                String type1 = jimureportCh.getType();
                if (type == null) {
                   if (type1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!type.equals(type1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getType(){
       return this.type;
    }
    public String getUrlData(){
       return this.urlData;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $urlData = this.getUrlData();
       i2 = result * 59;
       i1 = ($urlData == null)? i: $urlData.hashCode();
       result = i2 + i1;
       String $type = this.getType();
       i1 = result * 59;
       if ($type != null) {
          i = $type.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setType(String type){
       this.type = type;
    }
    public void setUrlData(String urlData){
       this.urlData = urlData;
    }
    public String toString(){
       return "JimureportChartDto\(tableName="+this.getTableName()+", ruleName="+this.getRuleName()+", urlData="+this.getUrlData()+", type="+this.getType()+"\)";
    }
}
