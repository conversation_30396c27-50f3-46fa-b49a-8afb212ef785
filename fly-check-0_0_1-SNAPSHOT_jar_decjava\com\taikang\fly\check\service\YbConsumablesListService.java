package com.taikang.fly.check.service.YbConsumablesListService;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybconsumablesList.YbConsumablesListSearchDto;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.dto.DatasourceInfo;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.ConsumablesMapper;
import com.taikang.fly.check.dto.mapstruct.YbConsumablesListMapping;

public class YbConsumablesListService	// class@00030b from classes.dex
{
    private ConsumablesMapper consumablesMapper;
    private YbConsumablesListMapping ybConsumablesListMapping;

    public void YbConsumablesListService(){
       super();
    }
    public NativePage queryList(Integer page,Integer size,YbConsumablesListSearchDto ybConsumablesListSearchDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       String oraUserName = ThreadLocalContextHolder.getContext().getUserInfo().getDatasourceInfo().getUsername();
       List ybConsumablesLists = this.consumablesMapper.queryList(ybConsumablesListSearchDto, oraUserName);
       List dtos = this.ybConsumablesListMapping.entryToDtoList(ybConsumablesLists);
       NativePage pageDto = new NativePage(dtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
