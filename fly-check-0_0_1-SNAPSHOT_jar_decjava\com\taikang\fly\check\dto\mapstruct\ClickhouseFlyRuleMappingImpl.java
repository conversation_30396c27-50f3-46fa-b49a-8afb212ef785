package com.taikang.fly.check.dto.mapstruct.ClickhouseFlyRuleMappingImpl;
import com.taikang.fly.check.dto.mapstruct.ClickhouseFlyRuleMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleAddDto;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRule;
import java.lang.String;
import java.util.Date;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleAddTemplateDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleRespDto;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZoneId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleEditDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseWorkOrderEditDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.TemplateClickhouseEditDto;

public class ClickhouseFlyRuleMappingImpl implements ClickhouseFlyRuleMapping	// class@000141 from classes.dex
{

    public void ClickhouseFlyRuleMappingImpl(){
       super();
    }
    public ClickhouseFlyRule addDtoToDomain(ClickhouseFlyRuleAddDto addDto){
       ClickhouseFlyRule uClickhouseF;
       if (addDto == null) {
          uClickhouseF = null;
       }else {
          uClickhouseF = new ClickhouseFlyRule();
          uClickhouseF.setRuleName(addDto.getRuleName());
          uClickhouseF.setRuleType(addDto.getRuleType());
          uClickhouseF.setPs(addDto.getPs());
          uClickhouseF.setSqlName(addDto.getSqlName());
          uClickhouseF.setRuleCategory1(addDto.getRuleCategory1());
          uClickhouseF.setRuleCategory2(addDto.getRuleCategory2());
          uClickhouseF.setDiagnosisType(addDto.getDiagnosisType());
          uClickhouseF.setRuleDescribe(addDto.getRuleDescribe());
          uClickhouseF.setRuleLevel(addDto.getRuleLevel());
          uClickhouseF.setCreatedTime(new Date());
          uClickhouseF.setOperateTime(new Date());
       }
       return uClickhouseF;
    }
    public ClickhouseFlyRule addRuleDtoToDomain(ClickhouseFlyRuleAddDto addDto){
       ClickhouseFlyRule uClickhouseF;
       if (addDto == null) {
          uClickhouseF = null;
       }else {
          uClickhouseF = new ClickhouseFlyRule();
          uClickhouseF.setRuleName(addDto.getRuleName());
          uClickhouseF.setPs(addDto.getPs());
          uClickhouseF.setSqlName(addDto.getSqlName());
          uClickhouseF.setRuleCategory1(addDto.getRuleCategory1());
          uClickhouseF.setRuleCategory2(addDto.getRuleCategory2());
          uClickhouseF.setDiagnosisType(addDto.getDiagnosisType());
          uClickhouseF.setRuleDescribe(addDto.getRuleDescribe());
          uClickhouseF.setRuleLevel(addDto.getRuleLevel());
          uClickhouseF.setRedField2("1");
          uClickhouseF.setRedField1("1");
          uClickhouseF.setOperateTime(new Date());
          uClickhouseF.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uClickhouseF.setSourceOfRule("1");
          uClickhouseF.setRemoved("1");
          uClickhouseF.setRuleType("2");
          uClickhouseF.setCreater(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uClickhouseF.setCreatedTime(new Date());
          uClickhouseF.setId(SequenceGenerator.getId());
          uClickhouseF.setState("2");
          uClickhouseF.setRegion(ThreadLocalContextHolder.getContext().getUserInfo().getRegion());
          uClickhouseF.setSubmitState("1");
       }
       return uClickhouseF;
    }
    public ClickhouseFlyRule addTemplateDtoToDomain(ClickhouseFlyRuleAddTemplateDto addDto){
       ClickhouseFlyRule uClickhouseF;
       if (addDto == null) {
          uClickhouseF = null;
       }else {
          uClickhouseF = new ClickhouseFlyRule();
          uClickhouseF.setRuleName(addDto.getRuleName());
          uClickhouseF.setRuleType(addDto.getRuleType());
          uClickhouseF.setPs(addDto.getPs());
          uClickhouseF.setSqlName(addDto.getSqlName());
          uClickhouseF.setRuleCategory1(addDto.getRuleCategory1());
          uClickhouseF.setRuleCategory2(addDto.getRuleCategory2());
          uClickhouseF.setDiagnosisType(addDto.getDiagnosisType());
          uClickhouseF.setRuleDescribe(addDto.getRuleDescribe());
          uClickhouseF.setRuleLevel(addDto.getRuleLevel());
          uClickhouseF.setCreatedTime(new Date());
          uClickhouseF.setOperateTime(new Date());
       }
       return uClickhouseF;
    }
    public ClickhouseFlyRuleRespDto domainToInfoDto(ClickhouseFlyRule domain){
       ClickhouseFlyRuleRespDto uClickhouseF;
       if (domain == null) {
          uClickhouseF = null;
       }else {
          uClickhouseF = new ClickhouseFlyRuleRespDto();
          uClickhouseF.setRuleName(domain.getRuleName());
          uClickhouseF.setRegion(domain.getRegion());
          uClickhouseF.setRuleType(domain.getRuleType());
          uClickhouseF.setPs(domain.getPs());
          uClickhouseF.setSqlName(domain.getSqlName());
          uClickhouseF.setNewSqlName(domain.getNewSqlName());
          uClickhouseF.setId(domain.getId());
          uClickhouseF.setOperator(domain.getOperator());
          uClickhouseF.setResultFlag(domain.getResultFlag());
          uClickhouseF.setRemoved(domain.getRemoved());
          if (domain.getOperateTime() != null) {
             uClickhouseF.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             uClickhouseF.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          uClickhouseF.setState(domain.getState());
          uClickhouseF.setCreater(domain.getCreater());
          uClickhouseF.setDataSources(domain.getDataSources());
          uClickhouseF.setSubmitState(domain.getSubmitState());
          uClickhouseF.setResultsEnforcement(domain.getResultsEnforcement());
          uClickhouseF.setExecutionDate(domain.getExecutionDate());
          uClickhouseF.setRuleLevel(domain.getRuleLevel());
          uClickhouseF.setRuleCategory1(domain.getRuleCategory1());
          uClickhouseF.setRuleCategory2(domain.getRuleCategory2());
          uClickhouseF.setDiagnosisType(domain.getDiagnosisType());
          uClickhouseF.setRuleDescribe(domain.getRuleDescribe());
          uClickhouseF.setSourceOfRule(domain.getSourceOfRule());
          uClickhouseF.setRuleLogic(domain.getRuleLogic());
          uClickhouseF.setRuleParameter(domain.getRuleParameter());
          uClickhouseF.setRedField3(domain.getRedField3());
          uClickhouseF.setRuleClassify(domain.getRuleClassify());
       }
       return uClickhouseF;
    }
    public ClickhouseFlyRuleAddDto domainToaddDto(ClickhouseFlyRule flyRule){
       ClickhouseFlyRuleAddDto uClickhouseF;
       if (flyRule == null) {
          uClickhouseF = null;
       }else {
          uClickhouseF = new ClickhouseFlyRuleAddDto();
          uClickhouseF.setRuleName(flyRule.getRuleName());
          uClickhouseF.setSqlName(flyRule.getSqlName());
          uClickhouseF.setRuleType(flyRule.getRuleType());
          uClickhouseF.setPs(flyRule.getPs());
          uClickhouseF.setRuleLevel(flyRule.getRuleLevel());
          uClickhouseF.setRuleCategory1(flyRule.getRuleCategory1());
          uClickhouseF.setRuleCategory2(flyRule.getRuleCategory2());
          uClickhouseF.setDiagnosisType(flyRule.getDiagnosisType());
          uClickhouseF.setRuleDescribe(flyRule.getRuleDescribe());
       }
       return uClickhouseF;
    }
    public List dtoListToDomain(List flyRuleList){
       List list;
       if (flyRuleList == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleList.size());
          Iterator iterator = flyRuleList.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoDto(iterator.next()));
          }
       }
       return list;
    }
    public ClickhouseFlyRule editDtoToDomain(ClickhouseFlyRuleEditDto editDto,ClickhouseFlyRule domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleName(editDto.getRuleName());
          domain.setRegion(editDto.getRegion());
          domain.setRuleType(editDto.getRuleType());
          domain.setPs(editDto.getPs());
          domain.setNewSqlName(editDto.getNewSqlName());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public ClickhouseFlyRule editWorkOrderDtoToDomain(ClickhouseWorkOrderEditDto editDto,ClickhouseFlyRule domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleName(editDto.getRuleName());
          domain.setRegion(editDto.getRegion());
          domain.setRuleType(editDto.getRuleType());
          domain.setPs(editDto.getPs());
          domain.setSqlName(editDto.getSqlName());
          domain.setNewSqlName(editDto.getNewSqlName());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public ClickhouseFlyRule templateEditDtoToDomain(TemplateClickhouseEditDto templateClickhouseEditDto,ClickhouseFlyRule flyRule){
       if (templateClickhouseEditDto == null) {
          flyRule = null;
       }else {
          flyRule.setRuleName(templateClickhouseEditDto.getRuleName());
          flyRule.setPs(templateClickhouseEditDto.getPs());
          flyRule.setNewSqlName(templateClickhouseEditDto.getNewSqlName());
          flyRule.setRuleCategory1(templateClickhouseEditDto.getRuleCategory1());
          flyRule.setRuleCategory2(templateClickhouseEditDto.getRuleCategory2());
          flyRule.setDiagnosisType(templateClickhouseEditDto.getDiagnosisType());
          flyRule.setRuleDescribe(templateClickhouseEditDto.getRuleDescribe());
          flyRule.setRuleLevel(templateClickhouseEditDto.getRuleLevel());
          flyRule.setOperateTime(new Date());
       }
       return flyRule;
    }
}
