package com.taikang.fly.check.mybatis.domain.FileInfo;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;

public class FileInfo	// class@000241 from classes.dex
{
    private String category;
    private LocalDateTime createTime;
    private String creator;
    private String fileName;
    private String filePath;
    private String fileSpec;
    private String hospName;
    private String id;
    private String ruleId;

    public void FileInfo(){
       super();
    }
    public String getCategory(){
       return this.category;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getFileName(){
       return this.fileName;
    }
    public String getFilePath(){
       return this.filePath;
    }
    public String getFileSpec(){
       return this.fileSpec;
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getId(){
       return this.id;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public void setCategory(String category){
       this.category = category;
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setFileName(String fileName){
       this.fileName = fileName;
    }
    public void setFilePath(String filePath){
       this.filePath = filePath;
    }
    public void setFileSpec(String fileSpec){
       this.fileSpec = fileSpec;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
}
