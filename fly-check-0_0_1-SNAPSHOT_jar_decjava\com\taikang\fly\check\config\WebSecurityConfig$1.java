package com.taikang.fly.check.config.WebSecurityConfig$1;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.taikang.fly.check.config.WebSecurityConfig;
import java.lang.Object;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import java.lang.String;
import org.springframework.web.servlet.config.annotation.CorsRegistration;

class WebSecurityConfig$1 implements WebMvcConfigurer	// class@000093 from classes.dex
{
    final WebSecurityConfig this$0;

    void WebSecurityConfig$1(WebSecurityConfig this$0){
       this.this$0 = this$0;
       super();
    }
    public void addCorsMappings(CorsRegistry registry){
       registry.addMapping("/api/**");
    }
}
