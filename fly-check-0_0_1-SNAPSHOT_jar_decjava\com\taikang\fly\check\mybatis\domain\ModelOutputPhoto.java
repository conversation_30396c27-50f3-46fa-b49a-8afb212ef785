package com.taikang.fly.check.mybatis.domain.ModelOutputPhoto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelOutputPhoto	// class@000258 from classes.dex
{
    private String modelWorkflowId;
    private String photoName;
    private String photoSrc;

    public void ModelOutputPhoto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelOutputPhoto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelOutputPhoto) {
             b = false;
          }else {
             ModelOutputPhoto modelOutputP = o;
             if (!modelOutputP.canEqual(this)) {
                b = false;
             }else {
                String modelWorkflo = this.getModelWorkflowId();
                String modelWorkflo1 = modelOutputP.getModelWorkflowId();
                if (modelWorkflo == null) {
                   if (modelWorkflo1 != null) {
                      b = false;
                   }
                }else if(modelWorkflo.equals(modelWorkflo1)){
                }
                String photoName = this.getPhotoName();
                String photoName1 = modelOutputP.getPhotoName();
                if (photoName == null) {
                   if (photoName1 != null) {
                      b = false;
                   }
                }else if(photoName.equals(photoName1)){
                }
                String photoSrc = this.getPhotoSrc();
                String photoSrc1 = modelOutputP.getPhotoSrc();
                if (photoSrc == null) {
                   if (photoSrc1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!photoSrc.equals(photoSrc1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getModelWorkflowId(){
       return this.modelWorkflowId;
    }
    public String getPhotoName(){
       return this.photoName;
    }
    public String getPhotoSrc(){
       return this.photoSrc;
    }
    public int hashCode(){
       String $modelWorkflowId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($modelWorkflowId = this.getModelWorkflowId()) == null)? i: $modelWorkflowId.hashCode();
       result = i1 + 59;
       String $photoName = this.getPhotoName();
       int i2 = result * 59;
       i1 = ($photoName == null)? i: $photoName.hashCode();
       result = i2 + i1;
       String $photoSrc = this.getPhotoSrc();
       i1 = result * 59;
       if ($photoSrc != null) {
          i = $photoSrc.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setModelWorkflowId(String modelWorkflowId){
       this.modelWorkflowId = modelWorkflowId;
    }
    public void setPhotoName(String photoName){
       this.photoName = photoName;
    }
    public void setPhotoSrc(String photoSrc){
       this.photoSrc = photoSrc;
    }
    public String toString(){
       return "ModelOutputPhoto\(modelWorkflowId="+this.getModelWorkflowId()+", photoName="+this.getPhotoName()+", photoSrc="+this.getPhotoSrc()+"\)";
    }
}
