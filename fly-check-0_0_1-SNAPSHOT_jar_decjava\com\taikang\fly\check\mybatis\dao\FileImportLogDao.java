package com.taikang.fly.check.mybatis.dao.FileImportLogDao;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.taikang.fly.check.dto.csventry.CsvConfigEntry;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.FileImportLog;
import java.util.Map;

public interface abstract FileImportLogDao implements BaseMapper	// class@0001ef from classes.dex
{

    void addColumnType(CsvConfigEntry p0);
    Integer delConfigByName(String p0);
    Integer deleteColumnType(String p0);
    CsvConfigEntry findByColumnName(String p0);
    String findByName(String p0);
    List findConfigNames();
    FileImportLog findPath(String p0);
    List getColumnType(Map p0);
    List getConfigInfo(String p0);
    List getConfigNameInfo(Map p0);
    void insertCsvConfig(CsvConfigEntry p0);
    List selectCsvConfig();
    Integer updateColumnType(CsvConfigEntry p0);
}
