package com.taikang.fly.check.rest.PlanRuleController;
import java.lang.Object;
import java.util.List;
import com.taikang.fly.check.dto.RmpResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.PlanRuleService;
import com.taikang.fly.check.dto.planRule.PlanRuleRespNewDto;
import java.lang.String;
import com.taikang.fly.check.dto.flyRule.FlyRulePlanDto;
import com.taikang.fly.check.comm.NativePage;

public class PlanRuleController	// class@0002a7 from classes.dex
{
    private PlanRuleService planRuleService;

    public void PlanRuleController(){
       super();
    }
    public RmpResponse add(List planRuleAddDto){
       return RmpResponse.success(this.planRuleService.add(planRuleAddDto));
    }
    public RmpResponse addAll(PlanRuleRespNewDto planRuleRespNewDto){
       return RmpResponse.success(this.planRuleService.addAll(planRuleRespNewDto));
    }
    public RmpResponse deletePlanRule(String id){
       return RmpResponse.success(this.planRuleService.deleteById(id));
    }
    public RmpResponse listPageNew(Integer page,Integer size,FlyRulePlanDto flyRulePlanDto){
       return RmpResponse.success(this.planRuleService.queryRuleInfoNew(page, size, flyRulePlanDto));
    }
}
