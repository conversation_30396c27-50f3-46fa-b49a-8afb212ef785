package com.taikang.fly.check.mybatis.dao.DiagnosisTreatmentMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.dto.ybDiagnosisTreatmentList.YbDiagnosisTreatmentListSearchDto;
import java.util.List;

public interface abstract DiagnosisTreatmentMapper implements BaseMapper	// class@0001e1 from classes.dex
{

    void createTable(String p0);
    List queryList(YbDiagnosisTreatmentListSearchDto p0,String p1);
}
