package com.taikang.fly.check.dto.csventry.CsvConfigSearchDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class CsvConfigSearchDto	// class@0000d6 from classes.dex
{
    private String configName;
    private String fieldName;
    private String fieldType;

    public void CsvConfigSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CsvConfigSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof CsvConfigSearchDto) {
             b = false;
          }else {
             CsvConfigSearchDto uCsvConfigSe = o;
             if (!uCsvConfigSe.canEqual(this)) {
                b = false;
             }else {
                String configName = this.getConfigName();
                String configName1 = uCsvConfigSe.getConfigName();
                if (configName == null) {
                   if (configName1 != null) {
                      b = false;
                   }
                }else if(configName.equals(configName1)){
                }
                String fieldName = this.getFieldName();
                String fieldName1 = uCsvConfigSe.getFieldName();
                if (fieldName == null) {
                   if (fieldName1 != null) {
                      b = false;
                   }
                }else if(fieldName.equals(fieldName1)){
                }
                String fieldType = this.getFieldType();
                String fieldType1 = uCsvConfigSe.getFieldType();
                if (fieldType == null) {
                   if (fieldType1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!fieldType.equals(fieldType1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getConfigName(){
       return this.configName;
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public String getFieldType(){
       return this.fieldType;
    }
    public int hashCode(){
       String $configName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($configName = this.getConfigName()) == null)? i: $configName.hashCode();
       result = i1 + 59;
       String $fieldName = this.getFieldName();
       int i2 = result * 59;
       i1 = ($fieldName == null)? i: $fieldName.hashCode();
       result = i2 + i1;
       String $fieldType = this.getFieldType();
       i1 = result * 59;
       if ($fieldType != null) {
          i = $fieldType.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setConfigName(String configName){
       this.configName = configName;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setFieldType(String fieldType){
       this.fieldType = fieldType;
    }
    public String toString(){
       return "CsvConfigSearchDto\(configName="+this.getConfigName()+", fieldName="+this.getFieldName()+", fieldType="+this.getFieldType()+"\)";
    }
}
