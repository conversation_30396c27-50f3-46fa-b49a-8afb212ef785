package com.taikang.fly.check.dto.ExportQueryDto;
import java.io.Serializable;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.lang.StringBuilder;

public class ExportQueryDto implements Serializable	// class@0000a0 from classes.dex
{
    private List str;
    private static final long serialVersionUID = 0x1;

    public void ExportQueryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ExportQueryDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ExportQueryDto) {
             b = false;
          }else {
             ExportQueryDto uExportQuery = o;
             if (!uExportQuery.canEqual(this)) {
                b = false;
             }else {
                List str = this.getStr();
                List str1 = uExportQuery.getStr();
                if (str == null) {
                   if (str1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!str.equals(str1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getStr(){
       return this.str;
    }
    public int hashCode(){
       List $str;
       int PRIME = 59;
       int result = 1;
       int i = (($str = this.getStr()) == null)? 43: $str.hashCode();
       result = i + 59;
       return result;
    }
    public void setStr(List str){
       this.str = str;
    }
    public String toString(){
       return "ExportQueryDto\(str="+this.getStr()+"\)";
    }
}
