package com.taikang.fly.check.dto.plan.PlanAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanAddDto implements Serializable	// class@00019e from classes.dex
{
    private String planName;
    private static final long serialVersionUID = 0x1;

    public void PlanAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanAddDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof PlanAddDto) {
             b = false;
          }else {
             PlanAddDto planAddDto = o;
             if (!planAddDto.canEqual(this)) {
                b = false;
             }else {
                String planName = this.getPlanName();
                String planName1 = planAddDto.getPlanName();
                if (planName == null) {
                   if (planName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!planName.equals(planName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getPlanName(){
       return this.planName;
    }
    public int hashCode(){
       String $planName;
       int PRIME = 59;
       int result = 1;
       int i = (($planName = this.getPlanName()) == null)? 43: $planName.hashCode();
       result = i + 59;
       return result;
    }
    public void setPlanName(String planName){
       this.planName = planName;
    }
    public String toString(){
       return "PlanAddDto\(planName="+this.getPlanName()+"\)";
    }
}
