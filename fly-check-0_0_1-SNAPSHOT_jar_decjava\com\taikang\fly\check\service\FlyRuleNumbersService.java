package com.taikang.fly.check.service.FlyRuleNumbersService;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taikang.fly.check.mybatis.dao.FlyRuleNumbersMapper;
import com.taikang.fly.check.comm.Page;
import java.util.List;

public class FlyRuleNumbersService	// class@0002df from classes.dex
{
    private FlyRuleNumbersMapper flyRuleNumbersMapper;
    private static final Logger log;

    static {
       FlyRuleNumbersService.log = LoggerFactory.getLogger(FlyRuleNumbersService.class);
    }
    public void FlyRuleNumbersService(){
       super();
    }
    public CommResponse getFlyRuleNumbersList(Integer page,Integer size,String ruleName){
       Page page1 = new Page((long)page.intValue(), (long)size.intValue());
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.like("FLY_RULE_NAME", ruleName);
       Object[] objArray = new Object[]{"NUMBERS"};
       queryWrapper.orderByDesc(objArray);
       IPage flyRuleNumbersIPage = this.flyRuleNumbersMapper.selectPage(page1, queryWrapper);
       Page page2 = new Page();
       page2.setDataList(flyRuleNumbersIPage.getRecords());
       page2.setTotalPage(Integer.valueOf((int)flyRuleNumbersIPage.getPages()));
       page2.setSize(Integer.valueOf((int)flyRuleNumbersIPage.getSize()));
       page2.setAllRow(Integer.valueOf((int)flyRuleNumbersIPage.getTotal()));
       page2.setStrCurrentPage(String.valueOf(flyRuleNumbersIPage.getCurrent()));
       return CommResponse.success(page2);
    }
}
