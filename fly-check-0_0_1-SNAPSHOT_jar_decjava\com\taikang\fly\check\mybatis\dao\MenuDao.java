package com.taikang.fly.check.mybatis.dao.MenuDao;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.Menu;
import java.util.List;
import java.util.Map;

public interface abstract MenuDao implements BaseMapper	// class@000200 from classes.dex
{

    Integer deleteById(String p0);
    Menu findById(String p0);
    Menu findByMenuId(String p0);
    List findByOrderByMenuOrder();
    List findByParentId(String p0);
    List findChildrenIdByList(List p0);
    List findMenuListByUser(Map p0);
    List queryAllMenuListByModule(Map p0);
    List roleMenuListByRoleId(String p0);
    Integer save(Menu p0);
    Integer updateMenuById(Menu p0);
}
