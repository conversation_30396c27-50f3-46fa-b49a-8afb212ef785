package com.taikang.fly.check.dto.clickhouseFlyRule.ClickHouseTableVO;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.util.List;
import java.lang.StringBuilder;

public class ClickHouseTableVO	// class@0000bc from classes.dex
{
    private String createSql;
    private Date createTime;
    private String database;
    private List filed;
    private String id;
    private String logPath;
    private String oracleTableName;
    private Date updateTime;

    public void ClickHouseTableVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickHouseTableVO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickHouseTableVO){
          b = false;
       }else {
          ClickHouseTableVO uClickHouseT = o;
          if (!uClickHouseT.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uClickHouseT.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String oracleTableN = this.getOracleTableName();
             String oracleTableN1 = uClickHouseT.getOracleTableName();
             if (oracleTableN == null) {
                if (oracleTableN1 != null) {
                   b = false;
                }
             }else if(oracleTableN.equals(oracleTableN1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = uClickHouseT.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             Date updateTime = this.getUpdateTime();
             Date updateTime1 = uClickHouseT.getUpdateTime();
             if (updateTime == null) {
                if (updateTime1 != null) {
                   b = false;
                }
             }else if(updateTime.equals(updateTime1)){
             }
             String logPath = this.getLogPath();
             String logPath1 = uClickHouseT.getLogPath();
             if (logPath == null) {
                if (logPath1 != null) {
                   b = false;
                }
             }else if(logPath.equals(logPath1)){
             }
             List filed = this.getFiled();
             List filed1 = uClickHouseT.getFiled();
             if (filed == null) {
                if (filed1 != null) {
                label_009f :
                   b = false;
                }
             }else if(filed.equals(filed1)){
             }
             String database = this.getDatabase();
             String database1 = uClickHouseT.getDatabase();
             if (database == null) {
                if (database1 != null) {
                   b = false;
                }
             }else if(database.equals(database1)){
             }
             String createSql = this.getCreateSql();
             String createSql1 = uClickHouseT.getCreateSql();
             if (createSql == null) {
                if (createSql1 != null) {
                label_00cb :
                   b = false;
                }
             }else if(createSql.equals(createSql1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateSql(){
       return this.createSql;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getDatabase(){
       return this.database;
    }
    public List getFiled(){
       return this.filed;
    }
    public String getId(){
       return this.id;
    }
    public String getLogPath(){
       return this.logPath;
    }
    public String getOracleTableName(){
       return this.oracleTableName;
    }
    public Date getUpdateTime(){
       return this.updateTime;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $oracleTableName = this.getOracleTableName();
       int i2 = result * 59;
       i1 = ($oracleTableName == null)? i: $oracleTableName.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       Date $updateTime = this.getUpdateTime();
       i2 = result * 59;
       i1 = ($updateTime == null)? i: $updateTime.hashCode();
       result = i2 + i1;
       String $logPath = this.getLogPath();
       i2 = result * 59;
       i1 = ($logPath == null)? i: $logPath.hashCode();
       result = i2 + i1;
       List filed = this.getFiled();
       i2 = result * 59;
       i1 = (filed == null)? i: filed.hashCode();
       String database = this.getDatabase();
       i2 = (i2 + i1) * 59;
       i1 = (database == null)? i: database.hashCode();
       String createSql = this.getCreateSql();
       i1 = (i2 + i1) * 59;
       if (createSql != null) {
          i = createSql.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateSql(String createSql){
       this.createSql = createSql;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setDatabase(String database){
       this.database = database;
    }
    public void setFiled(List filed){
       this.filed = filed;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setLogPath(String logPath){
       this.logPath = logPath;
    }
    public void setOracleTableName(String oracleTableName){
       this.oracleTableName = oracleTableName;
    }
    public void setUpdateTime(Date updateTime){
       this.updateTime = updateTime;
    }
    public String toString(){
       return "ClickHouseTableVO\(id="+this.getId()+", oracleTableName="+this.getOracleTableName()+", createTime="+this.getCreateTime()+", updateTime="+this.getUpdateTime()+", logPath="+this.getLogPath()+", filed="+this.getFiled()+", database="+this.getDatabase()+", createSql="+this.getCreateSql()+"\)";
    }
}
