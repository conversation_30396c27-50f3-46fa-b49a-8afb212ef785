package org.springframework.boot.loader.LaunchedURLClassLoader$UseFastConnectionExceptionsEnumeration;
import java.util.Enumeration;
import java.lang.Object;
import org.springframework.boot.loader.jar.Handler;
import java.net.URL;

class LaunchedURLClassLoader$UseFastConnectionExceptionsEnumeration implements Enumeration	// class@000530 from classes.dex
{
    private final Enumeration delegate;

    void LaunchedURLClassLoader$UseFastConnectionExceptionsEnumeration(Enumeration delegate){
       super();
       this.delegate = delegate;
    }
    public boolean hasMoreElements(){
       Handler.setUseFastConnectionExceptions(true);
       Handler.setUseFastConnectionExceptions(false);
       return this.delegate.hasMoreElements();
    }
    public Object nextElement(){
       return this.nextElement();
    }
    public URL nextElement(){
       Handler.setUseFastConnectionExceptions(true);
       Handler.setUseFastConnectionExceptions(false);
       return this.delegate.nextElement();
    }
}
