package com.taikang.fly.check.mybatis.dao.ClickhouseFlyRuleTemplateMapper;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRuleTemplate;
import java.util.List;
import java.lang.String;
import java.lang.Integer;

public interface abstract ClickhouseFlyRuleTemplateMapper	// class@0001db from classes.dex
{

    int insert(ClickhouseFlyRuleTemplate p0);
    List selectAll();
    ClickhouseFlyRuleTemplate selectById(String p0);
    ClickhouseFlyRuleTemplate selectByPrimaryKey(String p0);
    List selectPrimaryMenu();
    List selectRuleName();
    List selectSecondaryMenu(String p0);
    Integer updateByPrimaryKey(ClickhouseFlyRuleTemplate p0);
}
