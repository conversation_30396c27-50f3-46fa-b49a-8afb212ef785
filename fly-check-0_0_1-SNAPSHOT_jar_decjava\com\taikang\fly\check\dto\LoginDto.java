package com.taikang.fly.check.dto.LoginDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class LoginDto implements Serializable	// class@0000a6 from classes.dex
{
    private String hospName;
    private String password;
    private String region;
    private String username;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void LoginDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof LoginDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof LoginDto) {
             b = false;
          }else {
             LoginDto loginDto = o;
             if (!loginDto.canEqual(this)) {
                b = false;
             }else {
                String username = this.getUsername();
                String username1 = loginDto.getUsername();
                if (username == null) {
                   if (username1 != null) {
                      b = false;
                   }
                }else if(username.equals(username1)){
                }
                String password = this.getPassword();
                String password1 = loginDto.getPassword();
                if (password == null) {
                   if (password1 != null) {
                      b = false;
                   }
                }else if(password.equals(password1)){
                }
                String region = this.getRegion();
                String region1 = loginDto.getRegion();
                if (region == null) {
                   if (region1 != null) {
                      b = false;
                   }
                }else if(region.equals(region1)){
                }
                String hospName = this.getHospName();
                String hospName1 = loginDto.getHospName();
                if (hospName == null) {
                   if (hospName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!hospName.equals(hospName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getPassword(){
       return this.password;
    }
    public String getRegion(){
       return this.region;
    }
    public String getUsername(){
       return this.username;
    }
    public int hashCode(){
       String $username;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($username = this.getUsername()) == null)? i: $username.hashCode();
       result = i1 + 59;
       String $password = this.getPassword();
       int i2 = result * 59;
       i1 = ($password == null)? i: $password.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $hospName = this.getHospName();
       i1 = result * 59;
       if ($hospName != null) {
          i = $hospName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setUsername(String username){
       this.username = username;
    }
    public String toString(){
       return "LoginDto\(username="+this.getUsername()+", password="+this.getPassword()+", region="+this.getRegion()+", hospName="+this.getHospName()+"\)";
    }
}
