package com.taikang.fly.check.dto.importData.ImportDataDto;
import java.lang.Object;
import java.lang.String;
import java.util.List;

public class ImportDataDto	// class@000133 from classes.dex
{
    private String dataFileName;
    private String hospitalName;
    private boolean isInit;
    private boolean needSplit;
    private String sourceTableSpace;
    private String sourceUserName;
    private List tableList;
    private String type;

    public void ImportDataDto(){
       super();
       this.hospitalName = "default";
       this.isInit = false;
       this.needSplit = false;
    }
    public String getDataFileName(){
       return this.dataFileName;
    }
    public String getHospitalName(){
       return this.hospitalName;
    }
    public String getSourceTableSpace(){
       return this.sourceTableSpace;
    }
    public String getSourceUserName(){
       return this.sourceUserName;
    }
    public List getTableList(){
       return this.tableList;
    }
    public String getType(){
       return this.type;
    }
    public boolean isInit(){
       return this.isInit;
    }
    public boolean isNeedSplit(){
       return this.needSplit;
    }
    public void setDataFileName(String dataFileName){
       this.dataFileName = dataFileName;
    }
    public void setHospitalName(String hospitalName){
       this.hospitalName = hospitalName;
    }
    public void setInit(boolean isInit){
       this.isInit = isInit;
    }
    public void setNeedSplit(boolean needSplit){
       this.needSplit = needSplit;
    }
    public void setSourceTableSpace(String sourceTableSpace){
       this.sourceTableSpace = sourceTableSpace;
    }
    public void setSourceUserName(String sourceUserName){
       this.sourceUserName = sourceUserName;
    }
    public void setTableList(List tableList){
       this.tableList = tableList;
    }
    public void setType(String type){
       this.type = type;
    }
}
