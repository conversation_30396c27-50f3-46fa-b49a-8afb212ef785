#!/usr/bin/env python3
"""
FlyCheck Python 启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    # 确保在项目根目录
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # 检查是否安装了uv
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到uv包管理器，请先安装uv")
        print("安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh")
        sys.exit(1)
    
    # 检查虚拟环境
    venv_path = project_root / ".venv"
    if not venv_path.exists():
        print("🔧 创建虚拟环境...")
        subprocess.run(["uv", "venv"], check=True)
    
    # 安装依赖
    print("📦 安装依赖...")
    subprocess.run(["uv", "pip", "install", "-e", "."], check=True)
    
    # 创建必要的目录
    for dir_name in ["uploads", "results", "logs", "data"]:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
    
    # 初始化数据库
    print("🗄️ 初始化数据库...")
    try:
        subprocess.run([
            "uv", "run", "python", "-m", "flycheck.cli", "db", "init"
        ], check=True)
    except subprocess.CalledProcessError:
        print("⚠️ 数据库初始化失败，可能已经初始化过")
    
    # 创建示例数据
    print("🌱 创建示例数据...")
    try:
        subprocess.run([
            "uv", "run", "python", "-m", "flycheck.cli", "db", "seed"
        ], check=True)
    except subprocess.CalledProcessError:
        print("⚠️ 示例数据创建失败，可能已经存在")
    
    # 启动服务
    print("🚀 启动FlyCheck Python服务...")
    print("访问地址: http://localhost:8000")
    print("API文档: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服务")
    
    try:
        subprocess.run([
            "uv", "run", "uvicorn", "flycheck.main:app", 
            "--reload", "--host", "0.0.0.0", "--port", "8000"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")


if __name__ == "__main__":
    main()
