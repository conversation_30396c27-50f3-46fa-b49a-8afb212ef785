package com.taikang.fly.check.JASYPTTEST;
import java.lang.Object;
import java.lang.String;
import org.jasypt.util.text.BasicTextEncryptor;
import java.lang.System;
import java.lang.StringBuilder;
import java.io.PrintStream;

public class JASYPTTEST	// class@00006d from classes.dex
{

    public void JASYPTTEST(){
       super();
    }
    public static void main(String[] args){
       BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
       textEncryptor.setPassword("Fly-Check_P@ssw0rd");
       String str2 = textEncryptor.decrypt("xWF+Qo16VXd8jvvpMtU8TZCHBHTY2x/FniKVw9xuwoBfm+X1VW/lEIRWl2bqobv1TGbWIl6sR55pmQI2VUl9WUj7wNMgmBtM9pAdbfxtlnbez7juaWPOGxYYHE6UyC2i5IFYNrRVz6zpgiJlVN45riIc4qLVh+5lD03koHwU/lY=");
       System.out.println("str2:"+str2);
    }
}
