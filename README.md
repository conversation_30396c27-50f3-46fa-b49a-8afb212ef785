# FlyRule Python 版

基于 FastAPI + DuckDB 的规则引擎与管理系统示例。

## 主要功能
- 规则的增删改查（CRUD）
- DuckDB 轻量级数据库存储
- FastAPI 提供 RESTful API

## 环境准备
- Python 3.8+
- [uv](https://github.com/astral-sh/uv) 包管理工具

## 安装依赖
```bash
uv pip install -r requirements.txt
```
或直接用 pyproject.toml：
```bash
uv pip install -r pyproject.toml
```

## 启动服务
```bash
uvicorn main:app --reload
```

## API 示例
- 新增规则：POST /api/rule
- 查询规则：GET /api/rule/{id}
- 查询所有规则：GET /api/rule
- 更新规则：PUT /api/rule/{id}
- 删除规则：DELETE /api/rule/{id}

## 说明
- 首次运行会自动创建 duckdb 数据库文件 flyrule.db
- 可根据实际业务扩展更多功能 