package com.taikang.fly.check.mybatis.domain.DrgDrgsGroup;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class DrgDrgsGroup	// class@00023e from classes.dex
{
    private String age;
    private LocalDateTime begntime;
    private LocalDateTime createTime;
    private String drgCode;
    private String drgName;
    private String dscgMaindiagCode;
    private String dscgMaindiagName;
    private String dscgWay;
    private LocalDateTime endtime;
    private String gend;
    private String id;
    private String newDrgCode;
    private String newDrgName;
    private String psnName;
    private String psnNo;

    public void DrgDrgsGroup(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgDrgsGroup;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgDrgsGroup){
          b = false;
       }else {
          DrgDrgsGroup uDrgDrgsGrou = o;
          if (!uDrgDrgsGrou.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDrgDrgsGrou.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String psnName = this.getPsnName();
             String psnName1 = uDrgDrgsGrou.getPsnName();
             if (psnName == null) {
                if (psnName1 != null) {
                   b = false;
                }
             }else if(psnName.equals(psnName1)){
             }
             String psnNo = this.getPsnNo();
             String psnNo1 = uDrgDrgsGrou.getPsnNo();
             if (psnNo == null) {
                if (psnNo1 != null) {
                   b = false;
                }
             }else if(psnNo.equals(psnNo1)){
             }
             String gend = this.getGend();
             String gend1 = uDrgDrgsGrou.getGend();
             if (gend == null) {
                if (gend1 != null) {
                   b = false;
                }
             }else if(gend.equals(gend1)){
             }
             String age = this.getAge();
             String age1 = uDrgDrgsGrou.getAge();
             if (age == null) {
                if (age1 != null) {
                   b = false;
                }
             }else if(age.equals(age1)){
             }
             LocalDateTime begntime = this.getBegntime();
             LocalDateTime begntime1 = uDrgDrgsGrou.getBegntime();
             if (begntime == null) {
                if (begntime1 != null) {
                   b = false;
                }
             }else if(begntime.equals(begntime1)){
             }
             LocalDateTime endtime = this.getEndtime();
             LocalDateTime endtime1 = uDrgDrgsGrou.getEndtime();
             if (endtime == null) {
                if (endtime1 != null) {
                   b = false;
                }
             }else if(endtime.equals(endtime1)){
             }
             String dscgMaindiag = this.getDscgMaindiagCode();
             String dscgMaindiag1 = uDrgDrgsGrou.getDscgMaindiagCode();
             if (dscgMaindiag == null) {
                if (dscgMaindiag1 != null) {
                label_00d7 :
                   b = false;
                }
             }else if(dscgMaindiag.equals(dscgMaindiag1)){
             }
             String dscgMaindiag2 = this.getDscgMaindiagName();
             String dscgMaindiag3 = uDrgDrgsGrou.getDscgMaindiagName();
             if (dscgMaindiag2 == null) {
                if (dscgMaindiag3 != null) {
                   b = false;
                }
             }else if(dscgMaindiag2.equals(dscgMaindiag3)){
             }
             String dscgWay = this.getDscgWay();
             String dscgWay1 = uDrgDrgsGrou.getDscgWay();
             if (dscgWay == null) {
                if (dscgWay1 != null) {
                   b = false;
                }
             }else if(dscgWay.equals(dscgWay1)){
             }
             String drgCode = this.getDrgCode();
             String drgCode1 = uDrgDrgsGrou.getDrgCode();
             if (drgCode == null) {
                if (drgCode1 != null) {
                label_011f :
                   b = false;
                }
             }else if(drgCode.equals(drgCode1)){
             }
             String drgName = this.getDrgName();
             String drgName1 = uDrgDrgsGrou.getDrgName();
             if (drgName == null) {
                if (drgName1 != null) {
                   b = false;
                }
             }else if(drgName.equals(drgName1)){
             }
             String newDrgCode = this.getNewDrgCode();
             String newDrgCode1 = uDrgDrgsGrou.getNewDrgCode();
             if (newDrgCode == null) {
                if (newDrgCode1 != null) {
                label_014f :
                   b = false;
                }
             }else if(newDrgCode.equals(newDrgCode1)){
             }
             String newDrgName = this.getNewDrgName();
             String newDrgName1 = uDrgDrgsGrou.getNewDrgName();
             if (newDrgName == null) {
                if (newDrgName1 != null) {
                   b = false;
                }
             }else if(newDrgName.equals(newDrgName1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = uDrgDrgsGrou.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAge(){
       return this.age;
    }
    public LocalDateTime getBegntime(){
       return this.begntime;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getDrgName(){
       return this.drgName;
    }
    public String getDscgMaindiagCode(){
       return this.dscgMaindiagCode;
    }
    public String getDscgMaindiagName(){
       return this.dscgMaindiagName;
    }
    public String getDscgWay(){
       return this.dscgWay;
    }
    public LocalDateTime getEndtime(){
       return this.endtime;
    }
    public String getGend(){
       return this.gend;
    }
    public String getId(){
       return this.id;
    }
    public String getNewDrgCode(){
       return this.newDrgCode;
    }
    public String getNewDrgName(){
       return this.newDrgName;
    }
    public String getPsnName(){
       return this.psnName;
    }
    public String getPsnNo(){
       return this.psnNo;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $psnName = this.getPsnName();
       int i1 = result * 59;
       i = ($psnName == null)? 43: $psnName.hashCode();
       result = i1 + i;
       String $psnNo = this.getPsnNo();
       i1 = result * 59;
       i = ($psnNo == null)? 43: $psnNo.hashCode();
       result = i1 + i;
       String $gend = this.getGend();
       i1 = result * 59;
       i = ($gend == null)? 43: $gend.hashCode();
       result = i1 + i;
       String $age = this.getAge();
       i1 = result * 59;
       i = ($age == null)? 43: $age.hashCode();
       result = i1 + i;
       LocalDateTime begntime = this.getBegntime();
       i1 = result * 59;
       i = (begntime == null)? 43: begntime.hashCode();
       LocalDateTime endtime = this.getEndtime();
       i1 = (i1 + i) * 59;
       i = (endtime == null)? 43: endtime.hashCode();
       String dscgMaindiag = this.getDscgMaindiagCode();
       i1 = (i1 + i) * 59;
       i = (dscgMaindiag == null)? 43: dscgMaindiag.hashCode();
       String dscgMaindiag1 = this.getDscgMaindiagName();
       i1 = (i1 + i) * 59;
       i = (dscgMaindiag1 == null)? 43: dscgMaindiag1.hashCode();
       String dscgWay = this.getDscgWay();
       i1 = (i1 + i) * 59;
       i = (dscgWay == null)? 43: dscgWay.hashCode();
       String drgCode = this.getDrgCode();
       i1 = (i1 + i) * 59;
       i = (drgCode == null)? 43: drgCode.hashCode();
       String drgName = this.getDrgName();
       i1 = (i1 + i) * 59;
       i = (drgName == null)? 43: drgName.hashCode();
       String newDrgCode = this.getNewDrgCode();
       i1 = (i1 + i) * 59;
       i = (newDrgCode == null)? 43: newDrgCode.hashCode();
       String newDrgName = this.getNewDrgName();
       i1 = (i1 + i) * 59;
       i = (newDrgName == null)? 43: newDrgName.hashCode();
       LocalDateTime createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       return (i1 + i);
    }
    public void setAge(String age){
       this.age = age;
    }
    public void setBegntime(LocalDateTime begntime){
       this.begntime = begntime;
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setDrgName(String drgName){
       this.drgName = drgName;
    }
    public void setDscgMaindiagCode(String dscgMaindiagCode){
       this.dscgMaindiagCode = dscgMaindiagCode;
    }
    public void setDscgMaindiagName(String dscgMaindiagName){
       this.dscgMaindiagName = dscgMaindiagName;
    }
    public void setDscgWay(String dscgWay){
       this.dscgWay = dscgWay;
    }
    public void setEndtime(LocalDateTime endtime){
       this.endtime = endtime;
    }
    public void setGend(String gend){
       this.gend = gend;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewDrgCode(String newDrgCode){
       this.newDrgCode = newDrgCode;
    }
    public void setNewDrgName(String newDrgName){
       this.newDrgName = newDrgName;
    }
    public void setPsnName(String psnName){
       this.psnName = psnName;
    }
    public void setPsnNo(String psnNo){
       this.psnNo = psnNo;
    }
    public String toString(){
       return "DrgDrgsGroup\(id="+this.getId()+", psnName="+this.getPsnName()+", psnNo="+this.getPsnNo()+", gend="+this.getGend()+", age="+this.getAge()+", begntime="+this.getBegntime()+", endtime="+this.getEndtime()+", dscgMaindiagCode="+this.getDscgMaindiagCode()+", dscgMaindiagName="+this.getDscgMaindiagName()+", dscgWay="+this.getDscgWay()+", drgCode="+this.getDrgCode()+", drgName="+this.getDrgName()+", newDrgCode="+this.getNewDrgCode()+", newDrgName="+this.getNewDrgName()+", createTime="+this.getCreateTime()+"\)";
    }
}
