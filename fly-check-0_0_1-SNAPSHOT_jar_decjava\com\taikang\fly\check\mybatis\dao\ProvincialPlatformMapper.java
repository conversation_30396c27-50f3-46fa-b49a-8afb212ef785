package com.taikang.fly.check.mybatis.dao.ProvincialPlatformMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.dto.exportReport.DrgSPTExportEntry;
import java.util.List;
import com.taikang.fly.check.dto.exportReport.DataSPTExportEntry;

public interface abstract ProvincialPlatformMapper implements BaseMapper	// class@000219 from classes.dex
{

    DrgSPTExportEntry drgDataExportCount(String p0);
    List selectC(String p0);
    List selectC1(String p0);
    List selectC2(String p0);
    List selectC3(String p0);
    List selectC4(String p0);
    List selectC5(String p0);
    DataSPTExportEntry selectDataExportCount(String p0);
    List selectInsurance(String p0);
    String selectSPTSql();
    List selectSetlDateColumnType(String p0);
}
