package com.taikang.fly.check.dto.flyRule.FlyRuleStatisSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleStatisSearchDto implements Serializable	// class@00010c from classes.dex
{
    private String operator;
    private String region;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleStatisSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleStatisSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleStatisSearchDto) {
             b = false;
          }else {
             FlyRuleStatisSearchDto uFlyRuleStat = o;
             if (!uFlyRuleStat.canEqual(this)) {
                b = false;
             }else {
                String region = this.getRegion();
                String region1 = uFlyRuleStat.getRegion();
                if (region == null) {
                   if (region1 != null) {
                      b = false;
                   }
                }else if(region.equals(region1)){
                }
                String operator = this.getOperator();
                String operator1 = uFlyRuleStat.getOperator();
                if (operator == null) {
                   if (operator1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!operator.equals(operator1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getOperator(){
       return this.operator;
    }
    public String getRegion(){
       return this.region;
    }
    public int hashCode(){
       String $region;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($region = this.getRegion()) == null)? i: $region.hashCode();
       result = i1 + 59;
       String $operator = this.getOperator();
       i1 = result * 59;
       if ($operator != null) {
          i = $operator.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public String toString(){
       return "FlyRuleStatisSearchDto\(region="+this.getRegion()+", operator="+this.getOperator()+"\)";
    }
}
