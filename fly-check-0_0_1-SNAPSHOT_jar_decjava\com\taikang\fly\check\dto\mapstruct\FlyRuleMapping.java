package com.taikang.fly.check.dto.mapstruct.FlyRuleMapping;
import com.taikang.fly.check.dto.flyRule.FlyRuleAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRule;
import com.taikang.fly.check.dto.flyRule.FlyRuleCommonAddDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleAuditConfirmationDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleRespDto;
import com.taikang.fly.check.dto.flyRule.FlyRulePlanRespDto;
import com.taikang.fly.check.dto.planLog.PlanFlyRuleRespDto;
import java.util.List;
import com.taikang.fly.check.dto.flyRule.FlyRuleEditDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleWorkOrderEditDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleExchangerDto;
import com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditRespDto;
import com.taikang.fly.check.dto.flyRule.TemplateFlyRuleEditDto;

public interface abstract FlyRuleMapping	// class@000156 from classes.dex
{

    FlyRule addDtoToDomain(FlyRuleAddDto p0);
    FlyRule addDtoToDomain(FlyRuleCommonAddDto p0);
    FlyRule confirmationDtoToDomain(FlyRuleAuditConfirmationDto p0);
    FlyRuleRespDto domainToInfoDto(FlyRule p0);
    FlyRulePlanRespDto domainToInfoPlanDto(FlyRule p0);
    PlanFlyRuleRespDto domainToInfoPlanFlyRuleDto(FlyRule p0);
    FlyRuleAddDto domainToaddDto(FlyRule p0);
    FlyRule dtoToDomain(FlyRuleRespDto p0);
    List dtoToDomains(List p0);
    FlyRule editDtoToDomain(FlyRuleEditDto p0,FlyRule p1);
    FlyRule editWorkOrderDtoToDomain(FlyRuleWorkOrderEditDto p0,FlyRule p1);
    List entityToDtos(List p0);
    List entityToPlanDtos(List p0);
    List entityToPlanFlyRuleDtos(List p0);
    List exchangerListToFlyRuleList(List p0);
    FlyRule exchangerToFlyRule(FlyRuleExchangerDto p0);
    List flyRuleListToexchangerList(List p0);
    List flyRuleResListToexchangerList(List p0);
    FlyRuleExchangerDto flyRuleResToExchanger(FlyRuleRespDto p0);
    FlyRuleExchangerDto flyRuleToExchanger(FlyRule p0);
    List ruleAuditDtoListToRuleList(List p0);
    FlyRule ruleAuditDtoToRule(FlyRuleAuditRespDto p0);
    FlyRule templateEditDtoToDomain(TemplateFlyRuleEditDto p0,FlyRule p1);
}
