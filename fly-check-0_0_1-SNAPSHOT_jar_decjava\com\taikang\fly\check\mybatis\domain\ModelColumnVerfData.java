package com.taikang.fly.check.mybatis.domain.ModelColumnVerfData;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelColumnVerfData	// class@000255 from classes.dex
{
    private String columnDesc;
    private String columnName;
    private String columnType;
    private String id;
    private char status;
    private String statusDetail;
    private String tableName;
    private String verfDataId;

    public void ModelColumnVerfData(){
       super();
    }
    public void ModelColumnVerfData(String id,String verfDataId,String tableName,String columnName,String columnType,String columnDesc,char status,String statusDetail){
       super();
       this.id = id;
       this.verfDataId = verfDataId;
       this.tableName = tableName;
       this.columnName = columnName;
       this.columnType = columnType;
       this.columnDesc = columnDesc;
       this.status = status;
       this.statusDetail = statusDetail;
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelColumnVerfData;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModelColumnVerfData){
          b = false;
       }else {
          ModelColumnVerfData modelColumnV = o;
          if (!modelColumnV.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = modelColumnV.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String verfDataId = this.getVerfDataId();
             String verfDataId1 = modelColumnV.getVerfDataId();
             if (verfDataId == null) {
                if (verfDataId1 != null) {
                   b = false;
                }
             }else if(verfDataId.equals(verfDataId1)){
             }
             String tableName = this.getTableName();
             String tableName1 = modelColumnV.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String columnName = this.getColumnName();
             String columnName1 = modelColumnV.getColumnName();
             if (columnName == null) {
                if (columnName1 != null) {
                   b = false;
                }
             }else if(columnName.equals(columnName1)){
             }
             String columnType = this.getColumnType();
             String columnType1 = modelColumnV.getColumnType();
             if (columnType == null) {
                if (columnType1 != null) {
                   b = false;
                }
             }else if(columnType.equals(columnType1)){
             }
             String columnDesc = this.getColumnDesc();
             String columnDesc1 = modelColumnV.getColumnDesc();
             if (columnDesc == null) {
                if (columnDesc1 != null) {
                   b = false;
                }
             }else if(columnDesc.equals(columnDesc1)){
             }
             if (this.getStatus() != modelColumnV.getStatus()) {
                b = false;
             }else {
                String statusDetail = this.getStatusDetail();
                String statusDetail1 = modelColumnV.getStatusDetail();
                if (statusDetail == null) {
                   if (statusDetail1 != null) {
                   label_00c2 :
                      b = false;
                   }
                }else if(statusDetail.equals(statusDetail1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getColumnDesc(){
       return this.columnDesc;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnType(){
       return this.columnType;
    }
    public String getId(){
       return this.id;
    }
    public char getStatus(){
       return this.status;
    }
    public String getStatusDetail(){
       return this.statusDetail;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getVerfDataId(){
       return this.verfDataId;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $verfDataId = this.getVerfDataId();
       int i2 = result * 59;
       i1 = ($verfDataId == null)? i: $verfDataId.hashCode();
       result = i2 + i1;
       String $tableName = this.getTableName();
       i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $columnName = this.getColumnName();
       i2 = result * 59;
       i1 = ($columnName == null)? i: $columnName.hashCode();
       result = i2 + i1;
       String $columnType = this.getColumnType();
       i2 = result * 59;
       i1 = ($columnType == null)? i: $columnType.hashCode();
       result = i2 + i1;
       String columnDesc = this.getColumnDesc();
       i2 = result * 59;
       i1 = (columnDesc == null)? i: columnDesc.hashCode();
       String statusDetail = this.getStatusDetail();
       i1 = (((i2 + i1) * 59) + this.getStatus()) * 59;
       if (statusDetail != null) {
          i = statusDetail.hashCode();
       }
       return (i1 + i);
    }
    public void setColumnDesc(String columnDesc){
       this.columnDesc = columnDesc;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnType(String columnType){
       this.columnType = columnType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setStatus(char status){
       this.status = status;
    }
    public void setStatusDetail(String statusDetail){
       this.statusDetail = statusDetail;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setVerfDataId(String verfDataId){
       this.verfDataId = verfDataId;
    }
    public String toString(){
       return "ModelColumnVerfData\(id="+this.getId()+", verfDataId="+this.getVerfDataId()+", tableName="+this.getTableName()+", columnName="+this.getColumnName()+", columnType="+this.getColumnType()+", columnDesc="+this.getColumnDesc()+", status="+this.getStatus()+", statusDetail="+this.getStatusDetail()+"\)";
    }
}
