package com.taikang.fly.check.interceptor.WebSocketInterceptor;
import org.springframework.web.socket.server.HandshakeInterceptor;
import java.lang.Object;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.socket.WebSocketHandler;
import java.lang.Exception;
import java.util.Map;
import org.springframework.http.server.ServletServerHttpRequest;
import java.util.UUID;
import java.lang.String;
import java.lang.CharSequence;

public class WebSocketInterceptor implements HandshakeInterceptor	// class@0001d6 from classes.dex
{

    public void WebSocketInterceptor(){
       super();
    }
    public void afterHandshake(ServerHttpRequest serverHttpRequest,ServerHttpResponse serverHttpResponse,WebSocketHandler webSocketHandler,Exception e){
    }
    public boolean beforeHandshake(ServerHttpRequest serverHttpRequest,ServerHttpResponse serverHttpResponse,WebSocketHandler webSocketHandler,Map map){
       boolean b;
       if (serverHttpRequest instanceof ServletServerHttpRequest) {
          map.put("user_uuid", UUID.randomUUID().toString().replace("-", ""));
          b = true;
       }else {
          b = false;
       }
       return b;
    }
}
