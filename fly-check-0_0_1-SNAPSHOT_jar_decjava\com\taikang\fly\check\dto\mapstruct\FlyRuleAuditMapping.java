package com.taikang.fly.check.dto.mapstruct.FlyRuleAuditMapping;
import com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditRespDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleAudit;
import java.util.List;

public interface abstract FlyRuleAuditMapping	// class@000150 from classes.dex
{

    FlyRuleAudit flyRuleAuditDtoToFlyRuleAudit(FlyRuleAuditRespDto p0);
    List ruleAuditDtoListToRuleAuditList(List p0);
}
