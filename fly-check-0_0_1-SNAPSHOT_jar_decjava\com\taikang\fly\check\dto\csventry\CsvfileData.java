package com.taikang.fly.check.dto.csventry.CsvfileData;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class CsvfileData	// class@0000d9 from classes.dex
{
    private String fileName;
    private String filePath;
    private List previewFile;
    private String tableHeader;

    public void CsvfileData(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CsvfileData;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof CsvfileData) {
             b = false;
          }else {
             CsvfileData uCsvfileData = o;
             if (!uCsvfileData.canEqual(this)) {
                b = false;
             }else {
                String tableHeader = this.getTableHeader();
                String tableHeader1 = uCsvfileData.getTableHeader();
                if (tableHeader == null) {
                   if (tableHeader1 != null) {
                      b = false;
                   }
                }else if(tableHeader.equals(tableHeader1)){
                }
                String fileName = this.getFileName();
                String fileName1 = uCsvfileData.getFileName();
                if (fileName == null) {
                   if (fileName1 != null) {
                      b = false;
                   }
                }else if(fileName.equals(fileName1)){
                }
                String filePath = this.getFilePath();
                String filePath1 = uCsvfileData.getFilePath();
                if (filePath == null) {
                   if (filePath1 != null) {
                      b = false;
                   }
                }else if(filePath.equals(filePath1)){
                }
                List previewFile = this.getPreviewFile();
                List previewFile1 = uCsvfileData.getPreviewFile();
                if (previewFile == null) {
                   if (previewFile1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!previewFile.equals(previewFile1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFileName(){
       return this.fileName;
    }
    public String getFilePath(){
       return this.filePath;
    }
    public List getPreviewFile(){
       return this.previewFile;
    }
    public String getTableHeader(){
       return this.tableHeader;
    }
    public int hashCode(){
       String $tableHeader;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableHeader = this.getTableHeader()) == null)? i: $tableHeader.hashCode();
       result = i1 + 59;
       String $fileName = this.getFileName();
       int i2 = result * 59;
       i1 = ($fileName == null)? i: $fileName.hashCode();
       result = i2 + i1;
       String $filePath = this.getFilePath();
       i2 = result * 59;
       i1 = ($filePath == null)? i: $filePath.hashCode();
       result = i2 + i1;
       List $previewFile = this.getPreviewFile();
       i1 = result * 59;
       if ($previewFile != null) {
          i = $previewFile.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFileName(String fileName){
       this.fileName = fileName;
    }
    public void setFilePath(String filePath){
       this.filePath = filePath;
    }
    public void setPreviewFile(List previewFile){
       this.previewFile = previewFile;
    }
    public void setTableHeader(String tableHeader){
       this.tableHeader = tableHeader;
    }
    public String toString(){
       return "CsvfileData\(tableHeader="+this.getTableHeader()+", fileName="+this.getFileName()+", filePath="+this.getFilePath()+", previewFile="+this.getPreviewFile()+"\)";
    }
}
