package com.taikang.fly.check.dto.mapstruct.DictTypeMappingImpl;
import com.taikang.fly.check.dto.mapstruct.DictTypeMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.DictType;
import com.taikang.fly.check.dto.dictType.DictTypeDto;
import java.util.Date;
import java.lang.String;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class DictTypeMappingImpl implements DictTypeMapping	// class@00014d from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void DictTypeMappingImpl(){
       super();
    }
    public DictTypeDto toDto(DictType dictType){
       DictTypeDto uDictTypeDto;
       if (dictType == null) {
          uDictTypeDto = null;
       }else {
          uDictTypeDto = new DictTypeDto();
          uDictTypeDto.setCreateTime(this.typeConversionMapper.DateTime2String(dictType.getCreateTime()));
          uDictTypeDto.setModifyTime(this.typeConversionMapper.DateTime2String(dictType.getModifyTime()));
          uDictTypeDto.setId(dictType.getId());
          uDictTypeDto.setDictTypeId(dictType.getDictTypeId());
          uDictTypeDto.setDictTypeName(dictType.getDictTypeName());
          uDictTypeDto.setCreator(dictType.getCreator());
          uDictTypeDto.setModby(dictType.getModby());
          uDictTypeDto.setSignature(dictType.getSignature());
          uDictTypeDto.setDictMark(dictType.getDictMark());
       }
       return uDictTypeDto;
    }
    public List toDtoList(List dictTypeDtos){
       List list;
       if (dictTypeDtos == null) {
          list = null;
       }else {
          list = new ArrayList(dictTypeDtos.size());
          Iterator iterator = dictTypeDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toDto(iterator.next()));
          }
       }
       return list;
    }
    public DictType toEntity(DictTypeDto dictTypeDto){
       DictType uDictType;
       if (dictTypeDto == null) {
          uDictType = null;
       }else {
          uDictType = new DictType();
          uDictType.setCreateTime(this.typeConversionMapper.String2Time(dictTypeDto.getCreateTime()));
          uDictType.setModifyTime(this.typeConversionMapper.String2Time(dictTypeDto.getModifyTime()));
          uDictType.setId(dictTypeDto.getId());
          uDictType.setDictTypeId(dictTypeDto.getDictTypeId());
          uDictType.setDictTypeName(dictTypeDto.getDictTypeName());
          uDictType.setCreator(dictTypeDto.getCreator());
          uDictType.setModby(dictTypeDto.getModby());
          uDictType.setSignature(dictTypeDto.getSignature());
          uDictType.setDictMark(dictTypeDto.getDictMark());
       }
       return uDictType;
    }
    public List toEntityList(List dictTypeDtos){
       List list;
       if (dictTypeDtos == null) {
          list = null;
       }else {
          list = new ArrayList(dictTypeDtos.size());
          Iterator iterator = dictTypeDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toEntity(iterator.next()));
          }
       }
       return list;
    }
}
