package com.taikang.fly.check.service.MenuService;
import java.lang.Object;
import java.lang.String;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.lang.Integer;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taikang.fly.check.mybatis.dao.MenuDao;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.mybatis.domain.Menu;
import com.taikang.fly.check.dto.menu.MenuDto;
import com.taikang.fly.check.dto.mapstruct.MenuMapper;
import com.taikang.fly.check.dto.menu.MenuTreeResDto;
import com.taikang.fly.check.dto.menu.MenuIndexDto;
import com.taikang.fly.check.dto.menu.MenuAddDto;
import java.util.Collection;
import org.springframework.util.CollectionUtils;
import com.taikang.fly.check.dto.menu.MenuEditDto;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import java.util.HashMap;
import java.util.Map;
import com.taikang.fly.check.mybatis.dao.ModuleMapper;
import com.taikang.fly.check.mybatis.domain.Module;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;

public class MenuService	// class@0002ec from classes.dex
{
    private MenuDao menuDao;
    private MenuMapper menuMapper;
    private ModuleMapper moduleMapper;
    private static String VALID;

    static {
       MenuService.VALID = "is_valid";
    }
    public void MenuService(){
       super();
    }
    private void checkMenu(String moduleCode,String parentId,String menuName){
       QueryWrapper queryWrapper = new QueryWrapper();
       if (moduleCode != null) {
          queryWrapper.eq("module_code", moduleCode);
       }
       queryWrapper.eq("menu_name", menuName);
       queryWrapper.eq(MenuService.VALID, Integer.valueOf(1));
       if (StringUtils.isNotBlank(parentId) && "0".equals(parentId)) {
          queryWrapper.eq("parent_id", "0");
          if (this.menuDao.selectCount(queryWrapper).intValue() > 0) {
             throw new BizException(ResponseCodeEnum.MENU_PARENT_EXISTS);
          }
       }else {
          queryWrapper.eq("parent_id", parentId);
          if (this.menuDao.selectCount(queryWrapper).intValue() > 0) {
             throw new BizException(ResponseCodeEnum.MENU_CHILDREN_NAME_EXISTS);
          }
       }
       return;
    }
    private List transLeftMenuList(List menuList,int level,String parentId){
       List menuDtolist = new ArrayList();
       Iterator iterator = menuList.iterator();
       while (iterator.hasNext()) {
          Menu menu = iterator.next();
          if (parentId.equals(menu.getParentId())) {
             MenuDto menuDto = this.menuMapper.addToList(menu, level);
             int i = level + 1;
             menuDto.setChildren(this.transLeftMenuList(menuList, i, menu.getMenuId()));
             menuDtolist.add(menuDto);
          }
       }
       return menuDtolist;
    }
    private List transMenuTreeJson(List menuList,String parentId){
       List menuTreeList = null;
       Iterator iterator = menuList.iterator();
       while (iterator.hasNext()) {
          Menu menu = iterator.next();
          if (parentId.equals(menu.getParentId())) {
             if (menuTreeList == null) {
                menuTreeList = new ArrayList();
             }
             MenuTreeResDto menuTreeResD = new MenuTreeResDto();
             menuTreeResD.setId(menu.getMenuId());
             menuTreeResD.setText(menu.getMenuName());
             menuTreeResD.setUrl(menu.getUrl());
             menuTreeResD.setIcon(menu.getIcon());
             menuTreeResD.setIclass(menu.getIclass());
             menuTreeResD.setChildren(this.transMenuTreeJson(menuList, menu.getMenuId()));
             menuTreeList.add(menuTreeResD);
          }
       }
       return menuTreeList;
    }
    private void transTreeMenuList(List menuIndexDtoList,List menuList,int level,String parentId){
       Iterator iterator = menuList.iterator();
       while (iterator.hasNext()) {
          Menu menu = iterator.next();
          if (parentId.equals(menu.getParentId())) {
             menuIndexDtoList.add(this.menuMapper.menuToDto(menu, level, parentId));
             if (!"1".equals(menu.getIsLeaf())) {
                int i = level + 1;
                this.transTreeMenuList(menuIndexDtoList, menuList, i, menu.getMenuId());
             }
          }
       }
       return;
    }
    public Menu addMenu(MenuAddDto menuAddDto){
       Menu menu = this.menuMapper.addMenuToMenu(menuAddDto);
       if (StringUtils.isNotBlank(menuAddDto.getUrl())) {
          menu.setAclass("J_menuItem");
       }
       this.checkMenu(menu.getModuleCode(), menu.getParentId(), menu.getMenuName());
       this.menuDao.save(menu);
       if (!"0".equals(menuAddDto.getParentId())) {
          Menu menux = this.menuDao.findById(menuAddDto.getParentId());
          menux.setIsLeaf("2");
          this.menuDao.updateMenuById(menux);
       }
       return menu;
    }
    public ResponseCodeEnum delMenu(String menuId){
       List list;
       List childMenus = this.menuDao.findByParentId(menuId);
       if (!CollectionUtils.isEmpty(childMenus)) {
          throw new BizException(ResponseCodeEnum.MENU_CHILDREN_EXISTS);
       }
       Menu menu = this.menuDao.findByMenuId(menuId);
       this.menuDao.deleteById(menuId);
       if (!"0".equals(menu.getParentId()) && ((list = this.menuDao.findByParentId(menu.getParentId())) != null && !list.size())) {
          Menu menu1 = this.menuDao.findById(menu.getParentId());
          menu1.setIsLeaf("1");
          this.menuDao.updateMenuById(menu1);
       }
       return ResponseCodeEnum.SUCCESS;
    }
    public Integer editMenu(MenuEditDto menuEditDto){
       String str = null;
       Menu menu = this.menuDao.findByMenuId(menuEditDto.getMenuId());
       if (StringUtils.isNotBlank(menuEditDto.getUrl())) {
          menu.setAclass("J_menuItem");
       }else {
          menu.setAclass(str);
       }
       if (!menuEditDto.getMenuName().equals(menu.getMenuName())) {
          this.checkMenu(str, menu.getParentId(), menuEditDto.getMenuName());
       }
       menu = this.menuMapper.editMenuToMenu(menuEditDto, menu);
       return Integer.valueOf(this.menuDao.update(menu, new UpdateWrapper().eq("menu_id", menu.getMenuId()).eq(MenuService.VALID, "1")));
    }
    public List getAllMenuTreesByModule(String moduleCode){
       Map params = new HashMap(1);
       params.put("moduleCode", moduleCode);
       List menus = this.menuDao.queryAllMenuListByModule(params);
       List menuDtos = null;
       if (!CollectionUtils.isEmpty(menus)) {
          menuDtos = this.transLeftMenuList(menus, 1, "0");
       }
       return menuDtos;
    }
    public List getAllMouduleMenuTrees(){
       List menuDtos = new ArrayList();
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq(MenuService.VALID, "1");
       String[] stringArray = new String[]{"module_order"};
       queryWrapper.orderByAsc(stringArray);
       List modules = this.moduleMapper.selectList(queryWrapper);
       if (!CollectionUtils.isEmpty(modules)) {
          Iterator iterator = modules.iterator();
          while (iterator.hasNext()) {
             Module modulex = iterator.next();
             MenuDto menuDto = new MenuDto();
             String moduleCode = modulex.getModuleCode();
             List allMenuTreesByModule = this.getAllMenuTreesByModule(moduleCode);
             menuDto.setChildren(allMenuTreesByModule);
             menuDto.setMenuName(modulex.getModuleName());
             menuDto.setId(moduleCode);
             menuDtos.add(menuDto);
          }
       }
       return menuDtos;
    }
    public List getMenuTreeList(){
       List menuList = this.menuDao.findByOrderByMenuOrder();
       List menuIndexDtoList = new ArrayList();
       this.transTreeMenuList(menuIndexDtoList, menuList, 1, "0");
       return menuIndexDtoList;
    }
    public List getUserLeftMenuTrees(String moduleCode){
       Map params = new HashMap(2);
       UserDto userInfo = ThreadLocalContextHolder.getContext().getUserInfo();
       params.put("userCode", userInfo.getUserCode());
       params.put("moduleCode", moduleCode);
       List menuList = this.menuDao.findMenuListByUser(params);
       return this.transLeftMenuList(menuList, 1, "0");
    }
    public List menuTreeInitJson(){
       List menuList = this.menuDao.findByOrderByMenuOrder();
       List resultList = this.transMenuTreeJson(menuList, "0");
       return resultList;
    }
    public List roleMenuListByRoleId(){
       String roleCode = "1";
       List menuList = this.menuDao.roleMenuListByRoleId(roleCode);
       List resultList = this.transMenuTreeJson(menuList, "0");
       return resultList;
    }
}
