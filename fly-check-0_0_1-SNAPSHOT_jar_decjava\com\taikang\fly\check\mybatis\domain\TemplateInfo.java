package com.taikang.fly.check.mybatis.domain.TemplateInfo;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class TemplateInfo	// class@00026f from classes.dex
{
    private String content;
    private LocalDateTime createdTime;
    private String creator;
    private String explain;
    private String fieldItems;
    private String id;
    private String manufacturer;
    private String modby;
    private LocalDateTime modifyTime;
    private String sourceTableName;
    private String status;
    private String tableName;
    private String whereCondition;

    public void TemplateInfo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TemplateInfo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof TemplateInfo){
          b = false;
       }else {
          TemplateInfo templateInfo = o;
          if (!templateInfo.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = templateInfo.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String tableName = this.getTableName();
             String tableName1 = templateInfo.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String fieldItems = this.getFieldItems();
             String fieldItems1 = templateInfo.getFieldItems();
             if (fieldItems == null) {
                if (fieldItems1 != null) {
                   b = false;
                }
             }else if(fieldItems.equals(fieldItems1)){
             }
             String sourceTableN = this.getSourceTableName();
             String sourceTableN1 = templateInfo.getSourceTableName();
             if (sourceTableN == null) {
                if (sourceTableN1 != null) {
                   b = false;
                }
             }else if(sourceTableN.equals(sourceTableN1)){
             }
             String whereConditi = this.getWhereCondition();
             String whereConditi1 = templateInfo.getWhereCondition();
             if (whereConditi == null) {
                if (whereConditi1 != null) {
                   b = false;
                }
             }else if(whereConditi.equals(whereConditi1)){
             }
             String content = this.getContent();
             String content1 = templateInfo.getContent();
             if (content == null) {
                if (content1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(content.equals(content1)){
             }
             String creator = this.getCreator();
             String creator1 = templateInfo.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createdTime = this.getCreatedTime();
             LocalDateTime createdTime1 = templateInfo.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modby = this.getModby();
             String modby1 = templateInfo.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = templateInfo.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String explain = this.getExplain();
             String explain1 = templateInfo.getExplain();
             if (explain == null) {
                if (explain1 != null) {
                   b = false;
                }
             }else if(explain.equals(explain1)){
             }
             String manufacturer = this.getManufacturer();
             String manufacturer1 = templateInfo.getManufacturer();
             if (manufacturer == null) {
                if (manufacturer1 != null) {
                   b = false;
                }
             }else if(manufacturer.equals(manufacturer1)){
             }
             String status = this.getStatus();
             String status1 = templateInfo.getStatus();
             if (status == null) {
                if (status1 != null) {
                label_014b :
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getContent(){
       return this.content;
    }
    public LocalDateTime getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getExplain(){
       return this.explain;
    }
    public String getFieldItems(){
       return this.fieldItems;
    }
    public String getId(){
       return this.id;
    }
    public String getManufacturer(){
       return this.manufacturer;
    }
    public String getModby(){
       return this.modby;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getSourceTableName(){
       return this.sourceTableName;
    }
    public String getStatus(){
       return this.status;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getWhereCondition(){
       return this.whereCondition;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $tableName = this.getTableName();
       int i1 = result * 59;
       i = ($tableName == null)? 43: $tableName.hashCode();
       result = i1 + i;
       String $fieldItems = this.getFieldItems();
       i1 = result * 59;
       i = ($fieldItems == null)? 43: $fieldItems.hashCode();
       result = i1 + i;
       String $sourceTableName = this.getSourceTableName();
       i1 = result * 59;
       i = ($sourceTableName == null)? 43: $sourceTableName.hashCode();
       result = i1 + i;
       String $whereCondition = this.getWhereCondition();
       i1 = result * 59;
       i = ($whereCondition == null)? 43: $whereCondition.hashCode();
       result = i1 + i;
       String content = this.getContent();
       i1 = result * 59;
       i = (content == null)? 43: content.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       LocalDateTime createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String explain = this.getExplain();
       i1 = (i1 + i) * 59;
       i = (explain == null)? 43: explain.hashCode();
       String manufacturer = this.getManufacturer();
       i1 = (i1 + i) * 59;
       i = (manufacturer == null)? 43: manufacturer.hashCode();
       String status = this.getStatus();
       i1 = (i1 + i) * 59;
       i = (status == null)? 43: status.hashCode();
       return (i1 + i);
    }
    public TemplateInfo setContent(String content){
       this.content = content;
       return this;
    }
    public TemplateInfo setCreatedTime(LocalDateTime createdTime){
       this.createdTime = createdTime;
       return this;
    }
    public TemplateInfo setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public TemplateInfo setExplain(String explain){
       this.explain = explain;
       return this;
    }
    public TemplateInfo setFieldItems(String fieldItems){
       this.fieldItems = fieldItems;
       return this;
    }
    public TemplateInfo setId(String id){
       this.id = id;
       return this;
    }
    public TemplateInfo setManufacturer(String manufacturer){
       this.manufacturer = manufacturer;
       return this;
    }
    public TemplateInfo setModby(String modby){
       this.modby = modby;
       return this;
    }
    public TemplateInfo setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public TemplateInfo setSourceTableName(String sourceTableName){
       this.sourceTableName = sourceTableName;
       return this;
    }
    public TemplateInfo setStatus(String status){
       this.status = status;
       return this;
    }
    public TemplateInfo setTableName(String tableName){
       this.tableName = tableName;
       return this;
    }
    public TemplateInfo setWhereCondition(String whereCondition){
       this.whereCondition = whereCondition;
       return this;
    }
    public String toString(){
       return "TemplateInfo\(id="+this.getId()+", tableName="+this.getTableName()+", fieldItems="+this.getFieldItems()+", sourceTableName="+this.getSourceTableName()+", whereCondition="+this.getWhereCondition()+", content="+this.getContent()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", explain="+this.getExplain()+", manufacturer="+this.getManufacturer()+", status="+this.getStatus()+"\)";
    }
}
