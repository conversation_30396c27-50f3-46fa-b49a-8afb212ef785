package com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleRespDTO;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleRespDTO implements Serializable	// class@000116 from classes.dex
{
    private String diagnosticName;
    private String paramCode;
    private String paramDesc;
    private String paramName;
    private String paramRuleName;
    private String paramType;
    private String policyBasis;
    private String ruleSql;
    private String ruleparam;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleRespDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleRespDTO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleRespDTO){
          b = false;
       }else {
          FlyRuleRespDTO uFlyRuleResp = o;
          if (!uFlyRuleResp.canEqual(this)) {
             b = false;
          }else {
             String paramCode = this.getParamCode();
             String paramCode1 = uFlyRuleResp.getParamCode();
             if (paramCode == null) {
                if (paramCode1 != null) {
                   b = false;
                }
             }else if(paramCode.equals(paramCode1)){
             }
             String paramName = this.getParamName();
             String paramName1 = uFlyRuleResp.getParamName();
             if (paramName == null) {
                if (paramName1 != null) {
                   b = false;
                }
             }else if(paramName.equals(paramName1)){
             }
             String paramDesc = this.getParamDesc();
             String paramDesc1 = uFlyRuleResp.getParamDesc();
             if (paramDesc == null) {
                if (paramDesc1 != null) {
                   b = false;
                }
             }else if(paramDesc.equals(paramDesc1)){
             }
             String paramType = this.getParamType();
             String paramType1 = uFlyRuleResp.getParamType();
             if (paramType == null) {
                if (paramType1 != null) {
                   b = false;
                }
             }else if(paramType.equals(paramType1)){
             }
             String paramRuleNam = this.getParamRuleName();
             String paramRuleNam1 = uFlyRuleResp.getParamRuleName();
             if (paramRuleNam == null) {
                if (paramRuleNam1 != null) {
                   b = false;
                }
             }else if(paramRuleNam.equals(paramRuleNam1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = uFlyRuleResp.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                label_009c :
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             String ruleparam = this.getRuleparam();
             String ruleparam1 = uFlyRuleResp.getRuleparam();
             if (ruleparam == null) {
                if (ruleparam1 != null) {
                   b = false;
                }
             }else if(ruleparam.equals(ruleparam1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleResp.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_00cc :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String diagnosticNa = this.getDiagnosticName();
             String diagnosticNa1 = uFlyRuleResp.getDiagnosticName();
             if (diagnosticNa == null) {
                if (diagnosticNa1 != null) {
                   b = false;
                }
             }else if(diagnosticNa.equals(diagnosticNa1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosticName(){
       return this.diagnosticName;
    }
    public String getParamCode(){
       return this.paramCode;
    }
    public String getParamDesc(){
       return this.paramDesc;
    }
    public String getParamName(){
       return this.paramName;
    }
    public String getParamRuleName(){
       return this.paramRuleName;
    }
    public String getParamType(){
       return this.paramType;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleparam(){
       return this.ruleparam;
    }
    public int hashCode(){
       String $paramCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($paramCode = this.getParamCode()) == null)? i: $paramCode.hashCode();
       result = i1 + 59;
       String $paramName = this.getParamName();
       int i2 = result * 59;
       i1 = ($paramName == null)? i: $paramName.hashCode();
       result = i2 + i1;
       String $paramDesc = this.getParamDesc();
       i2 = result * 59;
       i1 = ($paramDesc == null)? i: $paramDesc.hashCode();
       result = i2 + i1;
       String $paramType = this.getParamType();
       i2 = result * 59;
       i1 = ($paramType == null)? i: $paramType.hashCode();
       result = i2 + i1;
       String $paramRuleName = this.getParamRuleName();
       i2 = result * 59;
       i1 = ($paramRuleName == null)? i: $paramRuleName.hashCode();
       result = i2 + i1;
       String ruleSql = this.getRuleSql();
       i2 = result * 59;
       i1 = (ruleSql == null)? i: ruleSql.hashCode();
       String ruleparam = this.getRuleparam();
       i2 = (i2 + i1) * 59;
       i1 = (ruleparam == null)? i: ruleparam.hashCode();
       String policyBasis = this.getPolicyBasis();
       i2 = (i2 + i1) * 59;
       i1 = (policyBasis == null)? i: policyBasis.hashCode();
       String diagnosticNa = this.getDiagnosticName();
       i1 = (i2 + i1) * 59;
       if (diagnosticNa != null) {
          i = diagnosticNa.hashCode();
       }
       return (i1 + i);
    }
    public void setDiagnosticName(String diagnosticName){
       this.diagnosticName = diagnosticName;
    }
    public void setParamCode(String paramCode){
       this.paramCode = paramCode;
    }
    public void setParamDesc(String paramDesc){
       this.paramDesc = paramDesc;
    }
    public void setParamName(String paramName){
       this.paramName = paramName;
    }
    public void setParamRuleName(String paramRuleName){
       this.paramRuleName = paramRuleName;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleparam(String ruleparam){
       this.ruleparam = ruleparam;
    }
    public String toString(){
       return "FlyRuleRespDTO\(paramCode="+this.getParamCode()+", paramName="+this.getParamName()+", paramDesc="+this.getParamDesc()+", paramType="+this.getParamType()+", paramRuleName="+this.getParamRuleName()+", ruleSql="+this.getRuleSql()+", ruleparam="+this.getRuleparam()+", policyBasis="+this.getPolicyBasis()+", diagnosticName="+this.getDiagnosticName()+"\)";
    }
}
