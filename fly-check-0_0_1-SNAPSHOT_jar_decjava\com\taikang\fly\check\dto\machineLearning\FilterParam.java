package com.taikang.fly.check.dto.machineLearning.FilterParam;
import java.lang.Object;
import java.time.LocalDate;
import java.lang.String;
import java.lang.StringBuilder;

public class FilterParam	// class@00013c from classes.dex
{
    LocalDate endTime;
    String param;
    LocalDate startTime;

    public void FilterParam(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FilterParam;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FilterParam) {
             b = false;
          }else {
             FilterParam uFilterParam = o;
             if (!uFilterParam.canEqual(this)) {
                b = false;
             }else {
                LocalDate startTime = this.getStartTime();
                LocalDate startTime1 = uFilterParam.getStartTime();
                if (startTime == null) {
                   if (startTime1 != null) {
                      b = false;
                   }
                }else if(startTime.equals(startTime1)){
                }
                LocalDate endTime = this.getEndTime();
                LocalDate endTime1 = uFilterParam.getEndTime();
                if (endTime == null) {
                   if (endTime1 != null) {
                      b = false;
                   }
                }else if(endTime.equals(endTime1)){
                }
                String param = this.getParam();
                String param1 = uFilterParam.getParam();
                if (param == null) {
                   if (param1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!param.equals(param1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public LocalDate getEndTime(){
       return this.endTime;
    }
    public String getParam(){
       return this.param;
    }
    public LocalDate getStartTime(){
       return this.startTime;
    }
    public int hashCode(){
       LocalDate $startTime;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($startTime = this.getStartTime()) == null)? i: $startTime.hashCode();
       result = i1 + 59;
       LocalDate $endTime = this.getEndTime();
       int i2 = result * 59;
       i1 = ($endTime == null)? i: $endTime.hashCode();
       result = i2 + i1;
       String $param = this.getParam();
       i1 = result * 59;
       if ($param != null) {
          i = $param.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setEndTime(LocalDate endTime){
       this.endTime = endTime;
    }
    public void setParam(String param){
       this.param = param;
    }
    public void setStartTime(LocalDate startTime){
       this.startTime = startTime;
    }
    public String toString(){
       return "FilterParam\(startTime="+this.getStartTime()+", endTime="+this.getEndTime()+", param="+this.getParam()+"\)";
    }
}
