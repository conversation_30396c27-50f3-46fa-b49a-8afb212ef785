package com.taikang.fly.check.utils.SpringContextUtil;
import org.springframework.context.ApplicationContextAware;
import java.lang.Object;
import java.lang.Class;
import org.springframework.context.ApplicationContext;
import java.lang.String;

public class SpringContextUtil implements ApplicationContextAware	// class@00034b from classes.dex
{
    private static ApplicationContext context;

    static {
       SpringContextUtil.context = null;
    }
    public void SpringContextUtil(){
       super();
    }
    public static Object getBean(Class requiredType){
       return SpringContextUtil.context.getBean(requiredType);
    }
    public static Object getBean(String name){
       return SpringContextUtil.context.getBean(name);
    }
    public void setApplicationContext(ApplicationContext applicationContext){
       if (SpringContextUtil.context == null) {
          SpringContextUtil.context = applicationContext;
       }
       return;
    }
}
