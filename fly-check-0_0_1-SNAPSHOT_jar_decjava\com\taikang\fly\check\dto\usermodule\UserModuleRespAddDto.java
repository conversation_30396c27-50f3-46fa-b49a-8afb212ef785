package com.taikang.fly.check.dto.usermodule.UserModuleRespAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserModuleRespAddDto implements Serializable	// class@0001c5 from classes.dex
{
    private String moduleCode;
    private String signature;
    private String userCode;
    private static final long serialVersionUID = 0x349d06aa3e3fa7be;

    public void UserModuleRespAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserModuleRespAddDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserModuleRespAddDto) {
             b = false;
          }else {
             UserModuleRespAddDto userModuleRe = o;
             if (!userModuleRe.canEqual(this)) {
                b = false;
             }else {
                String userCode = this.getUserCode();
                String userCode1 = userModuleRe.getUserCode();
                if (userCode == null) {
                   if (userCode1 != null) {
                      b = false;
                   }
                }else if(userCode.equals(userCode1)){
                }
                String moduleCode = this.getModuleCode();
                String moduleCode1 = userModuleRe.getModuleCode();
                if (moduleCode == null) {
                   if (moduleCode1 != null) {
                      b = false;
                   }
                }else if(moduleCode.equals(moduleCode1)){
                }
                String signature = this.getSignature();
                String signature1 = userModuleRe.getSignature();
                if (signature == null) {
                   if (signature1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!signature.equals(signature1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $userCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($userCode = this.getUserCode()) == null)? i: $userCode.hashCode();
       result = i1 + 59;
       String $moduleCode = this.getModuleCode();
       int i2 = result * 59;
       i1 = ($moduleCode == null)? i: $moduleCode.hashCode();
       result = i2 + i1;
       String $signature = this.getSignature();
       i1 = result * 59;
       if ($signature != null) {
          i = $signature.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserModuleRespAddDto\(userCode="+this.getUserCode()+", moduleCode="+this.getModuleCode()+", signature="+this.getSignature()+"\)";
    }
}
