package com.taikang.fly.check.service.impl.drg.DrgTkengVolaRsltServiceImpl;
import com.taikang.fly.check.service.drg.DrgTkengVolaRsltService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DrgTkengVolaRsltServiceImpl extends ServiceImpl implements DrgTkengVolaRsltService	// class@000326 from classes.dex
{
    private static final Logger log;

    static {
       DrgTkengVolaRsltServiceImpl.log = LoggerFactory.getLogger(DrgTkengVolaRsltServiceImpl.class);
    }
    public void DrgTkengVolaRsltServiceImpl(){
       super();
    }
}
