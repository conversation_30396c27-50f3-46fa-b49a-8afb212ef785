package com.taikang.fly.check.mybatis.domain.ProvinceCityDistrict;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ProvinceCityDistrict	// class@000266 from classes.dex
{
    private String id;
    private String name;
    private String pid;

    public void ProvinceCityDistrict(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ProvinceCityDistrict;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ProvinceCityDistrict) {
             b = false;
          }else {
             ProvinceCityDistrict provinceCity = o;
             if (!provinceCity.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = provinceCity.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String pid = this.getPid();
                String pid1 = provinceCity.getPid();
                if (pid == null) {
                   if (pid1 != null) {
                      b = false;
                   }
                }else if(pid.equals(pid1)){
                }
                String name = this.getName();
                String name1 = provinceCity.getName();
                if (name == null) {
                   if (name1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!name.equals(name1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getName(){
       return this.name;
    }
    public String getPid(){
       return this.pid;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $pid = this.getPid();
       int i2 = result * 59;
       i1 = ($pid == null)? i: $pid.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i1 = result * 59;
       if ($name != null) {
          i = $name.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setPid(String pid){
       this.pid = pid;
    }
    public String toString(){
       return "ProvinceCityDistrict\(id="+this.getId()+", pid="+this.getPid()+", name="+this.getName()+"\)";
    }
}
