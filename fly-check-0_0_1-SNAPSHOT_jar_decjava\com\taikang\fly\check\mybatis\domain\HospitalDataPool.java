package com.taikang.fly.check.mybatis.domain.HospitalDataPool;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class HospitalDataPool	// class@00024c from classes.dex
{
    private String categoryCost;
    private String categoryExpense;
    private String consumablesDataList;
    private String dataCategory;
    private String dataSummary;
    private String daySum;
    private String department;
    private String departmentCost;
    private String drugsDataList;
    private String highRembursementRatio;
    private String id;
    private String inspectionPropotion;
    private String lowRembursementRatio;
    private String maxHospitalCost;
    private String maxLengthStay;
    private String nursing;
    private String proportionDrugs;
    private String region;
    private String subjectDataList;
    private String wholeDataList;
    private String year;

    public void HospitalDataPool(){
       super();
    }
    public void HospitalDataPool(String id,String region,String year,String dataCategory){
       super();
       this.id = id;
       this.region = region;
       this.year = year;
       this.dataCategory = dataCategory;
    }
    public void HospitalDataPool(String id,String region,String year,String dataCategory,String dataSummary,String categoryCost,String categoryExpense,String departmentCost,String department,String wholeDataList,String drugsDataList,String consumablesDataList,String subjectDataList,String daySum,String maxLengthStay,String maxHospitalCost,String lowRembursementRatio,String highRembursementRatio,String proportionDrugs,String inspectionPropotion,String nursing){
       super();
       this.id = id;
       this.region = region;
       this.year = year;
       this.dataCategory = dataCategory;
       this.dataSummary = dataSummary;
       this.categoryCost = categoryCost;
       this.categoryExpense = categoryExpense;
       this.departmentCost = departmentCost;
       this.department = department;
       this.wholeDataList = wholeDataList;
       this.drugsDataList = drugsDataList;
       this.consumablesDataList = consumablesDataList;
       this.subjectDataList = subjectDataList;
       this.daySum = daySum;
       this.maxLengthStay = maxLengthStay;
       this.maxHospitalCost = maxHospitalCost;
       this.lowRembursementRatio = lowRembursementRatio;
       this.highRembursementRatio = highRembursementRatio;
       this.proportionDrugs = proportionDrugs;
       this.inspectionPropotion = inspectionPropotion;
       this.nursing = nursing;
    }
    protected boolean canEqual(Object other){
       return other instanceof HospitalDataPool;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof HospitalDataPool){
          b = false;
       }else {
          HospitalDataPool hospitalData = o;
          if (!hospitalData.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = hospitalData.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String region = this.getRegion();
             String region1 = hospitalData.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String year = this.getYear();
             String year1 = hospitalData.getYear();
             if (year == null) {
                if (year1 != null) {
                   b = false;
                }
             }else if(year.equals(year1)){
             }
             String dataCategory = this.getDataCategory();
             String dataCategory1 = hospitalData.getDataCategory();
             if (dataCategory == null) {
                if (dataCategory1 != null) {
                   b = false;
                }
             }else if(dataCategory.equals(dataCategory1)){
             }
             String dataSummary = this.getDataSummary();
             String dataSummary1 = hospitalData.getDataSummary();
             if (dataSummary == null) {
                if (dataSummary1 != null) {
                   b = false;
                }
             }else if(dataSummary.equals(dataSummary1)){
             }
             String categoryCost = this.getCategoryCost();
             String categoryCost1 = hospitalData.getCategoryCost();
             if (categoryCost == null) {
                if (categoryCost1 != null) {
                label_00a7 :
                   b = false;
                }
             }else if(categoryCost.equals(categoryCost1)){
             }
             String categoryExpe = this.getCategoryExpense();
             String categoryExpe1 = hospitalData.getCategoryExpense();
             if (categoryExpe == null) {
                if (categoryExpe1 != null) {
                   b = false;
                }
             }else if(categoryExpe.equals(categoryExpe1)){
             }
             String departmentCo = this.getDepartmentCost();
             String departmentCo1 = hospitalData.getDepartmentCost();
             if (departmentCo == null) {
                if (departmentCo1 != null) {
                   b = false;
                }
             }else if(departmentCo.equals(departmentCo1)){
             }
             String department = this.getDepartment();
             String department1 = hospitalData.getDepartment();
             if (department == null) {
                if (department1 != null) {
                label_00ef :
                   b = false;
                }
             }else if(department.equals(department1)){
             }
             String wholeDataLis = this.getWholeDataList();
             String wholeDataLis1 = hospitalData.getWholeDataList();
             if (wholeDataLis == null) {
                if (wholeDataLis1 != null) {
                   b = false;
                }
             }else if(wholeDataLis.equals(wholeDataLis1)){
             }
             String drugsDataLis = this.getDrugsDataList();
             String drugsDataLis1 = hospitalData.getDrugsDataList();
             if (drugsDataLis == null) {
                if (drugsDataLis1 != null) {
                label_0121 :
                   b = false;
                }
             }else if(drugsDataLis.equals(drugsDataLis1)){
             }
             String consumablesD = this.getConsumablesDataList();
             String consumablesD1 = hospitalData.getConsumablesDataList();
             if (consumablesD == null) {
                if (consumablesD1 != null) {
                   b = false;
                }
             }else if(consumablesD.equals(consumablesD1)){
             }
             String subjectDataL = this.getSubjectDataList();
             String subjectDataL1 = hospitalData.getSubjectDataList();
             if (subjectDataL == null) {
                if (subjectDataL1 != null) {
                label_0151 :
                   b = false;
                }
             }else if(subjectDataL.equals(subjectDataL1)){
             }
             String daySum = this.getDaySum();
             String daySum1 = hospitalData.getDaySum();
             if (daySum == null) {
                if (daySum1 != null) {
                   b = false;
                }
             }else if(daySum.equals(daySum1)){
             }
             String maxLengthSta = this.getMaxLengthStay();
             String maxLengthSta1 = hospitalData.getMaxLengthStay();
             if (maxLengthSta == null) {
                if (maxLengthSta1 != null) {
                label_0183 :
                   b = false;
                }
             }else if(maxLengthSta.equals(maxLengthSta1)){
             }
             String maxHospitalC = this.getMaxHospitalCost();
             String maxHospitalC1 = hospitalData.getMaxHospitalCost();
             if (maxHospitalC == null) {
                if (maxHospitalC1 != null) {
                   b = false;
                }
             }else if(maxHospitalC.equals(maxHospitalC1)){
             }
             String lowRembursem = this.getLowRembursementRatio();
             String lowRembursem1 = hospitalData.getLowRembursementRatio();
             if (lowRembursem == null) {
                if (lowRembursem1 != null) {
                label_01b7 :
                   b = false;
                }
             }else if(lowRembursem.equals(lowRembursem1)){
             }
             String highRemburse = this.getHighRembursementRatio();
             String highRemburse1 = hospitalData.getHighRembursementRatio();
             if (highRemburse == null) {
                if (highRemburse1 != null) {
                   b = false;
                }
             }else if(highRemburse.equals(highRemburse1)){
             }
             String proportionDr = this.getProportionDrugs();
             String proportionDr1 = hospitalData.getProportionDrugs();
             if (proportionDr == null) {
                if (proportionDr1 != null) {
                label_01e7 :
                   b = false;
                }
             }else if(proportionDr.equals(proportionDr1)){
             }
             String inspectionPr = this.getInspectionPropotion();
             String inspectionPr1 = hospitalData.getInspectionPropotion();
             if (inspectionPr == null) {
                if (inspectionPr1 != null) {
                   b = false;
                }
             }else if(inspectionPr.equals(inspectionPr1)){
             }
             String nursing = this.getNursing();
             String nursing1 = hospitalData.getNursing();
             if (nursing == null) {
                if (nursing1 != null) {
                   b = false;
                }
             }else if(nursing.equals(nursing1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCategoryCost(){
       return this.categoryCost;
    }
    public String getCategoryExpense(){
       return this.categoryExpense;
    }
    public String getConsumablesDataList(){
       return this.consumablesDataList;
    }
    public String getDataCategory(){
       return this.dataCategory;
    }
    public String getDataSummary(){
       return this.dataSummary;
    }
    public String getDaySum(){
       return this.daySum;
    }
    public String getDepartment(){
       return this.department;
    }
    public String getDepartmentCost(){
       return this.departmentCost;
    }
    public String getDrugsDataList(){
       return this.drugsDataList;
    }
    public String getHighRembursementRatio(){
       return this.highRembursementRatio;
    }
    public String getId(){
       return this.id;
    }
    public String getInspectionPropotion(){
       return this.inspectionPropotion;
    }
    public String getLowRembursementRatio(){
       return this.lowRembursementRatio;
    }
    public String getMaxHospitalCost(){
       return this.maxHospitalCost;
    }
    public String getMaxLengthStay(){
       return this.maxLengthStay;
    }
    public String getNursing(){
       return this.nursing;
    }
    public String getProportionDrugs(){
       return this.proportionDrugs;
    }
    public String getRegion(){
       return this.region;
    }
    public String getSubjectDataList(){
       return this.subjectDataList;
    }
    public String getWholeDataList(){
       return this.wholeDataList;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $region = this.getRegion();
       int i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $year = this.getYear();
       i1 = result * 59;
       i = ($year == null)? 43: $year.hashCode();
       result = i1 + i;
       String $dataCategory = this.getDataCategory();
       i1 = result * 59;
       i = ($dataCategory == null)? 43: $dataCategory.hashCode();
       result = i1 + i;
       String $dataSummary = this.getDataSummary();
       i1 = result * 59;
       i = ($dataSummary == null)? 43: $dataSummary.hashCode();
       result = i1 + i;
       String categoryCost = this.getCategoryCost();
       i1 = result * 59;
       i = (categoryCost == null)? 43: categoryCost.hashCode();
       String categoryExpe = this.getCategoryExpense();
       i1 = (i1 + i) * 59;
       i = (categoryExpe == null)? 43: categoryExpe.hashCode();
       String departmentCo = this.getDepartmentCost();
       i1 = (i1 + i) * 59;
       i = (departmentCo == null)? 43: departmentCo.hashCode();
       String department = this.getDepartment();
       i1 = (i1 + i) * 59;
       i = (department == null)? 43: department.hashCode();
       String wholeDataLis = this.getWholeDataList();
       i1 = (i1 + i) * 59;
       i = (wholeDataLis == null)? 43: wholeDataLis.hashCode();
       String drugsDataLis = this.getDrugsDataList();
       i1 = (i1 + i) * 59;
       i = (drugsDataLis == null)? 43: drugsDataLis.hashCode();
       String consumablesD = this.getConsumablesDataList();
       i1 = (i1 + i) * 59;
       i = (consumablesD == null)? 43: consumablesD.hashCode();
       String subjectDataL = this.getSubjectDataList();
       i1 = (i1 + i) * 59;
       i = (subjectDataL == null)? 43: subjectDataL.hashCode();
       String daySum = this.getDaySum();
       i1 = (i1 + i) * 59;
       i = (daySum == null)? 43: daySum.hashCode();
       String maxLengthSta = this.getMaxLengthStay();
       i1 = (i1 + i) * 59;
       i = (maxLengthSta == null)? 43: maxLengthSta.hashCode();
       String maxHospitalC = this.getMaxHospitalCost();
       i1 = (i1 + i) * 59;
       i = (maxHospitalC == null)? 43: maxHospitalC.hashCode();
       String lowRembursem = this.getLowRembursementRatio();
       i1 = (i1 + i) * 59;
       i = (lowRembursem == null)? 43: lowRembursem.hashCode();
       String highRemburse = this.getHighRembursementRatio();
       i1 = (i1 + i) * 59;
       i = (highRemburse == null)? 43: highRemburse.hashCode();
       String proportionDr = this.getProportionDrugs();
       i1 = (i1 + i) * 59;
       i = (proportionDr == null)? 43: proportionDr.hashCode();
       String inspectionPr = this.getInspectionPropotion();
       i1 = (i1 + i) * 59;
       i = (inspectionPr == null)? 43: inspectionPr.hashCode();
       String nursing = this.getNursing();
       i1 = (i1 + i) * 59;
       i = (nursing == null)? 43: nursing.hashCode();
       return (i1 + i);
    }
    public void setCategoryCost(String categoryCost){
       this.categoryCost = categoryCost;
    }
    public void setCategoryExpense(String categoryExpense){
       this.categoryExpense = categoryExpense;
    }
    public void setConsumablesDataList(String consumablesDataList){
       this.consumablesDataList = consumablesDataList;
    }
    public void setDataCategory(String dataCategory){
       this.dataCategory = dataCategory;
    }
    public void setDataSummary(String dataSummary){
       this.dataSummary = dataSummary;
    }
    public void setDaySum(String daySum){
       this.daySum = daySum;
    }
    public void setDepartment(String department){
       this.department = department;
    }
    public void setDepartmentCost(String departmentCost){
       this.departmentCost = departmentCost;
    }
    public void setDrugsDataList(String drugsDataList){
       this.drugsDataList = drugsDataList;
    }
    public void setHighRembursementRatio(String highRembursementRatio){
       this.highRembursementRatio = highRembursementRatio;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setInspectionPropotion(String inspectionPropotion){
       this.inspectionPropotion = inspectionPropotion;
    }
    public void setLowRembursementRatio(String lowRembursementRatio){
       this.lowRembursementRatio = lowRembursementRatio;
    }
    public void setMaxHospitalCost(String maxHospitalCost){
       this.maxHospitalCost = maxHospitalCost;
    }
    public void setMaxLengthStay(String maxLengthStay){
       this.maxLengthStay = maxLengthStay;
    }
    public void setNursing(String nursing){
       this.nursing = nursing;
    }
    public void setProportionDrugs(String proportionDrugs){
       this.proportionDrugs = proportionDrugs;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setSubjectDataList(String subjectDataList){
       this.subjectDataList = subjectDataList;
    }
    public void setWholeDataList(String wholeDataList){
       this.wholeDataList = wholeDataList;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "HospitalDataPool\(id="+this.getId()+", region="+this.getRegion()+", year="+this.getYear()+", dataCategory="+this.getDataCategory()+", dataSummary="+this.getDataSummary()+", categoryCost="+this.getCategoryCost()+", categoryExpense="+this.getCategoryExpense()+", departmentCost="+this.getDepartmentCost()+", department="+this.getDepartment()+", wholeDataList="+this.getWholeDataList()+", drugsDataList="+this.getDrugsDataList()+", consumablesDataList="+this.getConsumablesDataList()+", subjectDataList="+this.getSubjectDataList()+", daySum="+this.getDaySum()+", maxLengthStay="+this.getMaxLengthStay()+", maxHospitalCost="+this.getMaxHospitalCost()+", lowRembursementRatio="+this.getLowRembursementRatio()+", highRembursementRatio="+this.getHighRembursementRatio()+", proportionDrugs="+this.getProportionDrugs()+", inspectionPropotion="+this.getInspectionPropotion()+", nursing="+this.getNursing()+"\)";
    }
}
