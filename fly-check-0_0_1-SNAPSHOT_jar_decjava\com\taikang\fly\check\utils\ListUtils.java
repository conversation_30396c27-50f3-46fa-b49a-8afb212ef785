package com.taikang.fly.check.utils.ListUtils;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import java.lang.Number;
import java.text.NumberFormat;
import java.lang.Math;
import java.util.Date;
import java.lang.Long;
import java.lang.reflect.Field;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import java.lang.CharSequence;
import java.lang.Integer;
import java.util.stream.Stream;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import com.taikang.fly.check.utils.ListUtils$1;
import java.util.Comparator;
import java.util.Collections;

public class ListUtils	// class@000341 from classes.dex
{
    private static final Logger log;

    static {
       ListUtils.log = LoggerFactory.getLogger(ListUtils.class);
    }
    public void ListUtils(){
       super();
    }
    static int access$000(String x0,boolean x1,Object x2,Object x3){
       return ListUtils.compareObject(x0, x1, x2, x3);
    }
    static Logger access$100(){
       return ListUtils.log;
    }
    public static String addZero2Str(Number numObj,int length){
       NumberFormat nf = NumberFormat.getInstance();
       nf.setGroupingUsed(false);
       nf.setMaximumIntegerDigits(length);
       nf.setMinimumIntegerDigits(length);
       return nf.format(numObj);
    }
    private static int compareObject(String sortname,boolean isAsc,Object a,Object b){
       int maxlen;
       Object value1 = ListUtils.forceGetFieldValue(a, sortname);
       Object value2 = ListUtils.forceGetFieldValue(b, sortname);
       String str1 = value1.toString();
       String str2 = value2.toString();
       if (value1 instanceof Number && value2 instanceof Number) {
          maxlen = Math.max(str1.length(), str2.length());
          str1 = ListUtils.addZero2Str(value1, maxlen);
          str2 = ListUtils.addZero2Str(value2, maxlen);
       }else if(value1 instanceof Date && value2 instanceof Date){
          Date time = value1.getTime();
          long time1 = value2.getTime();
          maxlen = Long.toString(Math.max(time, time1)).length();
          str1 = ListUtils.addZero2Str(Long.valueOf(time), maxlen);
          str2 = ListUtils.addZero2Str(Long.valueOf(time1), maxlen);
       }
       int ret = (isAsc)? str1.compareTo(str2): str2.compareTo(str1);
       return ret;
    }
    public static Object forceGetFieldValue(Object obj,String fieldName){
       boolean accessible;
       Field field = obj.getClass().getDeclaredField(fieldName);
       Object object = null;
       if (!(accessible = field.isAccessible())) {
          field.setAccessible(true);
          object = field.get(obj);
          field.setAccessible(accessible);
          object = object;
       }else {
          object = field.get(obj);
          object = object;
          object = object;
       }
       return object;
    }
    public static List listDistinct(List list){
       List strlist = new ArrayList();
       Iterator iterator = list.iterator();
       while (iterator.hasNext()) {
          String s = String.join(",", iterator.next());
          strlist.add(s);
       }
       ArrayList uArrayList = new ArrayList();
       if (list.size() >= 2) {
          int i = 0;
          while (i < strlist.size()) {
             String strx = strlist.get(i);
             int i1 = i + 1;
             while (i1 < strlist.size()) {
                if (strx.equals(strlist.get(i1))) {
                   uArrayList.add(Integer.valueOf(i1));
                }
                i1 = i1 + 1;
             }
             i = i + 1;
          }
          ArrayList uArrayList1 = new ArrayList();
          Iterator iterator1 = uArrayList.stream().distinct().collect(Collectors.toList()).iterator();
          while (iterator1.hasNext()) {
             uArrayList1.add(list.get(iterator1.next().intValue()));
          }
          iterator = uArrayList1.iterator();
          while (iterator.hasNext()) {
             list.remove(iterator.next());
          }
       }
       return list;
    }
    public static void sort(List list,boolean isAsc,String[] sortnameArr){
       Collections.sort(list, new ListUtils$1(sortnameArr, isAsc));
    }
}
