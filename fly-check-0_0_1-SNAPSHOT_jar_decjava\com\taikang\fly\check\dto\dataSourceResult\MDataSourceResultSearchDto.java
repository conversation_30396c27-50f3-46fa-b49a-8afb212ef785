package com.taikang.fly.check.dto.dataSourceResult.MDataSourceResultSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MDataSourceResultSearchDto implements Serializable	// class@0000e1 from classes.dex
{
    private String creator;
    private String hospName;
    private static final long serialVersionUID = 0x1;

    public void MDataSourceResultSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MDataSourceResultSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MDataSourceResultSearchDto) {
             b = false;
          }else {
             MDataSourceResultSearchDto mDataSourceR = o;
             if (!mDataSourceR.canEqual(this)) {
                b = false;
             }else {
                String hospName = this.getHospName();
                String hospName1 = mDataSourceR.getHospName();
                if (hospName == null) {
                   if (hospName1 != null) {
                      b = false;
                   }
                }else if(hospName.equals(hospName1)){
                }
                String creator = this.getCreator();
                String creator1 = mDataSourceR.getCreator();
                if (creator == null) {
                   if (creator1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!creator.equals(creator1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCreator(){
       return this.creator;
    }
    public String getHospName(){
       return this.hospName;
    }
    public int hashCode(){
       String $hospName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($hospName = this.getHospName()) == null)? i: $hospName.hashCode();
       result = i1 + 59;
       String $creator = this.getCreator();
       i1 = result * 59;
       if ($creator != null) {
          i = $creator.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public String toString(){
       return "MDataSourceResultSearchDto\(hospName="+this.getHospName()+", creator="+this.getCreator()+"\)";
    }
}
