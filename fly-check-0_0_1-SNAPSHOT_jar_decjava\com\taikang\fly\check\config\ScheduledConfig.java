package com.taikang.fly.check.config.ScheduledConfig;
import java.lang.Object;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

public class ScheduledConfig	// class@00008f from classes.dex
{

    public void ScheduledConfig(){
       super();
    }
    public TaskScheduler taskScheduler(){
       ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
       scheduler.setPoolSize(10);
       scheduler.initialize();
       return scheduler;
    }
    public ThreadPoolTaskScheduler threadPoolTaskScheduler(){
       ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
       scheduler.setPoolSize(3);
       scheduler.setRemoveOnCancelPolicy(true);
       return scheduler;
    }
}
