package com.taikang.fly.check.dto.businesscolconfig.ColConfigRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class ColConfigRespDto implements Serializable	// class@0000ba from classes.dex
{
    private String businessId;
    private String businessKey;
    private String businessName;
    private List colConfigSaveDtoList;
    private String columnComment;
    private String columnName;
    private String columnWidth;
    private String createdBy;
    private String createdTime;
    private String dictType;
    private String id;
    private String isValid;
    private String modifier;
    private String modifyTime;
    private String parentId;
    private String sortNum;
    private static final long serialVersionUID = 0x1;

    public void ColConfigRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ColConfigRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ColConfigRespDto){
          b = false;
       }else {
          ColConfigRespDto uColConfigRe = o;
          if (!uColConfigRe.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uColConfigRe.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String businessId = this.getBusinessId();
             String businessId1 = uColConfigRe.getBusinessId();
             if (businessId == null) {
                if (businessId1 != null) {
                   b = false;
                }
             }else if(businessId.equals(businessId1)){
             }
             String businessKey = this.getBusinessKey();
             String businessKey1 = uColConfigRe.getBusinessKey();
             if (businessKey == null) {
                if (businessKey1 != null) {
                   b = false;
                }
             }else if(businessKey.equals(businessKey1)){
             }
             String businessName = this.getBusinessName();
             String businessName1 = uColConfigRe.getBusinessName();
             if (businessName == null) {
                if (businessName1 != null) {
                   b = false;
                }
             }else if(businessName.equals(businessName1)){
             }
             String columnName = this.getColumnName();
             String columnName1 = uColConfigRe.getColumnName();
             if (columnName == null) {
                if (columnName1 != null) {
                   b = false;
                }
             }else if(columnName.equals(columnName1)){
             }
             String columnCommen = this.getColumnComment();
             String columnCommen1 = uColConfigRe.getColumnComment();
             if (columnCommen == null) {
                if (columnCommen1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(columnCommen.equals(columnCommen1)){
             }
             String columnWidth = this.getColumnWidth();
             String columnWidth1 = uColConfigRe.getColumnWidth();
             if (columnWidth == null) {
                if (columnWidth1 != null) {
                   b = false;
                }
             }else if(columnWidth.equals(columnWidth1)){
             }
             String sortNum = this.getSortNum();
             String sortNum1 = uColConfigRe.getSortNum();
             if (sortNum == null) {
                if (sortNum1 != null) {
                   b = false;
                }
             }else if(sortNum.equals(sortNum1)){
             }
             String createdTime = this.getCreatedTime();
             String createdTime1 = uColConfigRe.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                label_00ed :
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String createdBy = this.getCreatedBy();
             String createdBy1 = uColConfigRe.getCreatedBy();
             if (createdBy == null) {
                if (createdBy1 != null) {
                   b = false;
                }
             }else if(createdBy.equals(createdBy1)){
             }
             String modifier = this.getModifier();
             String modifier1 = uColConfigRe.getModifier();
             if (modifier == null) {
                if (modifier1 != null) {
                label_011d :
                   b = false;
                }
             }else if(modifier.equals(modifier1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = uColConfigRe.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String dictType = this.getDictType();
             String dictType1 = uColConfigRe.getDictType();
             if (dictType == null) {
                if (dictType1 != null) {
                label_014f :
                   b = false;
                }
             }else if(dictType.equals(dictType1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = uColConfigRe.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             String parentId = this.getParentId();
             String parentId1 = uColConfigRe.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                label_017f :
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             List colConfigSav = this.getColConfigSaveDtoList();
             List colConfigSav1 = uColConfigRe.getColConfigSaveDtoList();
             if (colConfigSav == null) {
                if (colConfigSav1 != null) {
                   b = false;
                }
             }else if(colConfigSav.equals(colConfigSav1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getBusinessId(){
       return this.businessId;
    }
    public String getBusinessKey(){
       return this.businessKey;
    }
    public String getBusinessName(){
       return this.businessName;
    }
    public List getColConfigSaveDtoList(){
       return this.colConfigSaveDtoList;
    }
    public String getColumnComment(){
       return this.columnComment;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnWidth(){
       return this.columnWidth;
    }
    public String getCreatedBy(){
       return this.createdBy;
    }
    public String getCreatedTime(){
       return this.createdTime;
    }
    public String getDictType(){
       return this.dictType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModifier(){
       return this.modifier;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getSortNum(){
       return this.sortNum;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $businessId = this.getBusinessId();
       int i1 = result * 59;
       i = ($businessId == null)? 43: $businessId.hashCode();
       result = i1 + i;
       String $businessKey = this.getBusinessKey();
       i1 = result * 59;
       i = ($businessKey == null)? 43: $businessKey.hashCode();
       result = i1 + i;
       String $businessName = this.getBusinessName();
       i1 = result * 59;
       i = ($businessName == null)? 43: $businessName.hashCode();
       result = i1 + i;
       String $columnName = this.getColumnName();
       i1 = result * 59;
       i = ($columnName == null)? 43: $columnName.hashCode();
       result = i1 + i;
       String columnCommen = this.getColumnComment();
       i1 = result * 59;
       i = (columnCommen == null)? 43: columnCommen.hashCode();
       String columnWidth = this.getColumnWidth();
       i1 = (i1 + i) * 59;
       i = (columnWidth == null)? 43: columnWidth.hashCode();
       String sortNum = this.getSortNum();
       i1 = (i1 + i) * 59;
       i = (sortNum == null)? 43: sortNum.hashCode();
       String createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String createdBy = this.getCreatedBy();
       i1 = (i1 + i) * 59;
       i = (createdBy == null)? 43: createdBy.hashCode();
       String modifier = this.getModifier();
       i1 = (i1 + i) * 59;
       i = (modifier == null)? 43: modifier.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String dictType = this.getDictType();
       i1 = (i1 + i) * 59;
       i = (dictType == null)? 43: dictType.hashCode();
       String isValid = this.getIsValid();
       i1 = (i1 + i) * 59;
       i = (isValid == null)? 43: isValid.hashCode();
       String parentId = this.getParentId();
       i1 = (i1 + i) * 59;
       i = (parentId == null)? 43: parentId.hashCode();
       List colConfigSav = this.getColConfigSaveDtoList();
       i1 = (i1 + i) * 59;
       i = (colConfigSav == null)? 43: colConfigSav.hashCode();
       return (i1 + i);
    }
    public void setBusinessId(String businessId){
       this.businessId = businessId;
    }
    public void setBusinessKey(String businessKey){
       this.businessKey = businessKey;
    }
    public void setBusinessName(String businessName){
       this.businessName = businessName;
    }
    public void setColConfigSaveDtoList(List colConfigSaveDtoList){
       this.colConfigSaveDtoList = colConfigSaveDtoList;
    }
    public void setColumnComment(String columnComment){
       this.columnComment = columnComment;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnWidth(String columnWidth){
       this.columnWidth = columnWidth;
    }
    public void setCreatedBy(String createdBy){
       this.createdBy = createdBy;
    }
    public void setCreatedTime(String createdTime){
       this.createdTime = createdTime;
    }
    public void setDictType(String dictType){
       this.dictType = dictType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setModifier(String modifier){
       this.modifier = modifier;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setSortNum(String sortNum){
       this.sortNum = sortNum;
    }
    public String toString(){
       return "ColConfigRespDto\(id="+this.getId()+", businessId="+this.getBusinessId()+", businessKey="+this.getBusinessKey()+", businessName="+this.getBusinessName()+", columnName="+this.getColumnName()+", columnComment="+this.getColumnComment()+", columnWidth="+this.getColumnWidth()+", sortNum="+this.getSortNum()+", createdTime="+this.getCreatedTime()+", createdBy="+this.getCreatedBy()+", modifier="+this.getModifier()+", modifyTime="+this.getModifyTime()+", dictType="+this.getDictType()+", isValid="+this.getIsValid()+", parentId="+this.getParentId()+", colConfigSaveDtoList="+this.getColConfigSaveDtoList()+"\)";
    }
}
