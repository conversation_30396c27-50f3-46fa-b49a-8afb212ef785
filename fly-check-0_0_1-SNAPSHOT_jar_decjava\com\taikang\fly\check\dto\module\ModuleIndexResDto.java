package com.taikang.fly.check.dto.module.ModuleIndexResDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModuleIndexResDto implements Serializable	// class@000196 from classes.dex
{
    private boolean check;
    private String createdTime;
    private String creator;
    private String description;
    private String icon;
    private String modifyTime;
    private String moduleCode;
    private String moduleName;
    private String moduleOrder;
    private String url;
    private static final long serialVersionUID = 0x1;

    public void ModuleIndexResDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModuleIndexResDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModuleIndexResDto){
          b = false;
       }else {
          ModuleIndexResDto moduleIndexR = o;
          if (!moduleIndexR.canEqual(this)) {
             b = false;
          }else {
             String moduleCode = this.getModuleCode();
             String moduleCode1 = moduleIndexR.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String moduleName = this.getModuleName();
             String moduleName1 = moduleIndexR.getModuleName();
             if (moduleName == null) {
                if (moduleName1 != null) {
                   b = false;
                }
             }else if(moduleName.equals(moduleName1)){
             }
             String moduleOrder = this.getModuleOrder();
             String moduleOrder1 = moduleIndexR.getModuleOrder();
             if (moduleOrder == null) {
                if (moduleOrder1 != null) {
                   b = false;
                }
             }else if(moduleOrder.equals(moduleOrder1)){
             }
             String description = this.getDescription();
             String description1 = moduleIndexR.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String icon = this.getIcon();
             String icon1 = moduleIndexR.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String url = this.getUrl();
             String url1 = moduleIndexR.getUrl();
             if (url == null) {
                if (url1 != null) {
                label_009f :
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String creator = this.getCreator();
             String creator1 = moduleIndexR.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createdTime = this.getCreatedTime();
             String createdTime1 = moduleIndexR.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = moduleIndexR.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00e3 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             if (this.isCheck() != moduleIndexR.isCheck()) {
                b = false;
             }else {
                b = true;
             }
          }
       }
       return b;
    }
    public String getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDescription(){
       return this.description;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getModuleName(){
       return this.moduleName;
    }
    public String getModuleOrder(){
       return this.moduleOrder;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $moduleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($moduleCode = this.getModuleCode()) == null)? i: $moduleCode.hashCode();
       result = i1 + 59;
       String $moduleName = this.getModuleName();
       int i2 = result * 59;
       i1 = ($moduleName == null)? i: $moduleName.hashCode();
       result = i2 + i1;
       String $moduleOrder = this.getModuleOrder();
       i2 = result * 59;
       i1 = ($moduleOrder == null)? i: $moduleOrder.hashCode();
       result = i2 + i1;
       String $description = this.getDescription();
       i2 = result * 59;
       i1 = ($description == null)? i: $description.hashCode();
       result = i2 + i1;
       String $icon = this.getIcon();
       i2 = result * 59;
       i1 = ($icon == null)? i: $icon.hashCode();
       result = i2 + i1;
       String url = this.getUrl();
       i2 = result * 59;
       i1 = (url == null)? i: url.hashCode();
       String creator = this.getCreator();
       i2 = (i2 + i1) * 59;
       i1 = (creator == null)? i: creator.hashCode();
       String createdTime = this.getCreatedTime();
       i2 = (i2 + i1) * 59;
       i1 = (createdTime == null)? i: createdTime.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i2 + i1) * 59;
       if (modifyTime != null) {
          i = modifyTime.hashCode();
       }
       i = (i1 + i) * 59;
       i1 = (this.isCheck())? 79: 97;
       return (i + i1);
    }
    public boolean isCheck(){
       return this.check;
    }
    public void setCheck(boolean check){
       this.check = check;
    }
    public void setCreatedTime(String createdTime){
       this.createdTime = createdTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setModuleName(String moduleName){
       this.moduleName = moduleName;
    }
    public void setModuleOrder(String moduleOrder){
       this.moduleOrder = moduleOrder;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ModuleIndexResDto\(moduleCode="+this.getModuleCode()+", moduleName="+this.getModuleName()+", moduleOrder="+this.getModuleOrder()+", description="+this.getDescription()+", icon="+this.getIcon()+", url="+this.getUrl()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", modifyTime="+this.getModifyTime()+", check="+this.isCheck()+"\)";
    }
}
