package com.taikang.fly.check.dto.businesscolconfig.ColConfigSaveDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class ColConfigSaveDto implements Serializable	// class@0000bb from classes.dex
{
    private String businessId;
    private List colConfigSaveDtoList;
    private String columnComment;
    private String columnName;
    private String columnWidth;
    private String dictType;
    private String id;
    private String isValid;
    private String sortNum;
    private static final long serialVersionUID = 0x1;

    public void ColConfigSaveDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ColConfigSaveDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ColConfigSaveDto){
          b = false;
       }else {
          ColConfigSaveDto uColConfigSa = o;
          if (!uColConfigSa.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uColConfigSa.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String businessId = this.getBusinessId();
             String businessId1 = uColConfigSa.getBusinessId();
             if (businessId == null) {
                if (businessId1 != null) {
                   b = false;
                }
             }else if(businessId.equals(businessId1)){
             }
             String columnName = this.getColumnName();
             String columnName1 = uColConfigSa.getColumnName();
             if (columnName == null) {
                if (columnName1 != null) {
                   b = false;
                }
             }else if(columnName.equals(columnName1)){
             }
             String columnCommen = this.getColumnComment();
             String columnCommen1 = uColConfigSa.getColumnComment();
             if (columnCommen == null) {
                if (columnCommen1 != null) {
                   b = false;
                }
             }else if(columnCommen.equals(columnCommen1)){
             }
             String columnWidth = this.getColumnWidth();
             String columnWidth1 = uColConfigSa.getColumnWidth();
             if (columnWidth == null) {
                if (columnWidth1 != null) {
                   b = false;
                }
             }else if(columnWidth.equals(columnWidth1)){
             }
             String sortNum = this.getSortNum();
             String sortNum1 = uColConfigSa.getSortNum();
             if (sortNum == null) {
                if (sortNum1 != null) {
                label_009c :
                   b = false;
                }
             }else if(sortNum.equals(sortNum1)){
             }
             String dictType = this.getDictType();
             String dictType1 = uColConfigSa.getDictType();
             if (dictType == null) {
                if (dictType1 != null) {
                   b = false;
                }
             }else if(dictType.equals(dictType1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = uColConfigSa.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             List colConfigSav = this.getColConfigSaveDtoList();
             List colConfigSav1 = uColConfigSa.getColConfigSaveDtoList();
             if (colConfigSav == null) {
                if (colConfigSav1 != null) {
                label_00e4 :
                   b = false;
                }
             }else if(colConfigSav.equals(colConfigSav1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getBusinessId(){
       return this.businessId;
    }
    public List getColConfigSaveDtoList(){
       return this.colConfigSaveDtoList;
    }
    public String getColumnComment(){
       return this.columnComment;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnWidth(){
       return this.columnWidth;
    }
    public String getDictType(){
       return this.dictType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getSortNum(){
       return this.sortNum;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $businessId = this.getBusinessId();
       int i2 = result * 59;
       i1 = ($businessId == null)? i: $businessId.hashCode();
       result = i2 + i1;
       String $columnName = this.getColumnName();
       i2 = result * 59;
       i1 = ($columnName == null)? i: $columnName.hashCode();
       result = i2 + i1;
       String $columnComment = this.getColumnComment();
       i2 = result * 59;
       i1 = ($columnComment == null)? i: $columnComment.hashCode();
       result = i2 + i1;
       String $columnWidth = this.getColumnWidth();
       i2 = result * 59;
       i1 = ($columnWidth == null)? i: $columnWidth.hashCode();
       result = i2 + i1;
       String sortNum = this.getSortNum();
       i2 = result * 59;
       i1 = (sortNum == null)? i: sortNum.hashCode();
       String dictType = this.getDictType();
       i2 = (i2 + i1) * 59;
       i1 = (dictType == null)? i: dictType.hashCode();
       String isValid = this.getIsValid();
       i2 = (i2 + i1) * 59;
       i1 = (isValid == null)? i: isValid.hashCode();
       List colConfigSav = this.getColConfigSaveDtoList();
       i1 = (i2 + i1) * 59;
       if (colConfigSav != null) {
          i = colConfigSav.hashCode();
       }
       return (i1 + i);
    }
    public void setBusinessId(String businessId){
       this.businessId = businessId;
    }
    public void setColConfigSaveDtoList(List colConfigSaveDtoList){
       this.colConfigSaveDtoList = colConfigSaveDtoList;
    }
    public void setColumnComment(String columnComment){
       this.columnComment = columnComment;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnWidth(String columnWidth){
       this.columnWidth = columnWidth;
    }
    public void setDictType(String dictType){
       this.dictType = dictType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setSortNum(String sortNum){
       this.sortNum = sortNum;
    }
    public String toString(){
       return "ColConfigSaveDto\(id="+this.getId()+", businessId="+this.getBusinessId()+", columnName="+this.getColumnName()+", columnComment="+this.getColumnComment()+", columnWidth="+this.getColumnWidth()+", sortNum="+this.getSortNum()+", dictType="+this.getDictType()+", isValid="+this.getIsValid()+", colConfigSaveDtoList="+this.getColConfigSaveDtoList()+"\)";
    }
}
