package com.taikang.fly.check.dto.system.user.UserRoleResDto;
import java.io.Serializable;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.lang.StringBuilder;

public class UserRoleResDto implements Serializable	// class@0001ba from classes.dex
{
    private List roleList;
    private static final long serialVersionUID = 0x1;

    public void UserRoleResDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserRoleResDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserRoleResDto) {
             b = false;
          }else {
             UserRoleResDto userRoleResD = o;
             if (!userRoleResD.canEqual(this)) {
                b = false;
             }else {
                List roleList = this.getRoleList();
                List roleList1 = userRoleResD.getRoleList();
                if (roleList == null) {
                   if (roleList1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!roleList.equals(roleList1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getRoleList(){
       return this.roleList;
    }
    public int hashCode(){
       List $roleList;
       int PRIME = 59;
       int result = 1;
       int i = (($roleList = this.getRoleList()) == null)? 43: $roleList.hashCode();
       result = i + 59;
       return result;
    }
    public void setRoleList(List roleList){
       this.roleList = roleList;
    }
    public String toString(){
       return "UserRoleResDto\(roleList="+this.getRoleList()+"\)";
    }
}
