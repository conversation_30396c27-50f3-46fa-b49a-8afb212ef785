package com.taikang.fly.check.comm.enums.QueryTypeEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Class;
import java.lang.Object;

public final class QueryTypeEnum extends Enum	// class@000086 from classes.dex
{
    private static final QueryTypeEnum[] $VALUES;
    public static final QueryTypeEnum CLICKHOUSE_JDBC_TEMPLATE;
    public static final QueryTypeEnum DB_HELPER;
    public static final QueryTypeEnum DB_HELPER_CLICKHOUSE;
    public static final QueryTypeEnum DB_HELPER_SYSTEM;
    public static final QueryTypeEnum JDBC_TEMPLATE;
    public static final QueryTypeEnum JDBC_TEMPLATE_SYSTEM;

    static {
       QueryTypeEnum.JDBC_TEMPLATE = new QueryTypeEnum("JDBC_TEMPLATE", 0);
       QueryTypeEnum.DB_HELPER = new QueryTypeEnum("DB_HELPER", 1);
       QueryTypeEnum.JDBC_TEMPLATE_SYSTEM = new QueryTypeEnum("JDBC_TEMPLATE_SYSTEM", 2);
       QueryTypeEnum.DB_HELPER_SYSTEM = new QueryTypeEnum("DB_HELPER_SYSTEM", 3);
       QueryTypeEnum.CLICKHOUSE_JDBC_TEMPLATE = new QueryTypeEnum("CLICKHOUSE_JDBC_TEMPLATE", 4);
       QueryTypeEnum.DB_HELPER_CLICKHOUSE = new QueryTypeEnum("DB_HELPER_CLICKHOUSE", 5);
       QueryTypeEnum[] queryTypeEnu = new QueryTypeEnum[]{QueryTypeEnum.JDBC_TEMPLATE,QueryTypeEnum.DB_HELPER,QueryTypeEnum.JDBC_TEMPLATE_SYSTEM,QueryTypeEnum.DB_HELPER_SYSTEM,QueryTypeEnum.CLICKHOUSE_JDBC_TEMPLATE,QueryTypeEnum.DB_HELPER_CLICKHOUSE};
       QueryTypeEnum.$VALUES = queryTypeEnu;
    }
    private void QueryTypeEnum(String p0,int p1){
       super(p0, p1);
    }
    public static QueryTypeEnum valueOf(String name){
       return Enum.valueOf(QueryTypeEnum.class, name);
    }
    public static QueryTypeEnum[] values(){
       return QueryTypeEnum.$VALUES.clone();
    }
}
