package com.taikang.fly.check.dto.matchFiled.MatchFiled;
import java.io.Serializable;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import java.lang.StringBuilder;

public class MatchFiled implements Serializable	// class@00018c from classes.dex
{
    private List list;
    private List listMap;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MatchFiled(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MatchFiled;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MatchFiled) {
             b = false;
          }else {
             MatchFiled matchFiled = o;
             if (!matchFiled.canEqual(this)) {
                b = false;
             }else {
                List list = this.getList();
                List list1 = matchFiled.getList();
                if (list == null) {
                   if (list1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!list.equals(list1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getList(){
       return this.list;
    }
    public List getListMap(){
       return this.listMap;
    }
    public int hashCode(){
       List $list;
       int PRIME = 59;
       int result = 1;
       int i = (($list = this.getList()) == null)? 43: $list.hashCode();
       result = i + 59;
       return result;
    }
    public void setList(List list){
       this.list = list;
    }
    public void setListMap(List listMap){
       this.listMap = listMap;
    }
    public String toString(){
       return "MatchFiled\(listMap="+this.getListMap()+", list="+this.getList()+"\)";
    }
}
