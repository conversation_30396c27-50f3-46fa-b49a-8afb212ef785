package com.taikang.fly.check.dto.templateInfo.TemplateInfoEditDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TemplateInfoEditDto	// class@0001bc from classes.dex
{
    private String explain;
    private String fieldItems;
    private String id;
    private String sourceTableName;
    private String tableName;
    private String whereCondition;

    public void TemplateInfoEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TemplateInfoEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof TemplateInfoEditDto){
          b = false;
       }else {
          TemplateInfoEditDto templateInfo = o;
          if (!templateInfo.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = templateInfo.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String tableName = this.getTableName();
             String tableName1 = templateInfo.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String fieldItems = this.getFieldItems();
             String fieldItems1 = templateInfo.getFieldItems();
             if (fieldItems == null) {
                if (fieldItems1 != null) {
                   b = false;
                }
             }else if(fieldItems.equals(fieldItems1)){
             }
             String sourceTableN = this.getSourceTableName();
             String sourceTableN1 = templateInfo.getSourceTableName();
             if (sourceTableN == null) {
                if (sourceTableN1 != null) {
                   b = false;
                }
             }else if(sourceTableN.equals(sourceTableN1)){
             }
             String whereConditi = this.getWhereCondition();
             String whereConditi1 = templateInfo.getWhereCondition();
             if (whereConditi == null) {
                if (whereConditi1 != null) {
                   b = false;
                }
             }else if(whereConditi.equals(whereConditi1)){
             }
             String explain = this.getExplain();
             String explain1 = templateInfo.getExplain();
             if (explain == null) {
                if (explain1 != null) {
                   b = false;
                }
             }else if(explain.equals(explain1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getExplain(){
       return this.explain;
    }
    public String getFieldItems(){
       return this.fieldItems;
    }
    public String getId(){
       return this.id;
    }
    public String getSourceTableName(){
       return this.sourceTableName;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getWhereCondition(){
       return this.whereCondition;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $fieldItems = this.getFieldItems();
       i2 = result * 59;
       i1 = ($fieldItems == null)? i: $fieldItems.hashCode();
       result = i2 + i1;
       String $sourceTableName = this.getSourceTableName();
       i2 = result * 59;
       i1 = ($sourceTableName == null)? i: $sourceTableName.hashCode();
       result = i2 + i1;
       String $whereCondition = this.getWhereCondition();
       i2 = result * 59;
       i1 = ($whereCondition == null)? i: $whereCondition.hashCode();
       result = i2 + i1;
       String explain = this.getExplain();
       i1 = result * 59;
       if (explain != null) {
          i = explain.hashCode();
       }
       return (i1 + i);
    }
    public void setExplain(String explain){
       this.explain = explain;
    }
    public void setFieldItems(String fieldItems){
       this.fieldItems = fieldItems;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setSourceTableName(String sourceTableName){
       this.sourceTableName = sourceTableName;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setWhereCondition(String whereCondition){
       this.whereCondition = whereCondition;
    }
    public String toString(){
       return "TemplateInfoEditDto\(id="+this.getId()+", tableName="+this.getTableName()+", fieldItems="+this.getFieldItems()+", sourceTableName="+this.getSourceTableName()+", whereCondition="+this.getWhereCondition()+", explain="+this.getExplain()+"\)";
    }
}
