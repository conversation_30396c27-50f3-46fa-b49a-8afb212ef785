package com.taikang.fly.check.dto.TransmissionServerDataDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TransmissionServerDataDto	// class@0000b6 from classes.dex
{
    private String receiceIp;
    private String receicePassword;
    private String transmissionIp;

    public void TransmissionServerDataDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TransmissionServerDataDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof TransmissionServerDataDto) {
             b = false;
          }else {
             TransmissionServerDataDto transmission = o;
             if (!transmission.canEqual(this)) {
                b = false;
             }else {
                String transmission1 = this.getTransmissionIp();
                String transmission2 = transmission.getTransmissionIp();
                if (transmission1 == null) {
                   if (transmission2 != null) {
                      b = false;
                   }
                }else if(transmission1.equals(transmission2)){
                }
                String receiceIp = this.getReceiceIp();
                String receiceIp1 = transmission.getReceiceIp();
                if (receiceIp == null) {
                   if (receiceIp1 != null) {
                      b = false;
                   }
                }else if(receiceIp.equals(receiceIp1)){
                }
                String receicePassw = this.getReceicePassword();
                String receicePassw1 = transmission.getReceicePassword();
                if (receicePassw == null) {
                   if (receicePassw1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!receicePassw.equals(receicePassw1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getReceiceIp(){
       return this.receiceIp;
    }
    public String getReceicePassword(){
       return this.receicePassword;
    }
    public String getTransmissionIp(){
       return this.transmissionIp;
    }
    public int hashCode(){
       String $transmissionIp;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($transmissionIp = this.getTransmissionIp()) == null)? i: $transmissionIp.hashCode();
       result = i1 + 59;
       String $receiceIp = this.getReceiceIp();
       int i2 = result * 59;
       i1 = ($receiceIp == null)? i: $receiceIp.hashCode();
       result = i2 + i1;
       String $receicePassword = this.getReceicePassword();
       i1 = result * 59;
       if ($receicePassword != null) {
          i = $receicePassword.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setReceiceIp(String receiceIp){
       this.receiceIp = receiceIp;
    }
    public void setReceicePassword(String receicePassword){
       this.receicePassword = receicePassword;
    }
    public void setTransmissionIp(String transmissionIp){
       this.transmissionIp = transmissionIp;
    }
    public String toString(){
       return "TransmissionServerDataDto\(transmissionIp="+this.getTransmissionIp()+", receiceIp="+this.getReceiceIp()+", receicePassword="+this.getReceicePassword()+"\)";
    }
}
