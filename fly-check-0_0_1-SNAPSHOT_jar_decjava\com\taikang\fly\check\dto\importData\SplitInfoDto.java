package com.taikang.fly.check.dto.importData.SplitInfoDto;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class SplitInfoDto	// class@000135 from classes.dex
{
    public List splitDataDtoList;
    public String tag;

    public void SplitInfoDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof SplitInfoDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SplitInfoDto) {
             b = false;
          }else {
             SplitInfoDto splitInfoDto = o;
             if (!splitInfoDto.canEqual(this)) {
                b = false;
             }else {
                String tag = this.getTag();
                String tag1 = splitInfoDto.getTag();
                if (tag == null) {
                   if (tag1 != null) {
                      b = false;
                   }
                }else if(tag.equals(tag1)){
                }
                List splitDataDto = this.getSplitDataDtoList();
                List splitDataDto1 = splitInfoDto.getSplitDataDtoList();
                if (splitDataDto == null) {
                   if (splitDataDto1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!splitDataDto.equals(splitDataDto1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getSplitDataDtoList(){
       return this.splitDataDtoList;
    }
    public String getTag(){
       return this.tag;
    }
    public int hashCode(){
       String $tag;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tag = this.getTag()) == null)? i: $tag.hashCode();
       result = i1 + 59;
       List $splitDataDtoList = this.getSplitDataDtoList();
       i1 = result * 59;
       if ($splitDataDtoList != null) {
          i = $splitDataDtoList.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setSplitDataDtoList(List splitDataDtoList){
       this.splitDataDtoList = splitDataDtoList;
    }
    public void setTag(String tag){
       this.tag = tag;
    }
    public String toString(){
       return "SplitInfoDto\(tag="+this.getTag()+", splitDataDtoList="+this.getSplitDataDtoList()+"\)";
    }
}
