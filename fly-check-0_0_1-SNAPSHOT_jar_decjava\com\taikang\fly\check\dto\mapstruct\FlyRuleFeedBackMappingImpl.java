package com.taikang.fly.check.dto.mapstruct.FlyRuleFeedBackMappingImpl;
import com.taikang.fly.check.dto.mapstruct.FlyRuleFeedBackMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRule.FlyRuleFeedBackRespDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleExchangerDto;
import java.lang.String;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class FlyRuleFeedBackMappingImpl implements FlyRuleFeedBackMapping	// class@000153 from classes.dex
{

    public void FlyRuleFeedBackMappingImpl(){
       super();
    }
    public FlyRuleExchangerDto flyRuleFeedBackResToExchanger(FlyRuleFeedBackRespDto flyRuleFeedBackRespDto){
       FlyRuleExchangerDto uFlyRuleExch;
       if (flyRuleFeedBackRespDto == null) {
          uFlyRuleExch = null;
       }else {
          uFlyRuleExch = new FlyRuleExchangerDto();
          uFlyRuleExch.setId(flyRuleFeedBackRespDto.getId());
          uFlyRuleExch.setRuleName(flyRuleFeedBackRespDto.getRuleName());
          uFlyRuleExch.setOperator(flyRuleFeedBackRespDto.getOperator());
          uFlyRuleExch.setRegion(flyRuleFeedBackRespDto.getRegion());
          uFlyRuleExch.setRuleType(flyRuleFeedBackRespDto.getRuleType());
          uFlyRuleExch.setResultFlag(flyRuleFeedBackRespDto.getResultFlag());
          uFlyRuleExch.setRemoved(flyRuleFeedBackRespDto.getRemoved());
          if (flyRuleFeedBackRespDto.getOperateTime() != null) {
             uFlyRuleExch.setOperateTime(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRuleFeedBackRespDto.getOperateTime()));
          }
          if (flyRuleFeedBackRespDto.getCreatedTime() != null) {
             uFlyRuleExch.setCreatedTime(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRuleFeedBackRespDto.getCreatedTime()));
          }
          uFlyRuleExch.setPs(flyRuleFeedBackRespDto.getPs());
          uFlyRuleExch.setState(flyRuleFeedBackRespDto.getState());
          uFlyRuleExch.setCreater(flyRuleFeedBackRespDto.getCreater());
          uFlyRuleExch.setResultsEnforcement(flyRuleFeedBackRespDto.getResultsEnforcement());
          if (flyRuleFeedBackRespDto.getExecutionDate() != null) {
             uFlyRuleExch.setExecutionDate(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRuleFeedBackRespDto.getExecutionDate()));
          }
          uFlyRuleExch.setDataSources(flyRuleFeedBackRespDto.getDataSources());
          uFlyRuleExch.setSubmitState(flyRuleFeedBackRespDto.getSubmitState());
          uFlyRuleExch.setRuleLevel(flyRuleFeedBackRespDto.getRuleLevel());
          uFlyRuleExch.setRuleCategory1(flyRuleFeedBackRespDto.getRuleCategory1());
          uFlyRuleExch.setRuleCategory2(flyRuleFeedBackRespDto.getRuleCategory2());
          uFlyRuleExch.setDiagnosisType(flyRuleFeedBackRespDto.getDiagnosisType());
          uFlyRuleExch.setRuleDescribe(flyRuleFeedBackRespDto.getRuleDescribe());
          uFlyRuleExch.setSourceOfRule(flyRuleFeedBackRespDto.getSourceOfRule());
          uFlyRuleExch.setRuleLogic(flyRuleFeedBackRespDto.getRuleLogic());
          uFlyRuleExch.setRuleParameter(flyRuleFeedBackRespDto.getRuleParameter());
          uFlyRuleExch.setRedField3(flyRuleFeedBackRespDto.getRedField3());
          uFlyRuleExch.setSqlName(flyRuleFeedBackRespDto.getSqlName());
          uFlyRuleExch.setNewSqlName(flyRuleFeedBackRespDto.getNewSqlName());
          uFlyRuleExch.setRuleClassify(flyRuleFeedBackRespDto.getRuleClassify());
          uFlyRuleExch.setPolicyBasis(flyRuleFeedBackRespDto.getPolicyBasis());
          uFlyRuleExch.setIsSpecial(flyRuleFeedBackRespDto.getIsSpecial());
          if (flyRuleFeedBackRespDto.getRuleSuitTimeStart() != null) {
             uFlyRuleExch.setRuleSuitTimeStart(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRuleFeedBackRespDto.getRuleSuitTimeStart()));
          }
          if (flyRuleFeedBackRespDto.getRuleSuitTimeEnd() != null) {
             uFlyRuleExch.setRuleSuitTimeEnd(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRuleFeedBackRespDto.getRuleSuitTimeEnd()));
          }
          uFlyRuleExch.setRuleDimension(flyRuleFeedBackRespDto.getRuleDimension());
          uFlyRuleExch.setRuleScopeApply(flyRuleFeedBackRespDto.getRuleScopeApply());
          uFlyRuleExch.setFeedbackStatus(flyRuleFeedBackRespDto.getFeedbackStatus());
          uFlyRuleExch.setResultIsClear(flyRuleFeedBackRespDto.getResultIsClear());
          uFlyRuleExch.setFalsePositiveRate(flyRuleFeedBackRespDto.getFalsePositiveRate());
          uFlyRuleExch.setProblemDescription(flyRuleFeedBackRespDto.getProblemDescription());
       }
       return uFlyRuleExch;
    }
    public List flyRuleResListToexchangerList(List flyRuleFeedBackRespDtos){
       List list;
       if (flyRuleFeedBackRespDtos == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleFeedBackRespDtos.size());
          Iterator iterator = flyRuleFeedBackRespDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.flyRuleFeedBackResToExchanger(iterator.next()));
          }
       }
       return list;
    }
}
