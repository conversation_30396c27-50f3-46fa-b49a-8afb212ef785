package com.taikang.fly.check.vo.drg.DrgLowSetDetailVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgLowSetDetailVo	// class@00036f from classes.dex
{
    private String drgCode;
    private String year;

    public void DrgLowSetDetailVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgLowSetDetailVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgLowSetDetailVo) {
             b = false;
          }else {
             DrgLowSetDetailVo uDrgLowSetDe = o;
             if (!uDrgLowSetDe.canEqual(this)) {
                b = false;
             }else {
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgLowSetDe.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String year = this.getYear();
                String year1 = uDrgLowSetDe.getYear();
                if (year == null) {
                   if (year1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!year.equals(year1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $drgCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgCode = this.getDrgCode()) == null)? i: $drgCode.hashCode();
       result = i1 + 59;
       String $year = this.getYear();
       i1 = result * 59;
       if ($year != null) {
          i = $year.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DrgLowSetDetailVo\(drgCode="+this.getDrgCode()+", year="+this.getYear()+"\)";
    }
}
