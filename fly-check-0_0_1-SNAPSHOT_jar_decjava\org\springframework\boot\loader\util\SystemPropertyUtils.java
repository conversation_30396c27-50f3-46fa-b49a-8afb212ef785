package org.springframework.boot.loader.util.SystemPropertyUtils;
import java.lang.String;
import java.lang.Object;
import java.lang.CharSequence;
import java.lang.System;
import java.util.Locale;
import java.lang.StringBuilder;
import java.io.PrintStream;
import java.util.Properties;
import java.util.Set;
import java.lang.IllegalArgumentException;
import java.util.HashSet;

public abstract class SystemPropertyUtils	// class@00055f from classes.dex
{
    public static final String PLACEHOLDER_PREFIX = "${";
    public static final String PLACEHOLDER_SUFFIX = "}";
    private static final String SIMPLE_PREFIX;
    public static final String VALUE_SEPARATOR;

    static {
       SystemPropertyUtils.SIMPLE_PREFIX = "${".substring(1);
    }
    public void SystemPropertyUtils(){
       super();
    }
    private static int findPlaceholderEndIndex(CharSequence buf,int startIndex){
       int index = startIndex + "${".length();
       int withinNestedPlaceholder = 0;
       while (true) {
          if (index < buf.length()) {
             if (SystemPropertyUtils.substringMatch(buf, index, "}")) {
                if (withinNestedPlaceholder > 0) {
                   withinNestedPlaceholder--;
                   index = index + "}".length();
                }else {
                   break ;
                }
             }else if(SystemPropertyUtils.substringMatch(buf, index, SystemPropertyUtils.SIMPLE_PREFIX)){
                withinNestedPlaceholder++;
                index = index + SystemPropertyUtils.SIMPLE_PREFIX.length();
             }else {
                index++;
             }
          }else {
             index = -1;
             break ;
          }
       }
       return index;
    }
    public static String getProperty(String key){
       return SystemPropertyUtils.getProperty(key, null, "");
    }
    public static String getProperty(String key,String defaultValue){
       return SystemPropertyUtils.getProperty(key, defaultValue, "");
    }
    public static String getProperty(String key,String defaultValue,String text){
       String propVal;
       try{
          if ((propVal = System.getProperty(key)) == null) {
             propVal = System.getenv(key);
          }
          if (propVal == null) {
             propVal = System.getenv(key.replace('.', '_'));
          }
          if (propVal == null) {
             propVal = System.getenv(key.toUpperCase(Locale.ENGLISH).replace('.', '_'));
          }
          if (propVal != null) {
          label_002e :
             return propVal;
          }
       }catch(java.lang.Throwable e0){
          System.err.println("Could not resolve key \'"+key+"\' in \'"+text+"\' as system property or in environment: "+e0);
       }
       propVal = defaultValue;
       goto label_002e ;
    }
    private static String parseStringValue(Properties properties,String value,String current,Set visitedPlaceholders){
       int endIndex;
       String placeholder;
       String originalPlaceholder;
       String str;
       int i2;
       int i3;
       int i = -1;
       StringBuilder buf = current;
       int startIndex = current.indexOf("${");
       while (true) {
          if (startIndex == i) {
             return buf;
          }
          if ((endIndex = SystemPropertyUtils.findPlaceholderEndIndex(buf, startIndex)) != i) {
             int i1 = "${".length() + startIndex;
             placeholder = buf.substring(i1, endIndex);
             originalPlaceholder = placeholder;
             if (!visitedPlaceholders.add(originalPlaceholder)) {
                break ;
             }else {
                placeholder = SystemPropertyUtils.parseStringValue(properties, value, placeholder, visitedPlaceholders);
                if ((str = SystemPropertyUtils.resolvePlaceholder(properties, value, placeholder)) == null && (":" != null && (i2 = placeholder.indexOf(":")) != i)) {
                   i1 = ":".length() + i2;
                   String str1 = placeholder.substring(i1);
                   if ((str = SystemPropertyUtils.resolvePlaceholder(properties, value, placeholder.substring(0, i2))) == null) {
                      str = str1;
                   }
                }
                if (str != null) {
                   str = SystemPropertyUtils.parseStringValue(properties, value, str, visitedPlaceholders);
                   i1 = "}".length() + endIndex;
                   buf.replace(startIndex, i1, str);
                   i3 = str.length() + startIndex;
                   startIndex = buf.indexOf("${", i3);
                }else {
                   i3 = "}".length() + endIndex;
                   startIndex = buf.indexOf("${", i3);
                }
                visitedPlaceholders.remove(originalPlaceholder);
             }
          }else {
             startIndex = -1;
          }
       }
       throw new IllegalArgumentException("Circular placeholder reference \'"+originalPlaceholder+"\' in property definitions");
    }
    private static String resolvePlaceholder(Properties properties,String text,String placeholderName){
       String propVal;
       String str = null;
       if ((propVal = SystemPropertyUtils.getProperty(placeholderName, str, text)) == null) {
          if (properties != null) {
             str = properties.getProperty(placeholderName);
          }
          propVal = str;
       }
       return propVal;
    }
    public static String resolvePlaceholders(String text){
       if (text != null) {
          text = SystemPropertyUtils.parseStringValue(null, text, text, new HashSet());
       }
       return text;
    }
    public static String resolvePlaceholders(Properties properties,String text){
       if (text != null) {
          text = SystemPropertyUtils.parseStringValue(properties, text, text, new HashSet());
       }
       return text;
    }
    private static boolean substringMatch(CharSequence str,int index,CharSequence substring){
       int i;
       boolean b;
       int j = 0;
       while (true) {
          if (j < substring.length()) {
             if ((i = index + j) < str.length() && str.charAt(i) == substring.charAt(j)) {
                j++;
             }else {
                b = false;
                break ;
             }
          }else {
             b = true;
             break ;
          }
       }
       return b;
    }
}
