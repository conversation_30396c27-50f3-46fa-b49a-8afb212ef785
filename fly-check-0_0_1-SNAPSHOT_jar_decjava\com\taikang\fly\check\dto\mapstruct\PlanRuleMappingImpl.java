package com.taikang.fly.check.dto.mapstruct.PlanRuleMappingImpl;
import com.taikang.fly.check.dto.mapstruct.PlanRuleMapping;
import java.lang.Object;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.planRule.PlanRuleAddDto;
import com.taikang.fly.check.mybatis.domain.PlanRule;
import java.lang.String;
import com.taikang.fly.check.utils.SequenceGenerator;
import java.time.LocalDateTime;
import com.taikang.fly.check.dto.plan.PlanRuleRespDto;
import com.taikang.fly.check.utils.DateUtils;

public class PlanRuleMappingImpl implements PlanRuleMapping	// class@000176 from classes.dex
{

    public void PlanRuleMappingImpl(){
       super();
    }
    public List DtosToEntry(List planRuleAddDtos){
       List list;
       if (planRuleAddDtos == null) {
          list = null;
       }else {
          list = new ArrayList(planRuleAddDtos.size());
          Iterator iterator = planRuleAddDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.dtoToEntry(iterator.next()));
          }
       }
       return list;
    }
    public PlanRule dtoToEntry(PlanRuleAddDto planRuleAddDto){
       PlanRule planRule;
       if (planRuleAddDto == null) {
          planRule = null;
       }else {
          planRule = new PlanRule();
          planRule.setRuleId(planRuleAddDto.getRuleId());
          planRule.setPlanId(planRuleAddDto.getPlanId());
          planRule.setRuleName(planRuleAddDto.getRuleName());
          planRule.setSourceOfRule(planRuleAddDto.getSourceOfRule());
          planRule.setRuleIntension(planRuleAddDto.getRuleIntension());
          planRule.setPs(planRuleAddDto.getPs());
          planRule.setRuleType(planRuleAddDto.getRuleType());
          planRule.setMedicalCategory(planRuleAddDto.getMedicalCategory());
          planRule.setId(SequenceGenerator.getId());
          planRule.setCreateTime(LocalDateTime.now());
          planRule.setStatus("1");
       }
       return planRule;
    }
    public PlanRuleRespDto entryToDto(PlanRule planRule){
       PlanRuleRespDto planRuleResp;
       if (planRule == null) {
          planRuleResp = null;
       }else {
          planRuleResp = new PlanRuleRespDto();
          planRuleResp.setId(planRule.getId());
          planRuleResp.setRuleId(planRule.getRuleId());
          planRuleResp.setPlanId(planRule.getPlanId());
          planRuleResp.setRuleName(planRule.getRuleName());
          planRuleResp.setSourceOfRule(planRule.getSourceOfRule());
          planRuleResp.setRuleIntension(planRule.getRuleIntension());
          planRuleResp.setPs(planRule.getPs());
          planRuleResp.setRuleType(planRule.getRuleType());
          planRuleResp.setMedicalCategory(planRule.getMedicalCategory());
          planRuleResp.setStatus(planRule.getStatus());
          planRuleResp.setCreateTime(DateUtils.formatLocalDateTime(planRule.getCreateTime()));
       }
       return planRuleResp;
    }
    public List entryToDtos(List planRules){
       List list;
       if (planRules == null) {
          list = null;
       }else {
          list = new ArrayList(planRules.size());
          Iterator iterator = planRules.iterator();
          while (iterator.hasNext()) {
             list.add(this.entryToDto(iterator.next()));
          }
       }
       return list;
    }
}
