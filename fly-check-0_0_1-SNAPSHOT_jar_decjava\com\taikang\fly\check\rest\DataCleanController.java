package com.taikang.fly.check.rest.DataCleanController;
import java.lang.Object;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleAddDto;
import com.taikang.fly.check.comm.CommResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.DataCleanService;
import java.lang.String;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleEditDto;
import org.springframework.util.StringUtils;
import com.taikang.fly.check.mybatis.domain.DataCleanRule;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleQueryDto;
import com.taikang.fly.check.comm.NativePage;
import java.util.List;

public class DataCleanController	// class@000288 from classes.dex
{
    private DataCleanService dataCleanService;

    public void DataCleanController(){
       super();
    }
    public CommResponse addDataCleanRule(DataCleanRuleAddDto dataCleanRuleAddDto){
       return CommResponse.success(this.dataCleanService.add(dataCleanRuleAddDto));
    }
    public CommResponse delDataCleanRule(String id){
       return CommResponse.success(this.dataCleanService.delete(id));
    }
    public CommResponse editDataCleanRule(DataCleanRuleEditDto dataCleanRuleEditDto){
       return CommResponse.success(this.dataCleanService.edit(dataCleanRuleEditDto));
    }
    public CommResponse execDataCleanRule(String ids){
       String errorMsg = this.dataCleanService.execByDataCleanRuleIds(ids);
       CommResponse uCommRespons = (StringUtils.isEmpty(errorMsg))? CommResponse.success(): CommResponse.error("-23", errorMsg, null);
       return uCommRespons;
    }
    public CommResponse getDataCleanRuleItem(String id){
       return CommResponse.success(this.dataCleanService.queryById(id));
    }
    public CommResponse getDataCleanRuleList(Integer pageNum,Integer pageSize,DataCleanRuleQueryDto queryDto){
       return CommResponse.success(this.dataCleanService.queryAll(pageNum, pageSize, queryDto));
    }
    public CommResponse getTableNameList(){
       return CommResponse.success(this.dataCleanService.getTableNameList());
    }
}
