package com.taikang.fly.check.vo.drg.DrgTVRecordVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgTVRecordVo	// class@00037a from classes.dex
{
    private String chkRslt;
    private String ruleCode;
    private String ruleName;
    private String setlId;

    public void DrgTVRecordVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgTVRecordVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgTVRecordVo) {
             b = false;
          }else {
             DrgTVRecordVo uDrgTVRecord = o;
             if (!uDrgTVRecord.canEqual(this)) {
                b = false;
             }else {
                String setlId = this.getSetlId();
                String setlId1 = uDrgTVRecord.getSetlId();
                if (setlId == null) {
                   if (setlId1 != null) {
                      b = false;
                   }
                }else if(setlId.equals(setlId1)){
                }
                String ruleCode = this.getRuleCode();
                String ruleCode1 = uDrgTVRecord.getRuleCode();
                if (ruleCode == null) {
                   if (ruleCode1 != null) {
                      b = false;
                   }
                }else if(ruleCode.equals(ruleCode1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = uDrgTVRecord.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String chkRslt = this.getChkRslt();
                String chkRslt1 = uDrgTVRecord.getChkRslt();
                if (chkRslt == null) {
                   if (chkRslt1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!chkRslt.equals(chkRslt1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getChkRslt(){
       return this.chkRslt;
    }
    public String getRuleCode(){
       return this.ruleCode;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSetlId(){
       return this.setlId;
    }
    public int hashCode(){
       String $setlId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($setlId = this.getSetlId()) == null)? i: $setlId.hashCode();
       result = i1 + 59;
       String $ruleCode = this.getRuleCode();
       int i2 = result * 59;
       i1 = ($ruleCode == null)? i: $ruleCode.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $chkRslt = this.getChkRslt();
       i1 = result * 59;
       if ($chkRslt != null) {
          i = $chkRslt.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setChkRslt(String chkRslt){
       this.chkRslt = chkRslt;
    }
    public void setRuleCode(String ruleCode){
       this.ruleCode = ruleCode;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSetlId(String setlId){
       this.setlId = setlId;
    }
    public String toString(){
       return "DrgTVRecordVo\(setlId="+this.getSetlId()+", ruleCode="+this.getRuleCode()+", ruleName="+this.getRuleName()+", chkRslt="+this.getChkRslt()+"\)";
    }
}
