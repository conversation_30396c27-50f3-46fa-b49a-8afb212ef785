package com.taikang.fly.check.dto.DictDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictDto implements Serializable	// class@00009c from classes.dex
{
    private String dictCode;
    private String dictName;
    private static final long serialVersionUID = 0x1;

    public void DictDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DictDto) {
             b = false;
          }else {
             DictDto uDictDto = o;
             if (!uDictDto.canEqual(this)) {
                b = false;
             }else {
                String dictCode = this.getDictCode();
                String dictCode1 = uDictDto.getDictCode();
                if (dictCode == null) {
                   if (dictCode1 != null) {
                      b = false;
                   }
                }else if(dictCode.equals(dictCode1)){
                }
                String dictName = this.getDictName();
                String dictName1 = uDictDto.getDictName();
                if (dictName == null) {
                   if (dictName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!dictName.equals(dictName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDictCode(){
       return this.dictCode;
    }
    public String getDictName(){
       return this.dictName;
    }
    public int hashCode(){
       String $dictCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($dictCode = this.getDictCode()) == null)? i: $dictCode.hashCode();
       result = i1 + 59;
       String $dictName = this.getDictName();
       i1 = result * 59;
       if ($dictName != null) {
          i = $dictName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDictCode(String dictCode){
       this.dictCode = dictCode;
    }
    public void setDictName(String dictName){
       this.dictName = dictName;
    }
    public String toString(){
       return "DictDto\(dictCode="+this.getDictCode()+", dictName="+this.getDictName()+"\)";
    }
}
