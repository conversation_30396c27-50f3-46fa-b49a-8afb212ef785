package com.taikang.fly.check.dto.merge.MergeDownloadDTO;
import java.io.Serializable;
import java.lang.Object;
import java.util.List;
import java.lang.Integer;
import java.lang.String;
import java.lang.Double;
import java.lang.StringBuilder;

public class MergeDownloadDTO implements Serializable	// class@000194 from classes.dex
{
    private Integer count;
    private String datasources;
    private String dischargeDepartment;
    private String endTime;
    private List id;
    private Integer maxHospitalizationDays;
    private Double maxTotalMedicalAmount;
    private Integer minHospitalizationDays;
    private Double minTotalMedicalAmount;
    private String policyBasis;
    private String ruleName;
    private String ruleType;
    private String startTime;
    private Integer tag;
    private String time;

    public void MergeDownloadDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MergeDownloadDTO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MergeDownloadDTO){
          b = false;
       }else {
          MergeDownloadDTO mergeDownloa = o;
          if (!mergeDownloa.canEqual(this)) {
             b = false;
          }else {
             List id = this.getId();
             List id1 = mergeDownloa.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             Integer tag = this.getTag();
             Integer tag1 = mergeDownloa.getTag();
             if (tag == null) {
                if (tag1 != null) {
                   b = false;
                }
             }else if(tag.equals(tag1)){
             }
             Integer count = this.getCount();
             Integer count1 = mergeDownloa.getCount();
             if (count == null) {
                if (count1 != null) {
                   b = false;
                }
             }else if(count.equals(count1)){
             }
             String time = this.getTime();
             String time1 = mergeDownloa.getTime();
             if (time == null) {
                if (time1 != null) {
                   b = false;
                }
             }else if(time.equals(time1)){
             }
             String datasources = this.getDatasources();
             String datasources1 = mergeDownloa.getDatasources();
             if (datasources == null) {
                if (datasources1 != null) {
                   b = false;
                }
             }else if(datasources.equals(datasources1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = mergeDownloa.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                label_00a7 :
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = mergeDownloa.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String dischargeDep = this.getDischargeDepartment();
             String dischargeDep1 = mergeDownloa.getDischargeDepartment();
             if (dischargeDep == null) {
                if (dischargeDep1 != null) {
                   b = false;
                }
             }else if(dischargeDep.equals(dischargeDep1)){
             }
             Double minTotalMedi = this.getMinTotalMedicalAmount();
             Double minTotalMedi1 = mergeDownloa.getMinTotalMedicalAmount();
             if (minTotalMedi == null) {
                if (minTotalMedi1 != null) {
                label_00ef :
                   b = false;
                }
             }else if(minTotalMedi.equals(minTotalMedi1)){
             }
             Double maxTotalMedi = this.getMaxTotalMedicalAmount();
             Double maxTotalMedi1 = mergeDownloa.getMaxTotalMedicalAmount();
             if (maxTotalMedi == null) {
                if (maxTotalMedi1 != null) {
                   b = false;
                }
             }else if(maxTotalMedi.equals(maxTotalMedi1)){
             }
             Integer minHospitali = this.getMinHospitalizationDays();
             Integer minHospitali1 = mergeDownloa.getMinHospitalizationDays();
             if (minHospitali == null) {
                if (minHospitali1 != null) {
                label_011f :
                   b = false;
                }
             }else if(minHospitali.equals(minHospitali1)){
             }
             Integer maxHospitali = this.getMaxHospitalizationDays();
             Integer maxHospitali1 = mergeDownloa.getMaxHospitalizationDays();
             if (maxHospitali == null) {
                if (maxHospitali1 != null) {
                   b = false;
                }
             }else if(maxHospitali.equals(maxHospitali1)){
             }
             String startTime = this.getStartTime();
             String startTime1 = mergeDownloa.getStartTime();
             if (startTime == null) {
                if (startTime1 != null) {
                label_014f :
                   b = false;
                }
             }else if(startTime.equals(startTime1)){
             }
             String endTime = this.getEndTime();
             String endTime1 = mergeDownloa.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = mergeDownloa.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_017f :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Integer getCount(){
       return this.count;
    }
    public String getDatasources(){
       return this.datasources;
    }
    public String getDischargeDepartment(){
       return this.dischargeDepartment;
    }
    public String getEndTime(){
       return this.endTime;
    }
    public List getId(){
       return this.id;
    }
    public Integer getMaxHospitalizationDays(){
       return this.maxHospitalizationDays;
    }
    public Double getMaxTotalMedicalAmount(){
       return this.maxTotalMedicalAmount;
    }
    public Integer getMinHospitalizationDays(){
       return this.minHospitalizationDays;
    }
    public Double getMinTotalMedicalAmount(){
       return this.minTotalMedicalAmount;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getStartTime(){
       return this.startTime;
    }
    public Integer getTag(){
       return this.tag;
    }
    public String getTime(){
       return this.time;
    }
    public int hashCode(){
       List $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       Integer $tag = this.getTag();
       int i1 = result * 59;
       i = ($tag == null)? 43: $tag.hashCode();
       result = i1 + i;
       Integer $count = this.getCount();
       i1 = result * 59;
       i = ($count == null)? 43: $count.hashCode();
       result = i1 + i;
       String $time = this.getTime();
       i1 = result * 59;
       i = ($time == null)? 43: $time.hashCode();
       result = i1 + i;
       String $datasources = this.getDatasources();
       i1 = result * 59;
       i = ($datasources == null)? 43: $datasources.hashCode();
       result = i1 + i;
       String ruleName = this.getRuleName();
       i1 = result * 59;
       i = (ruleName == null)? 43: ruleName.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String dischargeDep = this.getDischargeDepartment();
       i1 = (i1 + i) * 59;
       i = (dischargeDep == null)? 43: dischargeDep.hashCode();
       Double minTotalMedi = this.getMinTotalMedicalAmount();
       i1 = (i1 + i) * 59;
       i = (minTotalMedi == null)? 43: minTotalMedi.hashCode();
       Double maxTotalMedi = this.getMaxTotalMedicalAmount();
       i1 = (i1 + i) * 59;
       i = (maxTotalMedi == null)? 43: maxTotalMedi.hashCode();
       Integer minHospitali = this.getMinHospitalizationDays();
       i1 = (i1 + i) * 59;
       i = (minHospitali == null)? 43: minHospitali.hashCode();
       Integer maxHospitali = this.getMaxHospitalizationDays();
       i1 = (i1 + i) * 59;
       i = (maxHospitali == null)? 43: maxHospitali.hashCode();
       String startTime = this.getStartTime();
       i1 = (i1 + i) * 59;
       i = (startTime == null)? 43: startTime.hashCode();
       String endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       return (i1 + i);
    }
    public void setCount(Integer count){
       this.count = count;
    }
    public void setDatasources(String datasources){
       this.datasources = datasources;
    }
    public void setDischargeDepartment(String dischargeDepartment){
       this.dischargeDepartment = dischargeDepartment;
    }
    public void setEndTime(String endTime){
       this.endTime = endTime;
    }
    public void setId(List id){
       this.id = id;
    }
    public void setMaxHospitalizationDays(Integer maxHospitalizationDays){
       this.maxHospitalizationDays = maxHospitalizationDays;
    }
    public void setMaxTotalMedicalAmount(Double maxTotalMedicalAmount){
       this.maxTotalMedicalAmount = maxTotalMedicalAmount;
    }
    public void setMinHospitalizationDays(Integer minHospitalizationDays){
       this.minHospitalizationDays = minHospitalizationDays;
    }
    public void setMinTotalMedicalAmount(Double minTotalMedicalAmount){
       this.minTotalMedicalAmount = minTotalMedicalAmount;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setStartTime(String startTime){
       this.startTime = startTime;
    }
    public void setTag(Integer tag){
       this.tag = tag;
    }
    public void setTime(String time){
       this.time = time;
    }
    public String toString(){
       return "MergeDownloadDTO\(id="+this.getId()+", tag="+this.getTag()+", count="+this.getCount()+", time="+this.getTime()+", datasources="+this.getDatasources()+", ruleName="+this.getRuleName()+", ruleType="+this.getRuleType()+", dischargeDepartment="+this.getDischargeDepartment()+", minTotalMedicalAmount="+this.getMinTotalMedicalAmount()+", maxTotalMedicalAmount="+this.getMaxTotalMedicalAmount()+", minHospitalizationDays="+this.getMinHospitalizationDays()+", maxHospitalizationDays="+this.getMaxHospitalizationDays()+", startTime="+this.getStartTime()+", endTime="+this.getEndTime()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
