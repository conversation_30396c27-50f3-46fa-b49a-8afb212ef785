package com.taikang.fly.check.vo.MdcsFundSetlListDiagVo;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MdcsFundSetlListDiagVo implements Serializable	// class@000359 from classes.dex
{
    private String diagCode;
    private String diagName;

    public void MdcsFundSetlListDiagVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MdcsFundSetlListDiagVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MdcsFundSetlListDiagVo) {
             b = false;
          }else {
             MdcsFundSetlListDiagVo mdcsFundSetl = o;
             if (!mdcsFundSetl.canEqual(this)) {
                b = false;
             }else {
                String diagCode = this.getDiagCode();
                String diagCode1 = mdcsFundSetl.getDiagCode();
                if (diagCode == null) {
                   if (diagCode1 != null) {
                      b = false;
                   }
                }else if(diagCode.equals(diagCode1)){
                }
                String diagName = this.getDiagName();
                String diagName1 = mdcsFundSetl.getDiagName();
                if (diagName == null) {
                   if (diagName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!diagName.equals(diagName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDiagCode(){
       return this.diagCode;
    }
    public String getDiagName(){
       return this.diagName;
    }
    public int hashCode(){
       String $diagCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($diagCode = this.getDiagCode()) == null)? i: $diagCode.hashCode();
       result = i1 + 59;
       String $diagName = this.getDiagName();
       i1 = result * 59;
       if ($diagName != null) {
          i = $diagName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDiagCode(String diagCode){
       this.diagCode = diagCode;
    }
    public void setDiagName(String diagName){
       this.diagName = diagName;
    }
    public String toString(){
       return "MdcsFundSetlListDiagVo\(diagCode="+this.getDiagCode()+", diagName="+this.getDiagName()+"\)";
    }
}
