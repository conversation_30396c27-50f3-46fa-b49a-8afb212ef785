package com.taikang.fly.check.rest.YbConsumablesListController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybconsumablesList.YbConsumablesListSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.YbConsumablesListService;

public class YbConsumablesListController	// class@0002ba from classes.dex
{
    private YbConsumablesListService ybConsumablesListService;
    private static final Logger log;

    static {
       YbConsumablesListController.log = LoggerFactory.getLogger(YbConsumablesListController.class);
    }
    public void YbConsumablesListController(){
       super();
    }
    public RmpResponse queryByPid(Integer page,Integer size,YbConsumablesListSearchDto ybConsumablesListSearchDto){
       return RmpResponse.success(this.ybConsumablesListService.queryList(page, size, ybConsumablesListSearchDto));
    }
}
