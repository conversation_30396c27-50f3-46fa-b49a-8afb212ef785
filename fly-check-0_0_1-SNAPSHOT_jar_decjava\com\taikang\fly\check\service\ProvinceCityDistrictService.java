package com.taikang.fly.check.service.ProvinceCityDistrictService;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taikang.fly.check.mybatis.dao.ProvinceCityDistrictDao;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Collection;
import org.springframework.util.CollectionUtils;
import com.taikang.fly.check.mybatis.domain.ProvinceCityDistrict;

public class ProvinceCityDistrictService	// class@0002f7 from classes.dex
{
    private ProvinceCityDistrictDao provinceCityDistrictDao;

    public void ProvinceCityDistrictService(){
       super();
    }
    public List queryByPid(String pid){
       List provinceCityDistricts = this.provinceCityDistrictDao.selectList(new QueryWrapper().eq("pid", pid));
       return provinceCityDistricts;
    }
    public List regionInfo(){
       String pid;
       List regionResult = new ArrayList();
       List regionInfoList = this.provinceCityDistrictDao.regionInfo();
       String tempPid = regionInfoList.get(0).get("pid").toString();
       Map itemMap = new HashMap(3);
       itemMap.put("label", regionInfoList.get(0).get("pname"));
       itemMap.put("value", regionInfoList.get(0).get("pid").toString());
       List childrenList = new ArrayList();
       Iterator iterator = regionInfoList.iterator();
       while (iterator.hasNext()) {
          Map map = iterator.next();
          pid = map.get("pid").toString();
          if (!pid.equalsIgnoreCase(tempPid)) {
             itemMap.put("children", childrenList);
             regionResult.add(itemMap);
             itemMap = new HashMap(3);
             childrenList = new ArrayList();
             tempPid = map.get("pid").toString();
             itemMap.put("label", map.get("pname"));
             itemMap.put("value", map.get("pid").toString());
          }
          HashMap hashMap = new HashMap(3);
          hashMap.put("label", map.get("cname"));
          hashMap.put("value", map.get("cid").toString());
          hashMap.put("children", new ArrayList());
          childrenList.add(hashMap);
       }
       itemMap.put("children", childrenList);
       regionResult.add(itemMap);
       return regionResult;
    }
    public List treeMap(){
       List provinceCityDistricts = this.queryByPid("0");
       List baseMaps = new ArrayList();
       String vals = "value";
       String label = "label";
       String children = "children";
       if (!CollectionUtils.isEmpty(provinceCityDistricts)) {
          Iterator iterator = provinceCityDistricts.iterator();
          while (iterator.hasNext()) {
             ProvinceCityDistrict provinceCity = iterator.next();
             String id = provinceCity.getId();
             ArrayList uArrayList = new ArrayList();
             HashMap hashMap = new HashMap(3);
             hashMap.put(vals, id);
             hashMap.put(label, provinceCity.getName());
             hashMap.put(children, uArrayList);
             baseMaps.add(hashMap);
             List list = this.queryByPid(id);
             if (!CollectionUtils.isEmpty(list)) {
                Iterator iterator1 = list.iterator();
                while (iterator1.hasNext()) {
                   ProvinceCityDistrict provinceCity1 = iterator1.next();
                   String id1 = provinceCity1.getId();
                   HashMap hashMap1 = new HashMap(3);
                   hashMap1.put(vals, id1);
                   hashMap1.put(label, provinceCity1.getName());
                   hashMap1.put(children, new ArrayList());
                   uArrayList.add(hashMap1);
                   if (!CollectionUtils.isEmpty(this.queryByPid(id1))) {
                      HashMap hashMap2 = new HashMap(3);
                      hashMap2.put(vals, provinceCity1.getId());
                      hashMap2.put(label, provinceCity1.getName());
                      hashMap2.put(children, new ArrayList());
                   }else {
                      continue ;
                   }
                }
             }
          }
       }
       return baseMaps;
    }
}
