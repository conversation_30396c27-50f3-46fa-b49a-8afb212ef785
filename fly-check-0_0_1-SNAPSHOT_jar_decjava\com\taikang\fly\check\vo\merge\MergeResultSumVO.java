package com.taikang.fly.check.vo.merge.MergeResultSumVO;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.Double;
import java.lang.StringBuilder;

public class MergeResultSumVO	// class@000380 from classes.dex
{
    private Double beyondAmt;
    private Double beyondCnt;
    private Double bmiConveredAmount;
    private Integer countMan;
    private String policyBasis;
    private String ruleConnotation;
    private String ruleName;
    private String ruleSql;
    private String ruleType;
    private Double totalAmount;
    private Double totalCount;
    private Double useAmt;
    private Double useCnt;

    public void MergeResultSumVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MergeResultSumVO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MergeResultSumVO){
          b = false;
       }else {
          MergeResultSumVO mergeResultS = o;
          if (!mergeResultS.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = mergeResultS.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = mergeResultS.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ruleConnotat = this.getRuleConnotation();
             String ruleConnotat1 = mergeResultS.getRuleConnotation();
             if (ruleConnotat == null) {
                if (ruleConnotat1 != null) {
                   b = false;
                }
             }else if(ruleConnotat.equals(ruleConnotat1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = mergeResultS.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             Integer countMan = this.getCountMan();
             Integer countMan1 = mergeResultS.getCountMan();
             if (countMan == null) {
                if (countMan1 != null) {
                   b = false;
                }
             }else if(countMan.equals(countMan1)){
             }
             Double totalCount = this.getTotalCount();
             Double totalCount1 = mergeResultS.getTotalCount();
             if (totalCount == null) {
                if (totalCount1 != null) {
                   b = false;
                }
             }else if(totalCount.equals(totalCount1)){
             }
             Double totalAmount = this.getTotalAmount();
             Double totalAmount1 = mergeResultS.getTotalAmount();
             if (totalAmount == null) {
                if (totalAmount1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(totalAmount.equals(totalAmount1)){
             }
             Double useCnt = this.getUseCnt();
             Double useCnt1 = mergeResultS.getUseCnt();
             if (useCnt == null) {
                if (useCnt1 != null) {
                   b = false;
                }
             }else if(useCnt.equals(useCnt1)){
             }
             Double useAmt = this.getUseAmt();
             Double useAmt1 = mergeResultS.getUseAmt();
             if (useAmt == null) {
                if (useAmt1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(useAmt.equals(useAmt1)){
             }
             Double bmiConveredA = this.getBmiConveredAmount();
             Double bmiConveredA1 = mergeResultS.getBmiConveredAmount();
             if (bmiConveredA == null) {
                if (bmiConveredA1 != null) {
                   b = false;
                }
             }else if(bmiConveredA.equals(bmiConveredA1)){
             }
             Double beyondCnt = this.getBeyondCnt();
             Double beyondCnt1 = mergeResultS.getBeyondCnt();
             if (beyondCnt == null) {
                if (beyondCnt1 != null) {
                label_011b :
                   b = false;
                }
             }else if(beyondCnt.equals(beyondCnt1)){
             }
             Double beyondAmt = this.getBeyondAmt();
             Double beyondAmt1 = mergeResultS.getBeyondAmt();
             if (beyondAmt == null) {
                if (beyondAmt1 != null) {
                   b = false;
                }
             }else if(beyondAmt.equals(beyondAmt1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = mergeResultS.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_014b :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Double getBeyondAmt(){
       return this.beyondAmt;
    }
    public Double getBeyondCnt(){
       return this.beyondCnt;
    }
    public Double getBmiConveredAmount(){
       return this.bmiConveredAmount;
    }
    public Integer getCountMan(){
       return this.countMan;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleConnotation(){
       return this.ruleConnotation;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public Double getTotalAmount(){
       return this.totalAmount;
    }
    public Double getTotalCount(){
       return this.totalCount;
    }
    public Double getUseAmt(){
       return this.useAmt;
    }
    public Double getUseCnt(){
       return this.useCnt;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $ruleType = this.getRuleType();
       int i1 = result * 59;
       i = ($ruleType == null)? 43: $ruleType.hashCode();
       result = i1 + i;
       String $ruleConnotation = this.getRuleConnotation();
       i1 = result * 59;
       i = ($ruleConnotation == null)? 43: $ruleConnotation.hashCode();
       result = i1 + i;
       String $ruleSql = this.getRuleSql();
       i1 = result * 59;
       i = ($ruleSql == null)? 43: $ruleSql.hashCode();
       result = i1 + i;
       Integer $countMan = this.getCountMan();
       i1 = result * 59;
       i = ($countMan == null)? 43: $countMan.hashCode();
       result = i1 + i;
       Double totalCount = this.getTotalCount();
       i1 = result * 59;
       i = (totalCount == null)? 43: totalCount.hashCode();
       Double totalAmount = this.getTotalAmount();
       i1 = (i1 + i) * 59;
       i = (totalAmount == null)? 43: totalAmount.hashCode();
       Double useCnt = this.getUseCnt();
       i1 = (i1 + i) * 59;
       i = (useCnt == null)? 43: useCnt.hashCode();
       Double useAmt = this.getUseAmt();
       i1 = (i1 + i) * 59;
       i = (useAmt == null)? 43: useAmt.hashCode();
       Double bmiConveredA = this.getBmiConveredAmount();
       i1 = (i1 + i) * 59;
       i = (bmiConveredA == null)? 43: bmiConveredA.hashCode();
       Double beyondCnt = this.getBeyondCnt();
       i1 = (i1 + i) * 59;
       i = (beyondCnt == null)? 43: beyondCnt.hashCode();
       Double beyondAmt = this.getBeyondAmt();
       i1 = (i1 + i) * 59;
       i = (beyondAmt == null)? 43: beyondAmt.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       return (i1 + i);
    }
    public void setBeyondAmt(Double beyondAmt){
       this.beyondAmt = beyondAmt;
    }
    public void setBeyondCnt(Double beyondCnt){
       this.beyondCnt = beyondCnt;
    }
    public void setBmiConveredAmount(Double bmiConveredAmount){
       this.bmiConveredAmount = bmiConveredAmount;
    }
    public void setCountMan(Integer countMan){
       this.countMan = countMan;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleConnotation(String ruleConnotation){
       this.ruleConnotation = ruleConnotation;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setTotalAmount(Double totalAmount){
       this.totalAmount = totalAmount;
    }
    public void setTotalCount(Double totalCount){
       this.totalCount = totalCount;
    }
    public void setUseAmt(Double useAmt){
       this.useAmt = useAmt;
    }
    public void setUseCnt(Double useCnt){
       this.useCnt = useCnt;
    }
    public String toString(){
       return "MergeResultSumVO\(ruleName="+this.getRuleName()+", ruleType="+this.getRuleType()+", ruleConnotation="+this.getRuleConnotation()+", ruleSql="+this.getRuleSql()+", countMan="+this.getCountMan()+", totalCount="+this.getTotalCount()+", totalAmount="+this.getTotalAmount()+", useCnt="+this.getUseCnt()+", useAmt="+this.getUseAmt()+", bmiConveredAmount="+this.getBmiConveredAmount()+", beyondCnt="+this.getBeyondCnt()+", beyondAmt="+this.getBeyondAmt()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
