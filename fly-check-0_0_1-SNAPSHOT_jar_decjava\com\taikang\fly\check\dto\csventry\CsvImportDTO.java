package com.taikang.fly.check.dto.csventry.CsvImportDTO;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class CsvImportDTO	// class@0000d8 from classes.dex
{
    private String boundarySymbol;
    private Integer errorCount;
    private String id;
    private String separator;

    public void CsvImportDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CsvImportDTO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof CsvImportDTO) {
             b = false;
          }else {
             CsvImportDTO uCsvImportDT = o;
             if (!uCsvImportDT.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uCsvImportDT.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                Integer errorCount = this.getErrorCount();
                Integer errorCount1 = uCsvImportDT.getErrorCount();
                if (errorCount == null) {
                   if (errorCount1 != null) {
                      b = false;
                   }
                }else if(errorCount.equals(errorCount1)){
                }
                String separator = this.getSeparator();
                String separator1 = uCsvImportDT.getSeparator();
                if (separator == null) {
                   if (separator1 != null) {
                      b = false;
                   }
                }else if(separator.equals(separator1)){
                }
                String boundarySymb = this.getBoundarySymbol();
                String boundarySymb1 = uCsvImportDT.getBoundarySymbol();
                if (boundarySymb == null) {
                   if (boundarySymb1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!boundarySymb.equals(boundarySymb1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getBoundarySymbol(){
       return this.boundarySymbol;
    }
    public Integer getErrorCount(){
       return this.errorCount;
    }
    public String getId(){
       return this.id;
    }
    public String getSeparator(){
       return this.separator;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       Integer $errorCount = this.getErrorCount();
       int i2 = result * 59;
       i1 = ($errorCount == null)? i: $errorCount.hashCode();
       result = i2 + i1;
       String $separator = this.getSeparator();
       i2 = result * 59;
       i1 = ($separator == null)? i: $separator.hashCode();
       result = i2 + i1;
       String $boundarySymbol = this.getBoundarySymbol();
       i1 = result * 59;
       if ($boundarySymbol != null) {
          i = $boundarySymbol.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setBoundarySymbol(String boundarySymbol){
       this.boundarySymbol = boundarySymbol;
    }
    public void setErrorCount(Integer errorCount){
       this.errorCount = errorCount;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setSeparator(String separator){
       this.separator = separator;
    }
    public String toString(){
       return "CsvImportDTO\(id="+this.getId()+", errorCount="+this.getErrorCount()+", separator="+this.getSeparator()+", boundarySymbol="+this.getBoundarySymbol()+"\)";
    }
}
