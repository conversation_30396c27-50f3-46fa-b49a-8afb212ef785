package org.springframework.boot.loader.jar.CentralDirectoryEndRecord;
import org.springframework.boot.loader.data.RandomAccessData;
import java.lang.Object;
import java.io.IOException;
import java.lang.StringBuilder;
import java.lang.String;
import java.lang.Math;
import org.springframework.boot.loader.jar.Bytes;
import java.lang.IllegalStateException;

class CentralDirectoryEndRecord	// class@00054b from classes.dex
{
    private byte[] block;
    private int offset;
    private int size;
    private static final int COMMENT_LENGTH_OFFSET = 20;
    private static final int MAXIMUM_COMMENT_LENGTH = 65535;
    private static final int MAXIMUM_SIZE = 65557;
    private static final int MINIMUM_SIZE = 22;
    private static final int READ_BLOCK_SIZE = 256;
    private static final int SIGNATURE = 101010256;

    void CentralDirectoryEndRecord(RandomAccessData data){
       super();
       this.block = this.createBlockFromEndOfData(data, 256);
       this.size = 22;
       this.offset = this.block.length - this.size;
       while (true) {
          if (this.isValid()) {
             return;
          }
          int i = this.size + 1;
          this.size = i;
          if (this.size > this.block.length) {
             if (this.size < 0x10015 && ((long)this.size - data.getSize()) <= 0) {
                i = this.size + 256;
                this.block = this.createBlockFromEndOfData(data, i);
             }else {
                break ;
             }
          }
          i = this.block.length - this.size;
          this.offset = i;
       }
       throw new IOException("Unable to find ZIP central directory records after reading "+this.size+" bytes");
    }
    private byte[] createBlockFromEndOfData(RandomAccessData data,int size){
       int length = (int)Math.min(data.getSize(), (long)size);
       return data.read((data.getSize() - (long)length), (long)length);
    }
    private boolean isValid(){
       boolean b = false;
       if (this.block.length >= 22 && (!(Bytes.littleEndianValue(this.block, (this.offset + 0), 4) - 0x6054b50) && !((long)this.size - (22 + Bytes.littleEndianValue(this.block, (this.offset + 20), 2))))) {
          b = true;
       }
       return b;
    }
    public RandomAccessData getCentralDirectory(RandomAccessData data){
       long offset = Bytes.littleEndianValue(this.block, (this.offset + 16), 4);
       long length = Bytes.littleEndianValue(this.block, (this.offset + 12), 4);
       return data.getSubsection(offset, length);
    }
    public int getNumberOfRecords(){
       long numberOfRecords = Bytes.littleEndianValue(this.block, (this.offset + 10), 2);
       if (!(numberOfRecords - 0xffff)) {
          throw new IllegalStateException("Zip64 archives are not supported");
       }
       return (int)numberOfRecords;
    }
    public long getStartOfArchive(RandomAccessData data){
       long length = Bytes.littleEndianValue(this.block, (this.offset + 12), 4);
       long specifiedOffset = Bytes.littleEndianValue(this.block, (this.offset + 16), 4);
       long actualOffset = (data.getSize() - (long)this.size) - length;
       return (actualOffset - specifiedOffset);
    }
}
