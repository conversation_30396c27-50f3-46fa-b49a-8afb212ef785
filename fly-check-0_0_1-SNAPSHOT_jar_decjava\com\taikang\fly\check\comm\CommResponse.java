package com.taikang.fly.check.comm.CommResponse;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.StringBuilder;

public class CommResponse	// class@000070 from classes.dex
{
    private String code;
    private Object data;
    private String message;

    public void CommResponse(){
       super();
    }
    public void CommResponse(String code,String message,Object data){
       super();
       this.code = code;
       this.message = message;
       this.data = data;
    }
    public static CommResponse error(ResponseCodeEnum responseCodeEnum){
       return new CommResponse(responseCodeEnum.getCode(), responseCodeEnum.getMsg(), null);
    }
    public static CommResponse error(String code,ResponseCodeEnum responseCodeEnum){
       return new CommResponse(code, responseCodeEnum.getMsg(), null);
    }
    public static CommResponse error(String code,String message,Object t){
       return new CommResponse(code, message, t);
    }
    public static CommResponse success(){
       return new CommResponse(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), null);
    }
    public static CommResponse success(Object t){
       return new CommResponse(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), t);
    }
    public static CommResponse success(String code,String message,Object t){
       return new CommResponse(code, message, t);
    }
    protected boolean canEqual(Object other){
       return other instanceof CommResponse;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof CommResponse) {
             b = false;
          }else {
             CommResponse uCommRespons = o;
             if (!uCommRespons.canEqual(this)) {
                b = false;
             }else {
                String code = this.getCode();
                String code1 = uCommRespons.getCode();
                if (code == null) {
                   if (code1 != null) {
                      b = false;
                   }
                }else if(code.equals(code1)){
                }
                String message = this.getMessage();
                String message1 = uCommRespons.getMessage();
                if (message == null) {
                   if (message1 != null) {
                      b = false;
                   }
                }else if(message.equals(message1)){
                }
                Object data = this.getData();
                Object data1 = uCommRespons.getData();
                if (data == null) {
                   if (data1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!data.equals(data1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCode(){
       return this.code;
    }
    public Object getData(){
       return this.data;
    }
    public String getMessage(){
       return this.message;
    }
    public int hashCode(){
       String $code;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($code = this.getCode()) == null)? i: $code.hashCode();
       result = i1 + 59;
       String $message = this.getMessage();
       int i2 = result * 59;
       i1 = ($message == null)? i: $message.hashCode();
       result = i2 + i1;
       Object $data = this.getData();
       i1 = result * 59;
       if ($data != null) {
          i = $data.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCode(String code){
       this.code = code;
    }
    public void setData(Object data){
       this.data = data;
    }
    public void setMessage(String message){
       this.message = message;
    }
    public String toString(){
       return "CommResponse\(code="+this.getCode()+", message="+this.getMessage()+", data="+this.getData()+"\)";
    }
}
