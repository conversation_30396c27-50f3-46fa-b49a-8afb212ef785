"""
配置管理模块
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # 应用配置
    app_name: str = "FlyCheck Python"
    app_version: str = "0.1.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    database_url: str = "duckdb:///./flycheck.db"
    database_echo: bool = False
    
    # 安全配置
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT密钥"
    )
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Redis配置 (用于Celery)
    redis_url: str = "redis://localhost:6379/0"
    
    # 文件上传配置
    upload_dir: str = "./uploads"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    # 分页配置
    default_page_size: int = 20
    max_page_size: int = 1000
    
    # 规则引擎配置
    rule_timeout: int = 300  # 规则执行超时时间(秒)
    max_concurrent_rules: int = 10  # 最大并发规则数
    
    # 数据清洗配置
    batch_size: int = 1000  # 批处理大小
    
    class Config:
        env_prefix = "FLYCHECK_"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
