package org.springframework.boot.loader.jar.CentralDirectoryFileHeader;
import org.springframework.boot.loader.jar.FileHeader;
import org.springframework.boot.loader.jar.AsciiBytes;
import java.lang.String;
import java.lang.Object;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.zone.ZoneRules;
import java.time.ZoneOffset;
import org.springframework.boot.loader.data.RandomAccessData;
import org.springframework.boot.loader.jar.JarEntryFilter;
import java.lang.System;
import org.springframework.boot.loader.jar.Bytes;
import java.lang.CharSequence;

final class CentralDirectoryFileHeader implements FileHeader	// class@00054c from classes.dex
{
    private AsciiBytes comment;
    private byte[] extra;
    private byte[] header;
    private int headerOffset;
    private long localHeaderOffset;
    private AsciiBytes name;
    private static final AsciiBytes NO_COMMENT;
    private static final byte[] NO_EXTRA;
    private static final AsciiBytes SLASH;

    static {
       CentralDirectoryFileHeader.SLASH = new AsciiBytes("/");
       byte[] uobyteArray = new byte[0];
       CentralDirectoryFileHeader.NO_EXTRA = uobyteArray;
       CentralDirectoryFileHeader.NO_COMMENT = new AsciiBytes("");
    }
    void CentralDirectoryFileHeader(){
       super();
    }
    void CentralDirectoryFileHeader(byte[] header,int headerOffset,AsciiBytes name,byte[] extra,AsciiBytes comment,long localHeaderOffset){
       super();
       this.header = header;
       this.headerOffset = headerOffset;
       this.name = name;
       this.extra = extra;
       this.comment = comment;
       this.localHeaderOffset = localHeaderOffset;
    }
    private long decodeMsDosFormatDateTime(long datetime){
       LocalDateTime localDateTime = LocalDateTime.of((int)(((datetime >> 25) & 127) + 1980), (int)((datetime >> 21) & 15), (int)((datetime >> 16) & 31), (int)((datetime >> 11) & 31), (int)((datetime >> 5) & 63), (int)((datetime << 1) & 62));
       return (localDateTime.toEpochSecond(ZoneId.systemDefault().getRules().getOffset(localDateTime)) * 1000);
    }
    public static CentralDirectoryFileHeader fromRandomAccessData(RandomAccessData data,int offset,JarEntryFilter filter){
       CentralDirectoryFileHeader fileHeader = new CentralDirectoryFileHeader();
       byte[] bytes = data.read((long)offset, 46);
       fileHeader.load(bytes, 0, data, offset, filter);
       return fileHeader;
    }
    public Object clone(){
       return this.clone();
    }
    public CentralDirectoryFileHeader clone(){
       byte[] header = new byte[46];
       System.arraycopy(this.header, this.headerOffset, header, 0, header.length);
       return new CentralDirectoryFileHeader(header, 0, this.name, header, this.comment, this.localHeaderOffset);
    }
    public AsciiBytes getComment(){
       return this.comment;
    }
    public long getCompressedSize(){
       return Bytes.littleEndianValue(this.header, (this.headerOffset + 20), 4);
    }
    public long getCrc(){
       return Bytes.littleEndianValue(this.header, (this.headerOffset + 16), 4);
    }
    public byte[] getExtra(){
       return this.extra;
    }
    public long getLocalHeaderOffset(){
       return this.localHeaderOffset;
    }
    public int getMethod(){
       return (int)Bytes.littleEndianValue(this.header, (this.headerOffset + 10), 2);
    }
    public AsciiBytes getName(){
       return this.name;
    }
    public long getSize(){
       return Bytes.littleEndianValue(this.header, (this.headerOffset + 24), 4);
    }
    public long getTime(){
       long datetime = Bytes.littleEndianValue(this.header, (this.headerOffset + 12), 4);
       return this.decodeMsDosFormatDateTime(datetime);
    }
    public boolean hasName(CharSequence name,char suffix){
       return this.name.matches(name, suffix);
    }
    public boolean isDirectory(){
       return this.name.endsWith(CentralDirectoryFileHeader.SLASH);
    }
    void load(byte[] data,int dataOffset,RandomAccessData variableData,int variableOffset,JarEntryFilter filter){
       this.header = data;
       this.headerOffset = dataOffset;
       long nameLength = Bytes.littleEndianValue(data, (dataOffset + 28), 2);
       long extraLength = Bytes.littleEndianValue(data, (dataOffset + 30), 2);
       long commentLength = Bytes.littleEndianValue(data, (dataOffset + 32), 2);
       this.localHeaderOffset = Bytes.littleEndianValue(data, (dataOffset + 42), 4);
       dataOffset = dataOffset + 46;
       if (variableData != null) {
          data = variableData.read((long)(variableOffset + 46), ((nameLength + extraLength) + commentLength));
          dataOffset = 0;
       }
       this.name = new AsciiBytes(data, dataOffset, (int)nameLength);
       if (filter != null) {
          this.name = filter.apply(this.name);
       }
       this.extra = CentralDirectoryFileHeader.NO_EXTRA;
       this.comment = CentralDirectoryFileHeader.NO_COMMENT;
       if ((extraLength) > 0) {
          byte[] uobyteArray = new byte[(int)extraLength];
          this.extra = uobyteArray;
          System.arraycopy(data, (int)((long)dataOffset + nameLength), this.extra, 0, this.extra.length);
       }
       if ((commentLength) > 0) {
          this.comment = new AsciiBytes(data, (int)(((long)dataOffset + nameLength) + extraLength), (int)commentLength);
       }
       return;
    }
}
