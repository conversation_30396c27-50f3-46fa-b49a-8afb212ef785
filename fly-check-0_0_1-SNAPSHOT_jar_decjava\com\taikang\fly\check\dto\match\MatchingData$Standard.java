package com.taikang.fly.check.dto.match.MatchingData$Standard;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MatchingData$Standard	// class@00018a from classes.dex
{
    private String isMatch;
    private String stanColumn;
    private String stanColumnName;
    private String tabName;

    public void MatchingData$Standard(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MatchingData$Standard;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MatchingData$Standard) {
             b = false;
          }else {
             MatchingData$Standard standard = o;
             if (!standard.canEqual(this)) {
                b = false;
             }else {
                String stanColumn = this.getStanColumn();
                String stanColumn1 = standard.getStanColumn();
                if (stanColumn == null) {
                   if (stanColumn1 != null) {
                      b = false;
                   }
                }else if(stanColumn.equals(stanColumn1)){
                }
                String stanColumnNa = this.getStanColumnName();
                String stanColumnNa1 = standard.getStanColumnName();
                if (stanColumnNa == null) {
                   if (stanColumnNa1 != null) {
                      b = false;
                   }
                }else if(stanColumnNa.equals(stanColumnNa1)){
                }
                String isMatch = this.getIsMatch();
                String isMatch1 = standard.getIsMatch();
                if (isMatch == null) {
                   if (isMatch1 != null) {
                      b = false;
                   }
                }else if(isMatch.equals(isMatch1)){
                }
                String tabName = this.getTabName();
                String tabName1 = standard.getTabName();
                if (tabName == null) {
                   if (tabName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!tabName.equals(tabName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getIsMatch(){
       return this.isMatch;
    }
    public String getStanColumn(){
       return this.stanColumn;
    }
    public String getStanColumnName(){
       return this.stanColumnName;
    }
    public String getTabName(){
       return this.tabName;
    }
    public int hashCode(){
       String $stanColumn;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($stanColumn = this.getStanColumn()) == null)? i: $stanColumn.hashCode();
       result = i1 + 59;
       String $stanColumnName = this.getStanColumnName();
       int i2 = result * 59;
       i1 = ($stanColumnName == null)? i: $stanColumnName.hashCode();
       result = i2 + i1;
       String $isMatch = this.getIsMatch();
       i2 = result * 59;
       i1 = ($isMatch == null)? i: $isMatch.hashCode();
       result = i2 + i1;
       String $tabName = this.getTabName();
       i1 = result * 59;
       if ($tabName != null) {
          i = $tabName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setIsMatch(String isMatch){
       this.isMatch = isMatch;
    }
    public void setStanColumn(String stanColumn){
       this.stanColumn = stanColumn;
    }
    public void setStanColumnName(String stanColumnName){
       this.stanColumnName = stanColumnName;
    }
    public void setTabName(String tabName){
       this.tabName = tabName;
    }
    public String toString(){
       return "MatchingData.Standard\(stanColumn="+this.getStanColumn()+", stanColumnName="+this.getStanColumnName()+", isMatch="+this.getIsMatch()+", tabName="+this.getTabName()+"\)";
    }
}
