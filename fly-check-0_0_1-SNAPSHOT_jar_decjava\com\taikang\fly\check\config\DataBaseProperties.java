package com.taikang.fly.check.config.DataBaseProperties;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;
import java.io.File;

public class DataBaseProperties	// class@000089 from classes.dex
{
    private String csvDir;
    private String dmpDir;
    private String dpDir;
    private String mssqlDir;
    private String password;
    private String serverName;
    private String tablespace;
    private String tempDir;
    private String tempTablespace;
    private String uploadFileDir;
    private String url;
    private String userName;

    public void DataBaseProperties(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataBaseProperties;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DataBaseProperties){
          b = false;
       }else {
          DataBaseProperties uDataBasePro = o;
          if (!uDataBasePro.canEqual(this)) {
             b = false;
          }else {
             String url = this.getUrl();
             String url1 = uDataBasePro.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String serverName = this.getServerName();
             String serverName1 = uDataBasePro.getServerName();
             if (serverName == null) {
                if (serverName1 != null) {
                   b = false;
                }
             }else if(serverName.equals(serverName1)){
             }
             String tablespace = this.getTablespace();
             String tablespace1 = uDataBasePro.getTablespace();
             if (tablespace == null) {
                if (tablespace1 != null) {
                   b = false;
                }
             }else if(tablespace.equals(tablespace1)){
             }
             String tempTablespa = this.getTempTablespace();
             String tempTablespa1 = uDataBasePro.getTempTablespace();
             if (tempTablespa == null) {
                if (tempTablespa1 != null) {
                   b = false;
                }
             }else if(tempTablespa.equals(tempTablespa1)){
             }
             String userName = this.getUserName();
             String userName1 = uDataBasePro.getUserName();
             if (userName == null) {
                if (userName1 != null) {
                   b = false;
                }
             }else if(userName.equals(userName1)){
             }
             String password = this.getPassword();
             String password1 = uDataBasePro.getPassword();
             if (password == null) {
                if (password1 != null) {
                   b = false;
                }
             }else if(password.equals(password1)){
             }
             String uploadFileDi = this.getUploadFileDir();
             String uploadFileDi1 = uDataBasePro.getUploadFileDir();
             if (uploadFileDi == null) {
                if (uploadFileDi1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(uploadFileDi.equals(uploadFileDi1)){
             }
             String dmpDir = this.getDmpDir();
             String dmpDir1 = uDataBasePro.getDmpDir();
             if (dmpDir == null) {
                if (dmpDir1 != null) {
                   b = false;
                }
             }else if(dmpDir.equals(dmpDir1)){
             }
             String dpDir = this.getDpDir();
             String dpDir1 = uDataBasePro.getDpDir();
             if (dpDir == null) {
                if (dpDir1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(dpDir.equals(dpDir1)){
             }
             String csvDir = this.getCsvDir();
             String csvDir1 = uDataBasePro.getCsvDir();
             if (csvDir == null) {
                if (csvDir1 != null) {
                   b = false;
                }
             }else if(csvDir.equals(csvDir1)){
             }
             String mssqlDir = this.getMssqlDir();
             String mssqlDir1 = uDataBasePro.getMssqlDir();
             if (mssqlDir == null) {
                if (mssqlDir1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(mssqlDir.equals(mssqlDir1)){
             }
             String tempDir = this.getTempDir();
             String tempDir1 = uDataBasePro.getTempDir();
             if (tempDir == null) {
                if (tempDir1 != null) {
                   b = false;
                }
             }else if(tempDir.equals(tempDir1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCsvDir(){
       return this.csvDir;
    }
    public String getDmpDir(){
       return this.dmpDir;
    }
    public String getDpDir(){
       return this.dpDir;
    }
    public String getMssqlDir(){
       return this.mssqlDir;
    }
    public String getPassword(){
       return this.password;
    }
    public String getServerName(){
       return this.serverName;
    }
    public String getTablespace(){
       return this.tablespace;
    }
    public String getTempDir(){
       return this.tempDir;
    }
    public String getTempTablespace(){
       return this.tempTablespace;
    }
    public String getUploadFileDir(){
       return this.uploadFileDir;
    }
    public String getUrl(){
       return this.url;
    }
    public String getUserName(){
       return this.userName;
    }
    public int hashCode(){
       String $url;
       int PRIME = 59;
       int result = 1;
       int i = (($url = this.getUrl()) == null)? 43: $url.hashCode();
       result = i + 59;
       String $serverName = this.getServerName();
       int i1 = result * 59;
       i = ($serverName == null)? 43: $serverName.hashCode();
       result = i1 + i;
       String $tablespace = this.getTablespace();
       i1 = result * 59;
       i = ($tablespace == null)? 43: $tablespace.hashCode();
       result = i1 + i;
       String $tempTablespace = this.getTempTablespace();
       i1 = result * 59;
       i = ($tempTablespace == null)? 43: $tempTablespace.hashCode();
       result = i1 + i;
       String $userName = this.getUserName();
       i1 = result * 59;
       i = ($userName == null)? 43: $userName.hashCode();
       result = i1 + i;
       String password = this.getPassword();
       i1 = result * 59;
       i = (password == null)? 43: password.hashCode();
       String uploadFileDi = this.getUploadFileDir();
       i1 = (i1 + i) * 59;
       i = (uploadFileDi == null)? 43: uploadFileDi.hashCode();
       String dmpDir = this.getDmpDir();
       i1 = (i1 + i) * 59;
       i = (dmpDir == null)? 43: dmpDir.hashCode();
       String dpDir = this.getDpDir();
       i1 = (i1 + i) * 59;
       i = (dpDir == null)? 43: dpDir.hashCode();
       String csvDir = this.getCsvDir();
       i1 = (i1 + i) * 59;
       i = (csvDir == null)? 43: csvDir.hashCode();
       String mssqlDir = this.getMssqlDir();
       i1 = (i1 + i) * 59;
       i = (mssqlDir == null)? 43: mssqlDir.hashCode();
       String tempDir = this.getTempDir();
       i1 = (i1 + i) * 59;
       i = (tempDir == null)? 43: tempDir.hashCode();
       return (i1 + i);
    }
    public void setCsvDir(String csvDir){
       this.csvDir = csvDir;
    }
    public void setDir(String uploadFileDir){
       this.dmpDir = uploadFileDir+"dmp"+File.separator;
       this.dpDir = uploadFileDir+"dp"+File.separator;
       this.csvDir = uploadFileDir+"csv"+File.separator;
       this.mssqlDir = uploadFileDir+"mssql"+File.separator;
       this.tempDir = uploadFileDir+"temp"+File.separator;
       File dmpPath = new File(this.dmpDir);
       if (!dmpPath.exists()) {
          dmpPath.mkdirs();
       }
       File uFile = new File(this.dpDir);
       if (!uFile.exists()) {
          uFile.mkdirs();
       }
       File uFile1 = new File(this.csvDir);
       if (!uFile1.exists()) {
          uFile1.mkdirs();
       }
       File uFile2 = new File(this.mssqlDir);
       if (!uFile2.exists()) {
          uFile2.mkdirs();
       }
       File uFile3 = new File(this.tempDir);
       if (!uFile3.exists()) {
          uFile3.mkdirs();
       }
       return;
    }
    public void setDmpDir(String dmpDir){
       this.dmpDir = dmpDir;
    }
    public void setDpDir(String dpDir){
       this.dpDir = dpDir;
    }
    public void setMssqlDir(String mssqlDir){
       this.mssqlDir = mssqlDir;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setServerName(String serverName){
       this.serverName = serverName;
    }
    public void setTablespace(String tablespace){
       this.tablespace = tablespace;
    }
    public void setTempDir(String tempDir){
       this.tempDir = tempDir;
    }
    public void setTempTablespace(String tempTablespace){
       this.tempTablespace = tempTablespace;
    }
    public void setUploadFileDir(String uploadFileDir){
       this.uploadFileDir = uploadFileDir;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public void setUserName(String userName){
       this.userName = userName.toUpperCase();
    }
    public String toString(){
       return "DataBaseProperties\(url="+this.getUrl()+", serverName="+this.getServerName()+", tablespace="+this.getTablespace()+", tempTablespace="+this.getTempTablespace()+", userName="+this.getUserName()+", password="+this.getPassword()+", uploadFileDir="+this.getUploadFileDir()+", dmpDir="+this.getDmpDir()+", dpDir="+this.getDpDir()+", csvDir="+this.getCsvDir()+", mssqlDir="+this.getMssqlDir()+", tempDir="+this.getTempDir()+"\)";
    }
}
