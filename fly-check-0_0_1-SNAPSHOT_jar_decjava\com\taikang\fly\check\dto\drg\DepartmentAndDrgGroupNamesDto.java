package com.taikang.fly.check.dto.drg.DepartmentAndDrgGroupNamesDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DepartmentAndDrgGroupNamesDto implements Serializable	// class@0000e9 from classes.dex
{
    private String departmentName;
    private String drgGroupName;
    private String year;
    private static final long serialVersionUID = 0x1;

    public void DepartmentAndDrgGroupNamesDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DepartmentAndDrgGroupNamesDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DepartmentAndDrgGroupNamesDto) {
             b = false;
          }else {
             DepartmentAndDrgGroupNamesDto uDepartmentA = o;
             if (!uDepartmentA.canEqual(this)) {
                b = false;
             }else {
                String departmentNa = this.getDepartmentName();
                String departmentNa1 = uDepartmentA.getDepartmentName();
                if (departmentNa == null) {
                   if (departmentNa1 != null) {
                      b = false;
                   }
                }else if(departmentNa.equals(departmentNa1)){
                }
                String drgGroupName = this.getDrgGroupName();
                String drgGroupName1 = uDepartmentA.getDrgGroupName();
                if (drgGroupName == null) {
                   if (drgGroupName1 != null) {
                      b = false;
                   }
                }else if(drgGroupName.equals(drgGroupName1)){
                }
                String year = this.getYear();
                String year1 = uDepartmentA.getYear();
                if (year == null) {
                   if (year1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!year.equals(year1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDepartmentName(){
       return this.departmentName;
    }
    public String getDrgGroupName(){
       return this.drgGroupName;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $departmentName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($departmentName = this.getDepartmentName()) == null)? i: $departmentName.hashCode();
       result = i1 + 59;
       String $drgGroupName = this.getDrgGroupName();
       int i2 = result * 59;
       i1 = ($drgGroupName == null)? i: $drgGroupName.hashCode();
       result = i2 + i1;
       String $year = this.getYear();
       i1 = result * 59;
       if ($year != null) {
          i = $year.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDepartmentName(String departmentName){
       this.departmentName = departmentName;
    }
    public void setDrgGroupName(String drgGroupName){
       this.drgGroupName = drgGroupName;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DepartmentAndDrgGroupNamesDto\(departmentName="+this.getDepartmentName()+", drgGroupName="+this.getDrgGroupName()+", year="+this.getYear()+"\)";
    }
}
