package com.taikang.fly.check.dto.exportReport.DrgSPTExportEntry;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgSPTExportEntry	// class@0000f1 from classes.dex
{
    private Integer drgCount;
    private Integer drgSScodeCount;
    private Integer drgZDcodeCount;
    private Integer notRelatedMI;
    private Integer notRelatedSI;
    private Integer seltIdCount;
    private Integer ssICode;
    private Integer ssZCode;
    private Integer zdICode;
    private Integer zdZCode;

    public void DrgSPTExportEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgSPTExportEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgSPTExportEntry){
          b = false;
       }else {
          DrgSPTExportEntry uDrgSPTExpor = o;
          if (!uDrgSPTExpor.canEqual(this)) {
             b = false;
          }else {
             Integer drgCount = this.getDrgCount();
             Integer drgCount1 = uDrgSPTExpor.getDrgCount();
             if (drgCount == null) {
                if (drgCount1 != null) {
                   b = false;
                }
             }else if(drgCount.equals(drgCount1)){
             }
             Integer seltIdCount = this.getSeltIdCount();
             Integer seltIdCount1 = uDrgSPTExpor.getSeltIdCount();
             if (seltIdCount == null) {
                if (seltIdCount1 != null) {
                   b = false;
                }
             }else if(seltIdCount.equals(seltIdCount1)){
             }
             Integer zdZCode = this.getZdZCode();
             Integer zdZCode1 = uDrgSPTExpor.getZdZCode();
             if (zdZCode == null) {
                if (zdZCode1 != null) {
                   b = false;
                }
             }else if(zdZCode.equals(zdZCode1)){
             }
             Integer zdICode = this.getZdICode();
             Integer zdICode1 = uDrgSPTExpor.getZdICode();
             if (zdICode == null) {
                if (zdICode1 != null) {
                   b = false;
                }
             }else if(zdICode.equals(zdICode1)){
             }
             Integer ssZCode = this.getSsZCode();
             Integer ssZCode1 = uDrgSPTExpor.getSsZCode();
             if (ssZCode == null) {
                if (ssZCode1 != null) {
                   b = false;
                }
             }else if(ssZCode.equals(ssZCode1)){
             }
             Integer ssICode = this.getSsICode();
             Integer ssICode1 = uDrgSPTExpor.getSsICode();
             if (ssICode == null) {
                if (ssICode1 != null) {
                   b = false;
                }
             }else if(ssICode.equals(ssICode1)){
             }
             Integer notRelatedSI = this.getNotRelatedSI();
             Integer notRelatedSI1 = uDrgSPTExpor.getNotRelatedSI();
             if (notRelatedSI == null) {
                if (notRelatedSI1 != null) {
                   b = false;
                }
             }else if(notRelatedSI.equals(notRelatedSI1)){
             }
             Integer notRelatedMI = this.getNotRelatedMI();
             Integer notRelatedMI1 = uDrgSPTExpor.getNotRelatedMI();
             if (notRelatedMI == null) {
                if (notRelatedMI1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(notRelatedMI.equals(notRelatedMI1)){
             }
             Integer drgZDcodeCou = this.getDrgZDcodeCount();
             Integer drgZDcodeCou1 = uDrgSPTExpor.getDrgZDcodeCount();
             if (drgZDcodeCou == null) {
                if (drgZDcodeCou1 != null) {
                   b = false;
                }
             }else if(drgZDcodeCou.equals(drgZDcodeCou1)){
             }
             Integer drgSScodeCou = this.getDrgSScodeCount();
             Integer drgSScodeCou1 = uDrgSPTExpor.getDrgSScodeCount();
             if (drgSScodeCou == null) {
                if (drgSScodeCou1 != null) {
                label_00ff :
                   b = false;
                }
             }else if(drgSScodeCou.equals(drgSScodeCou1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Integer getDrgCount(){
       return this.drgCount;
    }
    public Integer getDrgSScodeCount(){
       return this.drgSScodeCount;
    }
    public Integer getDrgZDcodeCount(){
       return this.drgZDcodeCount;
    }
    public Integer getNotRelatedMI(){
       return this.notRelatedMI;
    }
    public Integer getNotRelatedSI(){
       return this.notRelatedSI;
    }
    public Integer getSeltIdCount(){
       return this.seltIdCount;
    }
    public Integer getSsICode(){
       return this.ssICode;
    }
    public Integer getSsZCode(){
       return this.ssZCode;
    }
    public Integer getZdICode(){
       return this.zdICode;
    }
    public Integer getZdZCode(){
       return this.zdZCode;
    }
    public int hashCode(){
       Integer $drgCount;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgCount = this.getDrgCount()) == null)? i: $drgCount.hashCode();
       result = i1 + 59;
       Integer $seltIdCount = this.getSeltIdCount();
       int i2 = result * 59;
       i1 = ($seltIdCount == null)? i: $seltIdCount.hashCode();
       result = i2 + i1;
       Integer $zdZCode = this.getZdZCode();
       i2 = result * 59;
       i1 = ($zdZCode == null)? i: $zdZCode.hashCode();
       result = i2 + i1;
       Integer $zdICode = this.getZdICode();
       i2 = result * 59;
       i1 = ($zdICode == null)? i: $zdICode.hashCode();
       result = i2 + i1;
       Integer $ssZCode = this.getSsZCode();
       i2 = result * 59;
       i1 = ($ssZCode == null)? i: $ssZCode.hashCode();
       result = i2 + i1;
       Integer ssICode = this.getSsICode();
       i2 = result * 59;
       i1 = (ssICode == null)? i: ssICode.hashCode();
       Integer notRelatedSI = this.getNotRelatedSI();
       i2 = (i2 + i1) * 59;
       i1 = (notRelatedSI == null)? i: notRelatedSI.hashCode();
       Integer notRelatedMI = this.getNotRelatedMI();
       i2 = (i2 + i1) * 59;
       i1 = (notRelatedMI == null)? i: notRelatedMI.hashCode();
       Integer drgZDcodeCou = this.getDrgZDcodeCount();
       i2 = (i2 + i1) * 59;
       i1 = (drgZDcodeCou == null)? i: drgZDcodeCou.hashCode();
       Integer drgSScodeCou = this.getDrgSScodeCount();
       i1 = (i2 + i1) * 59;
       if (drgSScodeCou != null) {
          i = drgSScodeCou.hashCode();
       }
       return (i1 + i);
    }
    public void setDrgCount(Integer drgCount){
       this.drgCount = drgCount;
    }
    public void setDrgSScodeCount(Integer drgSScodeCount){
       this.drgSScodeCount = drgSScodeCount;
    }
    public void setDrgZDcodeCount(Integer drgZDcodeCount){
       this.drgZDcodeCount = drgZDcodeCount;
    }
    public void setNotRelatedMI(Integer notRelatedMI){
       this.notRelatedMI = notRelatedMI;
    }
    public void setNotRelatedSI(Integer notRelatedSI){
       this.notRelatedSI = notRelatedSI;
    }
    public void setSeltIdCount(Integer seltIdCount){
       this.seltIdCount = seltIdCount;
    }
    public void setSsICode(Integer ssICode){
       this.ssICode = ssICode;
    }
    public void setSsZCode(Integer ssZCode){
       this.ssZCode = ssZCode;
    }
    public void setZdICode(Integer zdICode){
       this.zdICode = zdICode;
    }
    public void setZdZCode(Integer zdZCode){
       this.zdZCode = zdZCode;
    }
    public String toString(){
       return "DrgSPTExportEntry\(drgCount="+this.getDrgCount()+", seltIdCount="+this.getSeltIdCount()+", zdZCode="+this.getZdZCode()+", zdICode="+this.getZdICode()+", ssZCode="+this.getSsZCode()+", ssICode="+this.getSsICode()+", notRelatedSI="+this.getNotRelatedSI()+", notRelatedMI="+this.getNotRelatedMI()+", drgZDcodeCount="+this.getDrgZDcodeCount()+", drgSScodeCount="+this.getDrgSScodeCount()+"\)";
    }
}
