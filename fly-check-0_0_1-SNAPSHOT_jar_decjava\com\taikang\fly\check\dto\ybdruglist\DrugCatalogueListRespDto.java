package com.taikang.fly.check.dto.ybdruglist.DrugCatalogueListRespDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrugCatalogueListRespDto	// class@0001d4 from classes.dex
{
    private String dosageForm;
    private String drugsCode;
    private String drugsName;
    private String endTime;
    private String hospitalSelfPay;
    private String leavePrice;
    private String maxPrice;
    private String oneMaxPrice;
    private String outpatientSelfPay;
    private String payer;
    private String paymentCategory;
    private String remark;
    private String startTime;
    private String threeMaxPrice;
    private String twoMaxPrice;
    private String workInjurySelfPay;
    private String yourSelfPay;

    public void DrugCatalogueListRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrugCatalogueListRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrugCatalogueListRespDto){
          b = false;
       }else {
          DrugCatalogueListRespDto uDrugCatalog = o;
          if (!uDrugCatalog.canEqual(this)) {
             b = false;
          }else {
             String drugsCode = this.getDrugsCode();
             String drugsCode1 = uDrugCatalog.getDrugsCode();
             if (drugsCode == null) {
                if (drugsCode1 != null) {
                   b = false;
                }
             }else if(drugsCode.equals(drugsCode1)){
             }
             String drugsName = this.getDrugsName();
             String drugsName1 = uDrugCatalog.getDrugsName();
             if (drugsName == null) {
                if (drugsName1 != null) {
                   b = false;
                }
             }else if(drugsName.equals(drugsName1)){
             }
             String dosageForm = this.getDosageForm();
             String dosageForm1 = uDrugCatalog.getDosageForm();
             if (dosageForm == null) {
                if (dosageForm1 != null) {
                   b = false;
                }
             }else if(dosageForm.equals(dosageForm1)){
             }
             String payer = this.getPayer();
             String payer1 = uDrugCatalog.getPayer();
             if (payer == null) {
                if (payer1 != null) {
                   b = false;
                }
             }else if(payer.equals(payer1)){
             }
             String paymentCateg = this.getPaymentCategory();
             String paymentCateg1 = uDrugCatalog.getPaymentCategory();
             if (paymentCateg == null) {
                if (paymentCateg1 != null) {
                   b = false;
                }
             }else if(paymentCateg.equals(paymentCateg1)){
             }
             String remark = this.getRemark();
             String remark1 = uDrugCatalog.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             String maxPrice = this.getMaxPrice();
             String maxPrice1 = uDrugCatalog.getMaxPrice();
             if (maxPrice == null) {
                if (maxPrice1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(maxPrice.equals(maxPrice1)){
             }
             String threeMaxPric = this.getThreeMaxPrice();
             String threeMaxPric1 = uDrugCatalog.getThreeMaxPrice();
             if (threeMaxPric == null) {
                if (threeMaxPric1 != null) {
                   b = false;
                }
             }else if(threeMaxPric.equals(threeMaxPric1)){
             }
             String twoMaxPrice = this.getTwoMaxPrice();
             String twoMaxPrice1 = uDrugCatalog.getTwoMaxPrice();
             if (twoMaxPrice == null) {
                if (twoMaxPrice1 != null) {
                label_00ed :
                   b = false;
                }
             }else if(twoMaxPrice.equals(twoMaxPrice1)){
             }
             String oneMaxPrice = this.getOneMaxPrice();
             String oneMaxPrice1 = uDrugCatalog.getOneMaxPrice();
             if (oneMaxPrice == null) {
                if (oneMaxPrice1 != null) {
                   b = false;
                }
             }else if(oneMaxPrice.equals(oneMaxPrice1)){
             }
             String leavePrice = this.getLeavePrice();
             String leavePrice1 = uDrugCatalog.getLeavePrice();
             if (leavePrice == null) {
                if (leavePrice1 != null) {
                label_011f :
                   b = false;
                }
             }else if(leavePrice.equals(leavePrice1)){
             }
             String outpatientSe = this.getOutpatientSelfPay();
             String outpatientSe1 = uDrugCatalog.getOutpatientSelfPay();
             if (outpatientSe == null) {
                if (outpatientSe1 != null) {
                   b = false;
                }
             }else if(outpatientSe.equals(outpatientSe1)){
             }
             String hospitalSelf = this.getHospitalSelfPay();
             String hospitalSelf1 = uDrugCatalog.getHospitalSelfPay();
             if (hospitalSelf == null) {
                if (hospitalSelf1 != null) {
                   b = false;
                }
             }else if(hospitalSelf.equals(hospitalSelf1)){
             }
             String workInjurySe = this.getWorkInjurySelfPay();
             String workInjurySe1 = uDrugCatalog.getWorkInjurySelfPay();
             if (workInjurySe == null) {
                if (workInjurySe1 != null) {
                label_0167 :
                   b = false;
                }
             }else if(workInjurySe.equals(workInjurySe1)){
             }
             String yourSelfPay = this.getYourSelfPay();
             String yourSelfPay1 = uDrugCatalog.getYourSelfPay();
             if (yourSelfPay == null) {
                if (yourSelfPay1 != null) {
                   b = false;
                }
             }else if(yourSelfPay.equals(yourSelfPay1)){
             }
             String startTime = this.getStartTime();
             String startTime1 = uDrugCatalog.getStartTime();
             if (startTime == null) {
                if (startTime1 != null) {
                label_019b :
                   b = false;
                }
             }else if(startTime.equals(startTime1)){
             }
             String endTime = this.getEndTime();
             String endTime1 = uDrugCatalog.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDosageForm(){
       return this.dosageForm;
    }
    public String getDrugsCode(){
       return this.drugsCode;
    }
    public String getDrugsName(){
       return this.drugsName;
    }
    public String getEndTime(){
       return this.endTime;
    }
    public String getHospitalSelfPay(){
       return this.hospitalSelfPay;
    }
    public String getLeavePrice(){
       return this.leavePrice;
    }
    public String getMaxPrice(){
       return this.maxPrice;
    }
    public String getOneMaxPrice(){
       return this.oneMaxPrice;
    }
    public String getOutpatientSelfPay(){
       return this.outpatientSelfPay;
    }
    public String getPayer(){
       return this.payer;
    }
    public String getPaymentCategory(){
       return this.paymentCategory;
    }
    public String getRemark(){
       return this.remark;
    }
    public String getStartTime(){
       return this.startTime;
    }
    public String getThreeMaxPrice(){
       return this.threeMaxPrice;
    }
    public String getTwoMaxPrice(){
       return this.twoMaxPrice;
    }
    public String getWorkInjurySelfPay(){
       return this.workInjurySelfPay;
    }
    public String getYourSelfPay(){
       return this.yourSelfPay;
    }
    public int hashCode(){
       String $drugsCode;
       int PRIME = 59;
       int result = 1;
       int i = (($drugsCode = this.getDrugsCode()) == null)? 43: $drugsCode.hashCode();
       result = i + 59;
       String $drugsName = this.getDrugsName();
       int i1 = result * 59;
       i = ($drugsName == null)? 43: $drugsName.hashCode();
       result = i1 + i;
       String $dosageForm = this.getDosageForm();
       i1 = result * 59;
       i = ($dosageForm == null)? 43: $dosageForm.hashCode();
       result = i1 + i;
       String $payer = this.getPayer();
       i1 = result * 59;
       i = ($payer == null)? 43: $payer.hashCode();
       result = i1 + i;
       String $paymentCategory = this.getPaymentCategory();
       i1 = result * 59;
       i = ($paymentCategory == null)? 43: $paymentCategory.hashCode();
       result = i1 + i;
       String remark = this.getRemark();
       i1 = result * 59;
       i = (remark == null)? 43: remark.hashCode();
       String maxPrice = this.getMaxPrice();
       i1 = (i1 + i) * 59;
       i = (maxPrice == null)? 43: maxPrice.hashCode();
       String threeMaxPric = this.getThreeMaxPrice();
       i1 = (i1 + i) * 59;
       i = (threeMaxPric == null)? 43: threeMaxPric.hashCode();
       String twoMaxPrice = this.getTwoMaxPrice();
       i1 = (i1 + i) * 59;
       i = (twoMaxPrice == null)? 43: twoMaxPrice.hashCode();
       String oneMaxPrice = this.getOneMaxPrice();
       i1 = (i1 + i) * 59;
       i = (oneMaxPrice == null)? 43: oneMaxPrice.hashCode();
       String leavePrice = this.getLeavePrice();
       i1 = (i1 + i) * 59;
       i = (leavePrice == null)? 43: leavePrice.hashCode();
       String outpatientSe = this.getOutpatientSelfPay();
       i1 = (i1 + i) * 59;
       i = (outpatientSe == null)? 43: outpatientSe.hashCode();
       String hospitalSelf = this.getHospitalSelfPay();
       i1 = (i1 + i) * 59;
       i = (hospitalSelf == null)? 43: hospitalSelf.hashCode();
       String workInjurySe = this.getWorkInjurySelfPay();
       i1 = (i1 + i) * 59;
       i = (workInjurySe == null)? 43: workInjurySe.hashCode();
       String yourSelfPay = this.getYourSelfPay();
       i1 = (i1 + i) * 59;
       i = (yourSelfPay == null)? 43: yourSelfPay.hashCode();
       String startTime = this.getStartTime();
       i1 = (i1 + i) * 59;
       i = (startTime == null)? 43: startTime.hashCode();
       String endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       return (i1 + i);
    }
    public void setDosageForm(String dosageForm){
       this.dosageForm = dosageForm;
    }
    public void setDrugsCode(String drugsCode){
       this.drugsCode = drugsCode;
    }
    public void setDrugsName(String drugsName){
       this.drugsName = drugsName;
    }
    public void setEndTime(String endTime){
       this.endTime = endTime;
    }
    public void setHospitalSelfPay(String hospitalSelfPay){
       this.hospitalSelfPay = hospitalSelfPay;
    }
    public void setLeavePrice(String leavePrice){
       this.leavePrice = leavePrice;
    }
    public void setMaxPrice(String maxPrice){
       this.maxPrice = maxPrice;
    }
    public void setOneMaxPrice(String oneMaxPrice){
       this.oneMaxPrice = oneMaxPrice;
    }
    public void setOutpatientSelfPay(String outpatientSelfPay){
       this.outpatientSelfPay = outpatientSelfPay;
    }
    public void setPayer(String payer){
       this.payer = payer;
    }
    public void setPaymentCategory(String paymentCategory){
       this.paymentCategory = paymentCategory;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public void setStartTime(String startTime){
       this.startTime = startTime;
    }
    public void setThreeMaxPrice(String threeMaxPrice){
       this.threeMaxPrice = threeMaxPrice;
    }
    public void setTwoMaxPrice(String twoMaxPrice){
       this.twoMaxPrice = twoMaxPrice;
    }
    public void setWorkInjurySelfPay(String workInjurySelfPay){
       this.workInjurySelfPay = workInjurySelfPay;
    }
    public void setYourSelfPay(String yourSelfPay){
       this.yourSelfPay = yourSelfPay;
    }
    public String toString(){
       return "DrugCatalogueListRespDto\(drugsCode="+this.getDrugsCode()+", drugsName="+this.getDrugsName()+", dosageForm="+this.getDosageForm()+", payer="+this.getPayer()+", paymentCategory="+this.getPaymentCategory()+", remark="+this.getRemark()+", maxPrice="+this.getMaxPrice()+", threeMaxPrice="+this.getThreeMaxPrice()+", twoMaxPrice="+this.getTwoMaxPrice()+", oneMaxPrice="+this.getOneMaxPrice()+", leavePrice="+this.getLeavePrice()+", outpatientSelfPay="+this.getOutpatientSelfPay()+", hospitalSelfPay="+this.getHospitalSelfPay()+", workInjurySelfPay="+this.getWorkInjurySelfPay()+", yourSelfPay="+this.getYourSelfPay()+", startTime="+this.getStartTime()+", endTime="+this.getEndTime()+"\)";
    }
}
