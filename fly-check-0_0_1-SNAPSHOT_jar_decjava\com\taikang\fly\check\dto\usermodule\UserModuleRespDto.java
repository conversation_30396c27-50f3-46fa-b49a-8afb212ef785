package com.taikang.fly.check.dto.usermodule.UserModuleRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserModuleRespDto implements Serializable	// class@0001c6 from classes.dex
{
    private String createTime;
    private String creator;
    private String id;
    private String isValid;
    private String modby;
    private String modifyTime;
    private String moduleCode;
    private String signature;
    private String userCode;
    private static final long serialVersionUID = 0x349d06aa3e3fa7be;

    public void UserModuleRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserModuleRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof UserModuleRespDto){
          b = false;
       }else {
          UserModuleRespDto userModuleRe = o;
          if (!userModuleRe.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = userModuleRe.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String userCode = this.getUserCode();
             String userCode1 = userModuleRe.getUserCode();
             if (userCode == null) {
                if (userCode1 != null) {
                   b = false;
                }
             }else if(userCode.equals(userCode1)){
             }
             String moduleCode = this.getModuleCode();
             String moduleCode1 = userModuleRe.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String creator = this.getCreator();
             String creator1 = userModuleRe.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = userModuleRe.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = userModuleRe.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_009d :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = userModuleRe.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = userModuleRe.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = userModuleRe.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $userCode = this.getUserCode();
       int i2 = result * 59;
       i1 = ($userCode == null)? i: $userCode.hashCode();
       result = i2 + i1;
       String $moduleCode = this.getModuleCode();
       i2 = result * 59;
       i1 = ($moduleCode == null)? i: $moduleCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i2 = (i2 + i1) * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String isValid = this.getIsValid();
       i1 = (i2 + i1) * 59;
       if (isValid != null) {
          i = isValid.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserModuleRespDto\(id="+this.getId()+", userCode="+this.getUserCode()+", moduleCode="+this.getModuleCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", isValid="+this.getIsValid()+"\)";
    }
}
