"""
检查计划Pydantic模型
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class PlanBase(BaseModel):
    """计划基础模型"""
    plan_name: str = Field(..., description="计划名称")
    plan_description: Optional[str] = Field(None, description="计划描述")
    plan_type: Optional[str] = Field(None, description="计划类型")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    data_source: Optional[str] = Field(None, description="数据源")
    data_range: Optional[str] = Field(None, description="数据范围")
    execution_config: Optional[Dict[str, Any]] = Field(None, description="执行配置")


class PlanCreate(PlanBase):
    """创建计划模型"""
    rule_ids: Optional[List[str]] = Field(None, description="关联的规则ID列表")


class PlanUpdate(BaseModel):
    """更新计划模型"""
    plan_name: Optional[str] = None
    plan_description: Optional[str] = None
    plan_type: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    data_source: Optional[str] = None
    data_range: Optional[str] = None
    execution_config: Optional[Dict[str, Any]] = None
    status: Optional[str] = None


class PlanResponse(PlanBase):
    """计划响应模型"""
    id: str
    status: str
    total_rules: int
    completed_rules: int
    error_rules: int
    created_time: datetime
    updated_time: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    
    class Config:
        from_attributes = True


class PlanLogResponse(BaseModel):
    """计划日志响应模型"""
    id: str
    plan_id: str
    rule_id: Optional[str] = None
    execution_start: Optional[datetime] = None
    execution_end: Optional[datetime] = None
    execution_status: Optional[str] = None
    total_records: int
    error_records: int
    warning_records: int
    execution_detail: Optional[str] = None
    error_message: Optional[str] = None
    result_file_path: Optional[str] = None
    created_time: datetime
    
    class Config:
        from_attributes = True
