package com.taikang.fly.check.service.ProvincialPlatformService;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import java.lang.String;

public interface abstract ProvincialPlatformService implements IService	// class@0002f8 from classes.dex
{

    ArrayList exeSql(List p0);
    void exportReport(HttpServletResponse p0);
    List getList(String p0);
    List getTableName();
}
