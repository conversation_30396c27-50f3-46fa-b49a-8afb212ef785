package com.taikang.fly.check.vo.drg.DrgDateOfDepartmentVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgDateOfDepartmentVo	// class@000364 from classes.dex
{
    private String adrgCode;
    private String drgCode;
    private String drgNum;
    private String year;

    public void DrgDateOfDepartmentVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgDateOfDepartmentVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgDateOfDepartmentVo) {
             b = false;
          }else {
             DrgDateOfDepartmentVo uDrgDateOfDe = o;
             if (!uDrgDateOfDe.canEqual(this)) {
                b = false;
             }else {
                String year = this.getYear();
                String year1 = uDrgDateOfDe.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String drgNum = this.getDrgNum();
                String drgNum1 = uDrgDateOfDe.getDrgNum();
                if (drgNum == null) {
                   if (drgNum1 != null) {
                      b = false;
                   }
                }else if(drgNum.equals(drgNum1)){
                }
                String drgCode = this.getDrgCode();
                String drgCode1 = uDrgDateOfDe.getDrgCode();
                if (drgCode == null) {
                   if (drgCode1 != null) {
                      b = false;
                   }
                }else if(drgCode.equals(drgCode1)){
                }
                String adrgCode = this.getAdrgCode();
                String adrgCode1 = uDrgDateOfDe.getAdrgCode();
                if (adrgCode == null) {
                   if (adrgCode1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!adrgCode.equals(adrgCode1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getAdrgCode(){
       return this.adrgCode;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getDrgNum(){
       return this.drgNum;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $year;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($year = this.getYear()) == null)? i: $year.hashCode();
       result = i1 + 59;
       String $drgNum = this.getDrgNum();
       int i2 = result * 59;
       i1 = ($drgNum == null)? i: $drgNum.hashCode();
       result = i2 + i1;
       String $drgCode = this.getDrgCode();
       i2 = result * 59;
       i1 = ($drgCode == null)? i: $drgCode.hashCode();
       result = i2 + i1;
       String $adrgCode = this.getAdrgCode();
       i1 = result * 59;
       if ($adrgCode != null) {
          i = $adrgCode.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setAdrgCode(String adrgCode){
       this.adrgCode = adrgCode;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setDrgNum(String drgNum){
       this.drgNum = drgNum;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DrgDateOfDepartmentVo\(year="+this.getYear()+", drgNum="+this.getDrgNum()+", drgCode="+this.getDrgCode()+", adrgCode="+this.getAdrgCode()+"\)";
    }
}
