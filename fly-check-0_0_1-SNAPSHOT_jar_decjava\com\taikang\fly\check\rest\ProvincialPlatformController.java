package com.taikang.fly.check.rest.ProvincialPlatformController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.util.List;
import com.taikang.fly.check.comm.CommResponse;
import java.util.ArrayList;
import com.taikang.fly.check.service.ProvincialPlatformService;
import java.lang.String;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.vo.ProvincialPlatformMatching;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;

public class ProvincialPlatformController	// class@0002a9 from classes.dex
{
    private ProvincialPlatformService provincialPlatformService;
    private static final Logger log;

    static {
       ProvincialPlatformController.log = LoggerFactory.getLogger(ProvincialPlatformController.class);
    }
    public void ProvincialPlatformController(){
       super();
    }
    public CommResponse exeSql(List idList){
       ArrayList list = this.provincialPlatformService.exeSql(idList);
       CommResponse uCommRespons = (list.size() > 0)? CommResponse.error("0", list.toString(), null): CommResponse.success();
       return uCommRespons;
    }
    public CommResponse getList(){
       return CommResponse.success(this.provincialPlatformService.getList("01"));
    }
    public void outReport(HttpServletResponse response){
       this.provincialPlatformService.exportReport(response);
    }
    public CommResponse rollbackSql(ProvincialPlatformMatching provincialPlatformMatching){
       boolean b;
       CommResponse uCommRespons = (b = this.provincialPlatformService.updateById(provincialPlatformMatching))? CommResponse.success(): CommResponse.error(ResponseCodeEnum.SAVE_ERROR);
       return uCommRespons;
    }
    public CommResponse updateSql(ProvincialPlatformMatching provincialPlatformMatching){
       boolean b;
       CommResponse uCommRespons = (b = this.provincialPlatformService.updateById(provincialPlatformMatching))? CommResponse.success(): CommResponse.error(ResponseCodeEnum.SAVE_ERROR);
       return uCommRespons;
    }
}
