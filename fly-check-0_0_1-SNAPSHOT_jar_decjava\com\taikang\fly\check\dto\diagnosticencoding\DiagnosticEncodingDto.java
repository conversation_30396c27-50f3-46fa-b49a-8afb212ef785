package com.taikang.fly.check.dto.diagnosticencoding.DiagnosticEncodingDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DiagnosticEncodingDto implements Serializable	// class@0000e2 from classes.dex
{
    private String createTime;
    private String creator;
    private String diagnosisCode;
    private String diagnosisName;
    private String id;
    private String modby;
    private String modifyTime;
    private static final long serialVersionUID = 0x1;

    public void DiagnosticEncodingDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DiagnosticEncodingDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DiagnosticEncodingDto){
          b = false;
       }else {
          DiagnosticEncodingDto uDiagnosticE = o;
          if (!uDiagnosticE.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDiagnosticE.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String diagnosisCod = this.getDiagnosisCode();
             String diagnosisCod1 = uDiagnosticE.getDiagnosisCode();
             if (diagnosisCod == null) {
                if (diagnosisCod1 != null) {
                   b = false;
                }
             }else if(diagnosisCod.equals(diagnosisCod1)){
             }
             String diagnosisNam = this.getDiagnosisName();
             String diagnosisNam1 = uDiagnosticE.getDiagnosisName();
             if (diagnosisNam == null) {
                if (diagnosisNam1 != null) {
                   b = false;
                }
             }else if(diagnosisNam.equals(diagnosisNam1)){
             }
             String creator = this.getCreator();
             String creator1 = uDiagnosticE.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = uDiagnosticE.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = uDiagnosticE.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = uDiagnosticE.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00ae :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDiagnosisCode(){
       return this.diagnosisCode;
    }
    public String getDiagnosisName(){
       return this.diagnosisName;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $diagnosisCode = this.getDiagnosisCode();
       int i2 = result * 59;
       i1 = ($diagnosisCode == null)? i: $diagnosisCode.hashCode();
       result = i2 + i1;
       String $diagnosisName = this.getDiagnosisName();
       i2 = result * 59;
       i1 = ($diagnosisName == null)? i: $diagnosisName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i2 + i1) * 59;
       if (modifyTime != null) {
          i = modifyTime.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDiagnosisCode(String diagnosisCode){
       this.diagnosisCode = diagnosisCode;
    }
    public void setDiagnosisName(String diagnosisName){
       this.diagnosisName = diagnosisName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public String toString(){
       return "DiagnosticEncodingDto\(id="+this.getId()+", diagnosisCode="+this.getDiagnosisCode()+", diagnosisName="+this.getDiagnosisName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
