package com.taikang.fly.check.dto.mapstruct.DrugCatalogueListMapping;
import com.taikang.fly.check.mybatis.domain.DrugCatalogue;
import com.taikang.fly.check.dto.ybdruglist.DrugCatalogueListRespDto;
import java.util.List;

public interface abstract DrugCatalogueListMapping	// class@00014e from classes.dex
{

    DrugCatalogueListRespDto entryToDtoList(DrugCatalogue p0);
    List entryToDtoList(List p0);
}
