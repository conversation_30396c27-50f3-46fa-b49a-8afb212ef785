package com.taikang.fly.check.dto.dictEntry.DictEntryDetailResultDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictEntryDetailResultDto implements Serializable	// class@0000e5 from classes.dex
{
    private String createTime;
    private String creator;
    private String dictCode;
    private String dictName;
    private String dictNec;
    private String dictType;
    private String dictTypeCode;
    private String dictTypeName;
    private String id;
    private String modby;
    private String modifyTime;
    private String sortNo;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a1;

    public void DictEntryDetailResultDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictEntryDetailResultDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DictEntryDetailResultDto){
          b = false;
       }else {
          DictEntryDetailResultDto uDictEntryDe = o;
          if (!uDictEntryDe.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDictEntryDe.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String dictTypeCode = this.getDictTypeCode();
             String dictTypeCode1 = uDictEntryDe.getDictTypeCode();
             if (dictTypeCode == null) {
                if (dictTypeCode1 != null) {
                   b = false;
                }
             }else if(dictTypeCode.equals(dictTypeCode1)){
             }
             String dictTypeName = this.getDictTypeName();
             String dictTypeName1 = uDictEntryDe.getDictTypeName();
             if (dictTypeName == null) {
                if (dictTypeName1 != null) {
                   b = false;
                }
             }else if(dictTypeName.equals(dictTypeName1)){
             }
             String dictCode = this.getDictCode();
             String dictCode1 = uDictEntryDe.getDictCode();
             if (dictCode == null) {
                if (dictCode1 != null) {
                   b = false;
                }
             }else if(dictCode.equals(dictCode1)){
             }
             String dictName = this.getDictName();
             String dictName1 = uDictEntryDe.getDictName();
             if (dictName == null) {
                if (dictName1 != null) {
                   b = false;
                }
             }else if(dictName.equals(dictName1)){
             }
             String sortNo = this.getSortNo();
             String sortNo1 = uDictEntryDe.getSortNo();
             if (sortNo == null) {
                if (sortNo1 != null) {
                   b = false;
                }
             }else if(sortNo.equals(sortNo1)){
             }
             String creator = this.getCreator();
             String creator1 = uDictEntryDe.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = uDictEntryDe.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = uDictEntryDe.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_00e9 :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = uDictEntryDe.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String dictType = this.getDictType();
             String dictType1 = uDictEntryDe.getDictType();
             if (dictType == null) {
                if (dictType1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(dictType.equals(dictType1)){
             }
             String dictNec = this.getDictNec();
             String dictNec1 = uDictEntryDe.getDictNec();
             if (dictNec == null) {
                if (dictNec1 != null) {
                   b = false;
                }
             }else if(dictNec.equals(dictNec1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDictCode(){
       return this.dictCode;
    }
    public String getDictName(){
       return this.dictName;
    }
    public String getDictNec(){
       return this.dictNec;
    }
    public String getDictType(){
       return this.dictType;
    }
    public String getDictTypeCode(){
       return this.dictTypeCode;
    }
    public String getDictTypeName(){
       return this.dictTypeName;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getSortNo(){
       return this.sortNo;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $dictTypeCode = this.getDictTypeCode();
       int i1 = result * 59;
       i = ($dictTypeCode == null)? 43: $dictTypeCode.hashCode();
       result = i1 + i;
       String $dictTypeName = this.getDictTypeName();
       i1 = result * 59;
       i = ($dictTypeName == null)? 43: $dictTypeName.hashCode();
       result = i1 + i;
       String $dictCode = this.getDictCode();
       i1 = result * 59;
       i = ($dictCode == null)? 43: $dictCode.hashCode();
       result = i1 + i;
       String $dictName = this.getDictName();
       i1 = result * 59;
       i = ($dictName == null)? 43: $dictName.hashCode();
       result = i1 + i;
       String sortNo = this.getSortNo();
       i1 = result * 59;
       i = (sortNo == null)? 43: sortNo.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       String createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String dictType = this.getDictType();
       i1 = (i1 + i) * 59;
       i = (dictType == null)? 43: dictType.hashCode();
       String dictNec = this.getDictNec();
       i1 = (i1 + i) * 59;
       i = (dictNec == null)? 43: dictNec.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDictCode(String dictCode){
       this.dictCode = dictCode;
    }
    public void setDictName(String dictName){
       this.dictName = dictName;
    }
    public void setDictNec(String dictNec){
       this.dictNec = dictNec;
    }
    public void setDictType(String dictType){
       this.dictType = dictType;
    }
    public void setDictTypeCode(String dictTypeCode){
       this.dictTypeCode = dictTypeCode;
    }
    public void setDictTypeName(String dictTypeName){
       this.dictTypeName = dictTypeName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setSortNo(String sortNo){
       this.sortNo = sortNo;
    }
    public String toString(){
       return "DictEntryDetailResultDto\(id="+this.getId()+", dictTypeCode="+this.getDictTypeCode()+", dictTypeName="+this.getDictTypeName()+", dictCode="+this.getDictCode()+", dictName="+this.getDictName()+", sortNo="+this.getSortNo()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", dictType="+this.getDictType()+", dictNec="+this.getDictNec()+"\)";
    }
}
