package com.taikang.fly.check.service.YbDrugInstructionsService;
import java.lang.Object;
import com.taikang.fly.check.dto.ybDrugInstructions.YbDrugInstructionsSearchDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.util.ObjectUtils;
import java.lang.String;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import java.lang.Integer;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.YbDrugInstructionsDao;
import com.taikang.fly.check.dto.mapstruct.YbDrugInstructionsMapping;

public class YbDrugInstructionsService	// class@00030e from classes.dex
{
    private YbDrugInstructionsDao ybDrugInstructionsDao;
    private YbDrugInstructionsMapping ybDrugInstructionsMapping;

    public void YbDrugInstructionsService(){
       super();
    }
    private QueryWrapper getQueryWrapper(YbDrugInstructionsSearchDto ybDrugInstructionsSearchDto){
       QueryWrapper queryWrapper = new QueryWrapper();
       if (!ObjectUtils.isEmpty(ybDrugInstructionsSearchDto)) {
          if (StringUtils.isNotBlank(ybDrugInstructionsSearchDto.getGenericName())) {
             queryWrapper.like("generic_name", ybDrugInstructionsSearchDto.getGenericName());
          }
          if (StringUtils.isNotBlank(ybDrugInstructionsSearchDto.getTradeName())) {
             queryWrapper.like("trade_name", ybDrugInstructionsSearchDto.getTradeName());
          }
          if (StringUtils.isNotBlank(ybDrugInstructionsSearchDto.getApplicable())) {
             queryWrapper.like("applicable", ybDrugInstructionsSearchDto.getApplicable());
          }
       }
       return queryWrapper;
    }
    public NativePage queryList(Integer page,Integer size,YbDrugInstructionsSearchDto yDrugInstructionsSearchDto){
       QueryWrapper queryWrapper = this.getQueryWrapper(yDrugInstructionsSearchDto);
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List ybDrugInstructions = this.ybDrugInstructionsDao.selectList(queryWrapper);
       List ybDrugInstructionsRespDtos = this.ybDrugInstructionsMapping.domainsToRespDtoList(ybDrugInstructions);
       NativePage pageDto = new NativePage(ybDrugInstructionsRespDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
