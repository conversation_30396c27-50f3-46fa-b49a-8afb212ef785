package org.springframework.boot.loader.PropertiesLauncher$PrefixMatchingArchiveFilter;
import org.springframework.boot.loader.archive.Archive$EntryFilter;
import java.lang.String;
import java.lang.Object;
import org.springframework.boot.loader.PropertiesLauncher$ArchiveEntryFilter;
import org.springframework.boot.loader.PropertiesLauncher$1;
import org.springframework.boot.loader.archive.Archive$Entry;

final class PropertiesLauncher$PrefixMatchingArchiveFilter implements Archive$EntryFilter	// class@000536 from classes.dex
{
    private final PropertiesLauncher$ArchiveEntryFilter filter;
    private final String prefix;

    private void PropertiesLauncher$PrefixMatchingArchiveFilter(String prefix){
       super();
       this.filter = new PropertiesLauncher$ArchiveEntryFilter(null);
       this.prefix = prefix;
    }
    void PropertiesLauncher$PrefixMatchingArchiveFilter(String x0,PropertiesLauncher$1 x1){
       super(x0);
    }
    public boolean matches(Archive$Entry entry){
       boolean b;
       if (entry.isDirectory()) {
          b = entry.getName().equals(this.prefix);
       }else if(entry.getName().startsWith(this.prefix) && this.filter.matches(entry)){
          b = true;
       }else {
          b = false;
       }
       return b;
    }
}
