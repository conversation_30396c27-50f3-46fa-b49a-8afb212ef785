package com.taikang.fly.check.mybatis.dao.FlyRuleHisMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.FlyRule;
import com.taikang.fly.check.mybatis.domain.FlyRuleHis;

public interface abstract FlyRuleHisMapper implements BaseMapper	// class@0001f2 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    List findFlyRuleHisPage();
    int insert(FlyRule p0);
    List seleceFlyHisList();
    List selectAll();
    FlyRuleHis selectByPrimaryKey(String p0);
    int updateByPrimaryKey(FlyRuleHis p0);
}
