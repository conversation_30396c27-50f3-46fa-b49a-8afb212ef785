package com.taikang.fly.check.dto.flyRule.FlyRuleStatisDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleStatisDto implements Serializable	// class@00010b from classes.dex
{
    private String operator;
    private String region;
    private String resultFalgTotal;
    private String ruleTotal;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleStatisDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleStatisDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleStatisDto) {
             b = false;
          }else {
             FlyRuleStatisDto uFlyRuleStat = o;
             if (!uFlyRuleStat.canEqual(this)) {
                b = false;
             }else {
                String region = this.getRegion();
                String region1 = uFlyRuleStat.getRegion();
                if (region == null) {
                   if (region1 != null) {
                      b = false;
                   }
                }else if(region.equals(region1)){
                }
                String operator = this.getOperator();
                String operator1 = uFlyRuleStat.getOperator();
                if (operator == null) {
                   if (operator1 != null) {
                      b = false;
                   }
                }else if(operator.equals(operator1)){
                }
                String ruleTotal = this.getRuleTotal();
                String ruleTotal1 = uFlyRuleStat.getRuleTotal();
                if (ruleTotal == null) {
                   if (ruleTotal1 != null) {
                      b = false;
                   }
                }else if(ruleTotal.equals(ruleTotal1)){
                }
                String resultFalgTo = this.getResultFalgTotal();
                String resultFalgTo1 = uFlyRuleStat.getResultFalgTotal();
                if (resultFalgTo == null) {
                   if (resultFalgTo1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!resultFalgTo.equals(resultFalgTo1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getOperator(){
       return this.operator;
    }
    public String getRegion(){
       return this.region;
    }
    public String getResultFalgTotal(){
       return this.resultFalgTotal;
    }
    public String getRuleTotal(){
       return this.ruleTotal;
    }
    public int hashCode(){
       String $region;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($region = this.getRegion()) == null)? i: $region.hashCode();
       result = i1 + 59;
       String $operator = this.getOperator();
       int i2 = result * 59;
       i1 = ($operator == null)? i: $operator.hashCode();
       result = i2 + i1;
       String $ruleTotal = this.getRuleTotal();
       i2 = result * 59;
       i1 = ($ruleTotal == null)? i: $ruleTotal.hashCode();
       result = i2 + i1;
       String $resultFalgTotal = this.getResultFalgTotal();
       i1 = result * 59;
       if ($resultFalgTotal != null) {
          i = $resultFalgTotal.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setResultFalgTotal(String resultFalgTotal){
       this.resultFalgTotal = resultFalgTotal;
    }
    public void setRuleTotal(String ruleTotal){
       this.ruleTotal = ruleTotal;
    }
    public String toString(){
       return "FlyRuleStatisDto\(region="+this.getRegion()+", operator="+this.getOperator()+", ruleTotal="+this.getRuleTotal()+", resultFalgTotal="+this.getResultFalgTotal()+"\)";
    }
}
