package com.taikang.fly.check.dto.mapstruct.UserMappingImpl;
import com.taikang.fly.check.dto.mapstruct.UserMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.user.UserAddDto;
import com.taikang.fly.check.mybatis.domain.User;
import java.lang.String;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import java.lang.CharSequence;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.user.UserEditDto;
import java.lang.Integer;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.utils.DateUtils;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class UserMappingImpl implements UserMapping	// class@00017e from classes.dex
{

    public void UserMappingImpl(){
       super();
    }
    public User userAddDtoToUser(UserAddDto userAddDto){
       User user;
       if (userAddDto == null) {
          user = null;
       }else {
          user = new User();
          user.setUserCode(userAddDto.getUserCode());
          user.setName(userAddDto.getName());
          user.setRegion(userAddDto.getRegion());
          user.setPassword(new BCryptPasswordEncoder().encode(userAddDto.getPassword()));
          user.setCreator("self");
          user.setModifyTime(LocalDateTime.now());
          user.setCreateTime(LocalDateTime.now());
          user.setModby("self");
          user.setId(SequenceGenerator.getId());
          user.setStatus("1");
       }
       return user;
    }
    public User userEditDtoToUser(UserEditDto userEditDto,User user){
       User userx;
       if (userEditDto == null && user == null) {
          userx = null;
       }else {
          userx = new User();
          if (user != null) {
             userx.setUserCode(user.getUserCode());
             userx.setPassword(user.getPassword());
             userx.setStatus(user.getStatus());
             userx.setLockTime(user.getLockTime());
             userx.setFailureTimes(user.getFailureTimes());
             userx.setCreator(user.getCreator());
             userx.setCreateTime(user.getCreateTime());
          }
          userx.setName(userEditDto.getName());
          userx.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getId());
          userx.setModifyTime(LocalDateTime.now());
          userx.setId(userEditDto.getId());
          userx.setRegion(userEditDto.getRegion());
       }
       return userx;
    }
    public UserDto userToUserDto(User user){
       UserDto userDto;
       if (user == null) {
          userDto = null;
       }else {
          userDto = new UserDto();
          userDto.setId(user.getId());
          userDto.setUserCode(user.getUserCode());
          userDto.setName(user.getName());
          userDto.setStatus(user.getStatus());
          userDto.setCreator(user.getCreator());
          userDto.setModby(user.getModby());
          userDto.setRegion(user.getRegion());
          userDto.setCreateTime(DateUtils.formatLocalDateTime(user.getCreateTime()));
          userDto.setModifyTime(DateUtils.formatLocalDateTime(user.getModifyTime()));
       }
       return userDto;
    }
    public List usersToUserDtos(List users){
       List list;
       if (users == null) {
          list = null;
       }else {
          list = new ArrayList(users.size());
          Iterator iterator = users.iterator();
          while (iterator.hasNext()) {
             list.add(this.userToUserDto(iterator.next()));
          }
       }
       return list;
    }
}
