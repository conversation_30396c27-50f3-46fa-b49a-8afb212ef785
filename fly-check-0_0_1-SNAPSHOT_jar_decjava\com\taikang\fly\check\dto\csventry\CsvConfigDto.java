package com.taikang.fly.check.dto.csventry.CsvConfigDto;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class CsvConfigDto	// class@0000d4 from classes.dex
{
    private String configName;
    private LocalDateTime createTime;

    public void CsvConfigDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CsvConfigDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof CsvConfigDto) {
             b = false;
          }else {
             CsvConfigDto uCsvConfigDt = o;
             if (!uCsvConfigDt.canEqual(this)) {
                b = false;
             }else {
                String configName = this.getConfigName();
                String configName1 = uCsvConfigDt.getConfigName();
                if (configName == null) {
                   if (configName1 != null) {
                      b = false;
                   }
                }else if(configName.equals(configName1)){
                }
                LocalDateTime createTime = this.getCreateTime();
                LocalDateTime createTime1 = uCsvConfigDt.getCreateTime();
                if (createTime == null) {
                   if (createTime1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!createTime.equals(createTime1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getConfigName(){
       return this.configName;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public int hashCode(){
       String $configName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($configName = this.getConfigName()) == null)? i: $configName.hashCode();
       result = i1 + 59;
       LocalDateTime $createTime = this.getCreateTime();
       i1 = result * 59;
       if ($createTime != null) {
          i = $createTime.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setConfigName(String configName){
       this.configName = configName;
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public String toString(){
       return "CsvConfigDto\(configName="+this.getConfigName()+", createTime="+this.getCreateTime()+"\)";
    }
}
