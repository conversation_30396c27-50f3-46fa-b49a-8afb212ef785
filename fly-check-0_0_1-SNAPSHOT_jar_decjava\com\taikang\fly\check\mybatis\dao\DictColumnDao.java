package com.taikang.fly.check.mybatis.dao.DictColumnDao;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.DictEntry;
import java.util.List;

public interface abstract DictColumnDao implements BaseMapper	// class@0001e2 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    int insertExcel(DictEntry p0);
    List queryDictColumn();
    List queryDictColumnList();
    List selectDictColumn(String p0,String p1,String p2);
    int updateDictColumn(String p0,String p1);
}
