package com.taikang.fly.check.mybatis.dao.DrgRuleMapper;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.vo.drg.DrgMedicalRecordVo;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.drg.DrgCheckTasks;
import com.taikang.fly.check.mybatis.domain.DrgDrgsGroup;

public interface abstract DrgRuleMapper	// class@0001ea from classes.dex
{

    List finDrgHighCostList(Map p0);
    List findDrgMedicalListOne(Map p0);
    List findDrgMedicalListTwo(Map p0);
    DrgMedicalRecordVo getACheckAuditTaskResults(Map p0);
    List getAbnormalDischargeOfNormalCasesList(Map p0);
    List getAllDepartmentNames(String p0);
    List getAllDrgGroupsNames(String p0);
    List getApplyHighCostOutputOneDetail(Map p0);
    List getApplyHighCostOutputOneLeft(String p0);
    List getApplyHighCostOutputOneRight(String p0);
    List getApplyHighCostOutputThree(Map p0);
    List getApplyHighCostOutputThreeDetail(Map p0);
    List getApplyHighCostOutputTwo(Map p0);
    List getCheckAuditTaskResultByLocal(Map p0);
    List getCheckAuditTaskResults(Map p0);
    List getCheckAuditTaskResults1(Map p0);
    List getCheckAuditTaskSID(String p0);
    List getCheckAuditTasks(String p0);
    List getDepartmentProfitOrLossByDrgGroup(String p0,String p1,String p2);
    List getDepartmentsProfitAndLoss(String p0,String p1,String p2);
    List getDrgGroupProfitOrLossByDepartment(String p0,String p1,String p2);
    List getDrgGroups();
    List getDrgGroupsProfitAndLoss(String p0,String p1,String p2);
    List getDrgLowSets(Map p0);
    List getDrgProportion(String p0);
    List getDrgResult(String p0);
    List getDrgTime(String p0);
    List getDrgYear(String p0);
    List getLimitHighCaseList(Map p0);
    List getLimitLowCaseList(Map p0);
    List getLossDepartmentOrDrg(String p0,String p1,String p2);
    List getLowNumberCaseList(String p0);
    List getNormalCaseList(String p0,String p1);
    List getOnlyOneDrgContainsMedicalList(Map p0);
    List getProfitDepartmentOrDrg(String p0,String p1,String p2);
    List getSampleList(Map p0);
    List getTotalDepartmentsProfitAndLoss(String p0,String p1);
    List getTotalDrgGroupsProfitAndLoss(String p0,String p1);
    int insert(DrgCheckTasks p0);
    int insertDRG(DrgDrgsGroup p0);
    List mildCase(Map p0);
    String queryDataRange(String p0);
    List shortStay(Map p0);
    int update(DrgCheckTasks p0);
}
