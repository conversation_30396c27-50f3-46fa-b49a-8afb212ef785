package com.taikang.fly.check.mybatis.dao.MfromtColumnConfigMapper;
import java.util.List;
import java.lang.String;
import java.lang.Long;
import com.taikang.fly.check.mybatis.domain.MfromtColumnConfig;
import java.util.Map;

public interface abstract MfromtColumnConfigMapper	// class@000203 from classes.dex
{

    int batchDelete(List p0);
    int batchInsert(List p0);
    int deleteByBusinessId(String p0);
    int deleteByPrimaryKey(Long p0);
    int insert(MfromtColumnConfig p0);
    int insertSelective(MfromtColumnConfig p0);
    List queryList(Map p0);
    MfromtColumnConfig selectByPrimaryKey(Long p0);
    int updateByPrimaryKey(MfromtColumnConfig p0);
    int updateByPrimaryKeySelective(MfromtColumnConfig p0);
}
