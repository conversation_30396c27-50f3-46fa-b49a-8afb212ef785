package com.taikang.fly.check.vo.drg.DrgAnalysisVO;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.Long;
import java.lang.StringBuilder;

public class DrgAnalysisVO	// class@00035d from classes.dex
{
    private String dataRange;
    private Integer ruleCount;
    private Long suspiciousRuleCount;
    private Long violationRuleCount;

    public void DrgAnalysisVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgAnalysisVO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgAnalysisVO) {
             b = false;
          }else {
             DrgAnalysisVO uDrgAnalysis = o;
             if (!uDrgAnalysis.canEqual(this)) {
                b = false;
             }else {
                String dataRange = this.getDataRange();
                String dataRange1 = uDrgAnalysis.getDataRange();
                if (dataRange == null) {
                   if (dataRange1 != null) {
                      b = false;
                   }
                }else if(dataRange.equals(dataRange1)){
                }
                Integer ruleCount = this.getRuleCount();
                Integer ruleCount1 = uDrgAnalysis.getRuleCount();
                if (ruleCount == null) {
                   if (ruleCount1 != null) {
                      b = false;
                   }
                }else if(ruleCount.equals(ruleCount1)){
                }
                Long violationRul = this.getViolationRuleCount();
                Long violationRul1 = uDrgAnalysis.getViolationRuleCount();
                if (violationRul == null) {
                   if (violationRul1 != null) {
                      b = false;
                   }
                }else if(violationRul.equals(violationRul1)){
                }
                Long suspiciousRu = this.getSuspiciousRuleCount();
                Long suspiciousRu1 = uDrgAnalysis.getSuspiciousRuleCount();
                if (suspiciousRu == null) {
                   if (suspiciousRu1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!suspiciousRu.equals(suspiciousRu1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDataRange(){
       return this.dataRange;
    }
    public Integer getRuleCount(){
       return this.ruleCount;
    }
    public Long getSuspiciousRuleCount(){
       return this.suspiciousRuleCount;
    }
    public Long getViolationRuleCount(){
       return this.violationRuleCount;
    }
    public int hashCode(){
       String $dataRange;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($dataRange = this.getDataRange()) == null)? i: $dataRange.hashCode();
       result = i1 + 59;
       Integer $ruleCount = this.getRuleCount();
       int i2 = result * 59;
       i1 = ($ruleCount == null)? i: $ruleCount.hashCode();
       result = i2 + i1;
       Long $violationRuleCount = this.getViolationRuleCount();
       i2 = result * 59;
       i1 = ($violationRuleCount == null)? i: $violationRuleCount.hashCode();
       result = i2 + i1;
       Long $suspiciousRuleCount = this.getSuspiciousRuleCount();
       i1 = result * 59;
       if ($suspiciousRuleCount != null) {
          i = $suspiciousRuleCount.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDataRange(String dataRange){
       this.dataRange = dataRange;
    }
    public void setRuleCount(Integer ruleCount){
       this.ruleCount = ruleCount;
    }
    public void setSuspiciousRuleCount(Long suspiciousRuleCount){
       this.suspiciousRuleCount = suspiciousRuleCount;
    }
    public void setViolationRuleCount(Long violationRuleCount){
       this.violationRuleCount = violationRuleCount;
    }
    public String toString(){
       return "DrgAnalysisVO\(dataRange="+this.getDataRange()+", ruleCount="+this.getRuleCount()+", violationRuleCount="+this.getViolationRuleCount()+", suspiciousRuleCount="+this.getSuspiciousRuleCount()+"\)";
    }
}
