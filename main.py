from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import duckdb
import datetime

app = FastAPI()
conn = duckdb.connect('flyrule.db')

# 初始化表结构
conn.execute('''
CREATE TABLE IF NOT EXISTS rules (
    id INTEGER PRIMARY KEY,
    name TEXT,
    description TEXT,
    region TEXT,
    status TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
''')

class Rule(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    region: Optional[str] = None
    status: Optional[str] = None
    created_at: Optional[datetime.datetime] = None
    updated_at: Optional[datetime.datetime] = None

@app.post("/api/rule", response_model=Rule)
def add_rule(rule: Rule):
    now = datetime.datetime.now()
    try:
        conn.execute(
            "INSERT INTO rules VALUES (?, ?, ?, ?, ?, ?, ?)",
            (rule.id, rule.name, rule.description, rule.region, rule.status, now, now)
        )
        rule.created_at = now
        rule.updated_at = now
        return rule
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/rule/{rule_id}", response_model=Rule)
def get_rule(rule_id: int):
    result = conn.execute("SELECT * FROM rules WHERE id = ?", (rule_id,)).fetchone()
    if not result:
        raise HTTPException(status_code=404, detail="Rule not found")
    return Rule(
        id=result[0], name=result[1], description=result[2], region=result[3],
        status=result[4], created_at=result[5], updated_at=result[6]
    )

@app.get("/api/rule", response_model=List[Rule])
def list_rules():
    results = conn.execute("SELECT * FROM rules").fetchall()
    return [
        Rule(
            id=row[0], name=row[1], description=row[2], region=row[3],
            status=row[4], created_at=row[5], updated_at=row[6]
        ) for row in results
    ]

@app.put("/api/rule/{rule_id}", response_model=Rule)
def update_rule(rule_id: int, rule: Rule):
    now = datetime.datetime.now()
    result = conn.execute("SELECT * FROM rules WHERE id = ?", (rule_id,)).fetchone()
    if not result:
        raise HTTPException(status_code=404, detail="Rule not found")
    conn.execute(
        "UPDATE rules SET name=?, description=?, region=?, status=?, updated_at=? WHERE id=?",
        (rule.name, rule.description, rule.region, rule.status, now, rule_id)
    )
    rule.updated_at = now
    rule.created_at = result[5]
    return rule

@app.delete("/api/rule/{rule_id}")
def delete_rule(rule_id: int):
    result = conn.execute("SELECT * FROM rules WHERE id = ?", (rule_id,)).fetchone()
    if not result:
        raise HTTPException(status_code=404, detail="Rule not found")
    conn.execute("DELETE FROM rules WHERE id = ?", (rule_id,))
    return {"msg": "deleted"} 