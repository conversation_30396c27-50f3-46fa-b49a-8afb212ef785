package com.taikang.fly.check.mybatis.domain.ModelColumnVerfPrin;
import java.lang.Object;
import java.lang.String;
import java.lang.Float;
import java.lang.StringBuilder;

public class ModelColumnVerfPrin	// class@000256 from classes.dex
{
    private String columnDesc;
    private String columnName;
    private String columnType;
    private String id;
    private Float missingRatioError;
    private Float missingRatioWarn;
    private String tableName;

    public void ModelColumnVerfPrin(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelColumnVerfPrin;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModelColumnVerfPrin){
          b = false;
       }else {
          ModelColumnVerfPrin modelColumnV = o;
          if (!modelColumnV.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = modelColumnV.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String tableName = this.getTableName();
             String tableName1 = modelColumnV.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String columnName = this.getColumnName();
             String columnName1 = modelColumnV.getColumnName();
             if (columnName == null) {
                if (columnName1 != null) {
                   b = false;
                }
             }else if(columnName.equals(columnName1)){
             }
             String columnType = this.getColumnType();
             String columnType1 = modelColumnV.getColumnType();
             if (columnType == null) {
                if (columnType1 != null) {
                   b = false;
                }
             }else if(columnType.equals(columnType1)){
             }
             String columnDesc = this.getColumnDesc();
             String columnDesc1 = modelColumnV.getColumnDesc();
             if (columnDesc == null) {
                if (columnDesc1 != null) {
                   b = false;
                }
             }else if(columnDesc.equals(columnDesc1)){
             }
             Float missingRatio = this.getMissingRatioWarn();
             Float missingRatio1 = modelColumnV.getMissingRatioWarn();
             if (missingRatio == null) {
                if (missingRatio1 != null) {
                label_009a :
                   b = false;
                }
             }else if(missingRatio.equals(missingRatio1)){
             }
             Float missingRatio2 = this.getMissingRatioError();
             Float missingRatio3 = modelColumnV.getMissingRatioError();
             if (missingRatio2 == null) {
                if (missingRatio3 != null) {
                   b = false;
                }
             }else if(missingRatio2.equals(missingRatio3)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getColumnDesc(){
       return this.columnDesc;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnType(){
       return this.columnType;
    }
    public String getId(){
       return this.id;
    }
    public Float getMissingRatioError(){
       return this.missingRatioError;
    }
    public Float getMissingRatioWarn(){
       return this.missingRatioWarn;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $columnName = this.getColumnName();
       i2 = result * 59;
       i1 = ($columnName == null)? i: $columnName.hashCode();
       result = i2 + i1;
       String $columnType = this.getColumnType();
       i2 = result * 59;
       i1 = ($columnType == null)? i: $columnType.hashCode();
       result = i2 + i1;
       String $columnDesc = this.getColumnDesc();
       i2 = result * 59;
       i1 = ($columnDesc == null)? i: $columnDesc.hashCode();
       result = i2 + i1;
       Float missingRatio = this.getMissingRatioWarn();
       i2 = result * 59;
       i1 = (missingRatio == null)? i: missingRatio.hashCode();
       Float missingRatio1 = this.getMissingRatioError();
       i1 = (i2 + i1) * 59;
       if (missingRatio1 != null) {
          i = missingRatio1.hashCode();
       }
       return (i1 + i);
    }
    public void setColumnDesc(String columnDesc){
       this.columnDesc = columnDesc;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnType(String columnType){
       this.columnType = columnType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setMissingRatioError(Float missingRatioError){
       this.missingRatioError = missingRatioError;
    }
    public void setMissingRatioWarn(Float missingRatioWarn){
       this.missingRatioWarn = missingRatioWarn;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "ModelColumnVerfPrin\(id="+this.getId()+", tableName="+this.getTableName()+", columnName="+this.getColumnName()+", columnType="+this.getColumnType()+", columnDesc="+this.getColumnDesc()+", missingRatioWarn="+this.getMissingRatioWarn()+", missingRatioError="+this.getMissingRatioError()+"\)";
    }
}
