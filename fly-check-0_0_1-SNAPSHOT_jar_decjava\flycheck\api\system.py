"""
系统管理API
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from flycheck.database import get_db
from flycheck.models import SystemConfig

router = APIRouter()


@router.get("/config", response_model=List[dict])
async def get_system_configs(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    config_key: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """获取系统配置"""
    query = db.query(SystemConfig).filter(SystemConfig.is_deleted == "0")
    
    if config_key:
        query = query.filter(SystemConfig.config_key.contains(config_key))
    
    configs = query.offset(skip).limit(limit).all()
    return [config.to_dict() for config in configs]


@router.get("/config/{config_key}")
async def get_system_config(config_key: str, db: Session = Depends(get_db)):
    """获取单个系统配置"""
    config = db.query(SystemConfig).filter(
        SystemConfig.config_key == config_key,
        SystemConfig.is_deleted == "0"
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    return config.to_dict()


@router.post("/config")
async def create_system_config(
    config_data: dict,
    db: Session = Depends(get_db)
):
    """创建系统配置"""
    # 检查配置键是否已存在
    existing = db.query(SystemConfig).filter(
        SystemConfig.config_key == config_data['config_key']
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="配置键已存在")
    
    config = SystemConfig(**config_data)
    db.add(config)
    db.commit()
    db.refresh(config)
    
    return config.to_dict()


@router.put("/config/{config_key}")
async def update_system_config(
    config_key: str,
    config_data: dict,
    db: Session = Depends(get_db)
):
    """更新系统配置"""
    config = db.query(SystemConfig).filter(
        SystemConfig.config_key == config_key,
        SystemConfig.is_deleted == "0"
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 更新字段
    for key, value in config_data.items():
        if hasattr(config, key) and key != 'config_key':
            setattr(config, key, value)
    
    db.commit()
    db.refresh(config)
    
    return config.to_dict()


@router.delete("/config/{config_key}")
async def delete_system_config(config_key: str, db: Session = Depends(get_db)):
    """删除系统配置"""
    config = db.query(SystemConfig).filter(
        SystemConfig.config_key == config_key,
        SystemConfig.is_deleted == "0"
    ).first()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    config.soft_delete()
    db.commit()
    
    return {"message": "配置删除成功"}


@router.get("/info")
async def get_system_info():
    """获取系统信息"""
    from flycheck.config import get_settings
    import platform
    import sys
    
    settings = get_settings()
    
    return {
        "app_name": settings.app_name,
        "app_version": settings.app_version,
        "python_version": sys.version,
        "platform": platform.platform(),
        "database_url": settings.database_url,
        "debug": settings.debug,
    }


@router.get("/health")
async def health_check(db: Session = Depends(get_db)):
    """健康检查"""
    try:
        # 测试数据库连接
        db.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "database": db_status,
        "timestamp": "2024-01-01T00:00:00Z"
    }
