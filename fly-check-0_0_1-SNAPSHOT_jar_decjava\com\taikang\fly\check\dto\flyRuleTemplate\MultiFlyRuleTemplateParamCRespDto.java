package com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateParamCRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MultiFlyRuleTemplateParamCRespDto implements Serializable	// class@00011e from classes.dex
{
    private String paramCodeOne;
    private String paramCodeTwo;
    private String paramDesc;
    private String paramNameOne;
    private String paramNameTwo;
    private String paramRuleName;
    private String paramType;
    private String policyBasis;
    private String ruleSql;
    private String ruleparam;
    private static final long serialVersionUID = 0x1;

    public void MultiFlyRuleTemplateParamCRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MultiFlyRuleTemplateParamCRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MultiFlyRuleTemplateParamCRespDto){
          b = false;
       }else {
          MultiFlyRuleTemplateParamCRespDto multiFlyRule = o;
          if (!multiFlyRule.canEqual(this)) {
             b = false;
          }else {
             String paramCodeOne = this.getParamCodeOne();
             String paramCodeOne1 = multiFlyRule.getParamCodeOne();
             if (paramCodeOne == null) {
                if (paramCodeOne1 != null) {
                   b = false;
                }
             }else if(paramCodeOne.equals(paramCodeOne1)){
             }
             String paramNameOne = this.getParamNameOne();
             String paramNameOne1 = multiFlyRule.getParamNameOne();
             if (paramNameOne == null) {
                if (paramNameOne1 != null) {
                   b = false;
                }
             }else if(paramNameOne.equals(paramNameOne1)){
             }
             String paramCodeTwo = this.getParamCodeTwo();
             String paramCodeTwo1 = multiFlyRule.getParamCodeTwo();
             if (paramCodeTwo == null) {
                if (paramCodeTwo1 != null) {
                   b = false;
                }
             }else if(paramCodeTwo.equals(paramCodeTwo1)){
             }
             String paramNameTwo = this.getParamNameTwo();
             String paramNameTwo1 = multiFlyRule.getParamNameTwo();
             if (paramNameTwo == null) {
                if (paramNameTwo1 != null) {
                   b = false;
                }
             }else if(paramNameTwo.equals(paramNameTwo1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = multiFlyRule.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String paramDesc = this.getParamDesc();
             String paramDesc1 = multiFlyRule.getParamDesc();
             if (paramDesc == null) {
                if (paramDesc1 != null) {
                   b = false;
                }
             }else if(paramDesc.equals(paramDesc1)){
             }
             String paramType = this.getParamType();
             String paramType1 = multiFlyRule.getParamType();
             if (paramType == null) {
                if (paramType1 != null) {
                   b = false;
                }
             }else if(paramType.equals(paramType1)){
             }
             String paramRuleNam = this.getParamRuleName();
             String paramRuleNam1 = multiFlyRule.getParamRuleName();
             if (paramRuleNam == null) {
                if (paramRuleNam1 != null) {
                label_00cd :
                   b = false;
                }
             }else if(paramRuleNam.equals(paramRuleNam1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = multiFlyRule.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             String ruleparam = this.getRuleparam();
             String ruleparam1 = multiFlyRule.getRuleparam();
             if (ruleparam == null) {
                if (ruleparam1 != null) {
                label_00fd :
                   b = false;
                }
             }else if(ruleparam.equals(ruleparam1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getParamCodeOne(){
       return this.paramCodeOne;
    }
    public String getParamCodeTwo(){
       return this.paramCodeTwo;
    }
    public String getParamDesc(){
       return this.paramDesc;
    }
    public String getParamNameOne(){
       return this.paramNameOne;
    }
    public String getParamNameTwo(){
       return this.paramNameTwo;
    }
    public String getParamRuleName(){
       return this.paramRuleName;
    }
    public String getParamType(){
       return this.paramType;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleparam(){
       return this.ruleparam;
    }
    public int hashCode(){
       String $paramCodeOne;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($paramCodeOne = this.getParamCodeOne()) == null)? i: $paramCodeOne.hashCode();
       result = i1 + 59;
       String $paramNameOne = this.getParamNameOne();
       int i2 = result * 59;
       i1 = ($paramNameOne == null)? i: $paramNameOne.hashCode();
       result = i2 + i1;
       String $paramCodeTwo = this.getParamCodeTwo();
       i2 = result * 59;
       i1 = ($paramCodeTwo == null)? i: $paramCodeTwo.hashCode();
       result = i2 + i1;
       String $paramNameTwo = this.getParamNameTwo();
       i2 = result * 59;
       i1 = ($paramNameTwo == null)? i: $paramNameTwo.hashCode();
       result = i2 + i1;
       String $policyBasis = this.getPolicyBasis();
       i2 = result * 59;
       i1 = ($policyBasis == null)? i: $policyBasis.hashCode();
       result = i2 + i1;
       String paramDesc = this.getParamDesc();
       i2 = result * 59;
       i1 = (paramDesc == null)? i: paramDesc.hashCode();
       String paramType = this.getParamType();
       i2 = (i2 + i1) * 59;
       i1 = (paramType == null)? i: paramType.hashCode();
       String paramRuleNam = this.getParamRuleName();
       i2 = (i2 + i1) * 59;
       i1 = (paramRuleNam == null)? i: paramRuleNam.hashCode();
       String ruleSql = this.getRuleSql();
       i2 = (i2 + i1) * 59;
       i1 = (ruleSql == null)? i: ruleSql.hashCode();
       String ruleparam = this.getRuleparam();
       i1 = (i2 + i1) * 59;
       if (ruleparam != null) {
          i = ruleparam.hashCode();
       }
       return (i1 + i);
    }
    public void setParamCodeOne(String paramCodeOne){
       this.paramCodeOne = paramCodeOne;
    }
    public void setParamCodeTwo(String paramCodeTwo){
       this.paramCodeTwo = paramCodeTwo;
    }
    public void setParamDesc(String paramDesc){
       this.paramDesc = paramDesc;
    }
    public void setParamNameOne(String paramNameOne){
       this.paramNameOne = paramNameOne;
    }
    public void setParamNameTwo(String paramNameTwo){
       this.paramNameTwo = paramNameTwo;
    }
    public void setParamRuleName(String paramRuleName){
       this.paramRuleName = paramRuleName;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleparam(String ruleparam){
       this.ruleparam = ruleparam;
    }
    public String toString(){
       return "MultiFlyRuleTemplateParamCRespDto\(paramCodeOne="+this.getParamCodeOne()+", paramNameOne="+this.getParamNameOne()+", paramCodeTwo="+this.getParamCodeTwo()+", paramNameTwo="+this.getParamNameTwo()+", policyBasis="+this.getPolicyBasis()+", paramDesc="+this.getParamDesc()+", paramType="+this.getParamType()+", paramRuleName="+this.getParamRuleName()+", ruleSql="+this.getRuleSql()+", ruleparam="+this.getRuleparam()+"\)";
    }
}
