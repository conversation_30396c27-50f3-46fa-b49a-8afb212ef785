package com.taikang.fly.check.mybatis.dao.DatasourceMatchLogMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;

public interface abstract DatasourceMatchLogMapper implements BaseMapper	// class@0001e0 from classes.dex
{

    int count();
    int createMzMx(String p0);
    int createMzZd(String p0);
    int createZyMx(String p0);
    int createZyZd(String p0);
    List selectIndexes(String p0,String p1);
}
