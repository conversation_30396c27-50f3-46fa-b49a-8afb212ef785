package com.taikang.fly.check.mybatis.domain.UserModule;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class UserModule implements Serializable	// class@000273 from classes.dex
{
    private LocalDateTime createTime;
    private String creator;
    private String id;
    private String isValid;
    private String modby;
    private LocalDateTime modifyTime;
    private String moduleCode;
    private String signature;
    private String userCode;
    private static final long serialVersionUID = 0x1;

    public void UserModule(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserModule;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof UserModule){
          b = false;
       }else {
          UserModule userModule = o;
          if (!userModule.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = userModule.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String userCode = this.getUserCode();
             String userCode1 = userModule.getUserCode();
             if (userCode == null) {
                if (userCode1 != null) {
                   b = false;
                }
             }else if(userCode.equals(userCode1)){
             }
             String moduleCode = this.getModuleCode();
             String moduleCode1 = userModule.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String creator = this.getCreator();
             String creator1 = userModule.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = userModule.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = userModule.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = userModule.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = userModule.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00cd :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = userModule.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModby(){
       return this.modby;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $userCode = this.getUserCode();
       int i2 = result * 59;
       i1 = ($userCode == null)? i: $userCode.hashCode();
       result = i2 + i1;
       String $moduleCode = this.getModuleCode();
       i2 = result * 59;
       i1 = ($moduleCode == null)? i: $moduleCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       LocalDateTime $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i2 = (i2 + i1) * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String isValid = this.getIsValid();
       i1 = (i2 + i1) * 59;
       if (isValid != null) {
          i = isValid.hashCode();
       }
       return (i1 + i);
    }
    public UserModule setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
       return this;
    }
    public UserModule setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public UserModule setId(String id){
       this.id = id;
       return this;
    }
    public UserModule setIsValid(String isValid){
       this.isValid = isValid;
       return this;
    }
    public UserModule setModby(String modby){
       this.modby = modby;
       return this;
    }
    public UserModule setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public UserModule setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
       return this;
    }
    public UserModule setSignature(String signature){
       this.signature = signature;
       return this;
    }
    public UserModule setUserCode(String userCode){
       this.userCode = userCode;
       return this;
    }
    public String toString(){
       return "UserModule\(id="+this.getId()+", userCode="+this.getUserCode()+", moduleCode="+this.getModuleCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", isValid="+this.getIsValid()+"\)";
    }
}
