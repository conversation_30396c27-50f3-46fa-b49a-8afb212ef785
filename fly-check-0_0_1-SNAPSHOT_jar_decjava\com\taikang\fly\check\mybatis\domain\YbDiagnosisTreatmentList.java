package com.taikang.fly.check.mybatis.domain.YbDiagnosisTreatmentList;
import java.io.Serializable;
import java.lang.Object;
import java.lang.Long;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;
import java.lang.Class;

public class YbDiagnosisTreatmentList implements Serializable	// class@000277 from classes.dex
{
    private Long birtPaymentRatio;
    private Long busiPaymentRatio;
    private String connotation;
    private Date createTime;
    private String creator;
    private Long depaPaymentRatio;
    private Long highestPrice;
    private Long hospPaymentRatio;
    private String id;
    private Long level1HighestPrice;
    private Long level2HighestPrice;
    private Long level3HighestPrice;
    private String listExcludedContent;
    private String modby;
    private Date modifyTime;
    private String paymentCategory;
    private String paymentUnit;
    private String projectCode;
    private String projectName;
    private String remark;
    private Long retirementPrice;
    private static final long serialVersionUID = 0x1;

    public void YbDiagnosisTreatmentList(){
       super();
    }
    public Long getBirtPaymentRatio(){
       return this.birtPaymentRatio;
    }
    public Long getBusiPaymentRatio(){
       return this.busiPaymentRatio;
    }
    public String getConnotation(){
       return this.connotation;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public Long getDepaPaymentRatio(){
       return this.depaPaymentRatio;
    }
    public Long getHighestPrice(){
       return this.highestPrice;
    }
    public Long getHospPaymentRatio(){
       return this.hospPaymentRatio;
    }
    public String getId(){
       return this.id;
    }
    public Long getLevel1HighestPrice(){
       return this.level1HighestPrice;
    }
    public Long getLevel2HighestPrice(){
       return this.level2HighestPrice;
    }
    public Long getLevel3HighestPrice(){
       return this.level3HighestPrice;
    }
    public String getListExcludedContent(){
       return this.listExcludedContent;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getPaymentCategory(){
       return this.paymentCategory;
    }
    public String getPaymentUnit(){
       return this.paymentUnit;
    }
    public String getProjectCode(){
       return this.projectCode;
    }
    public String getProjectName(){
       return this.projectName;
    }
    public String getRemark(){
       return this.remark;
    }
    public Long getRetirementPrice(){
       return this.retirementPrice;
    }
    public void setBirtPaymentRatio(Long birtPaymentRatio){
       this.birtPaymentRatio = birtPaymentRatio;
    }
    public void setBusiPaymentRatio(Long busiPaymentRatio){
       this.busiPaymentRatio = busiPaymentRatio;
    }
    public void setConnotation(String connotation){
       String str = (connotation == null)? null: connotation.trim();
       this.connotation = str;
       return;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       String str = (creator == null)? null: creator.trim();
       this.creator = str;
       return;
    }
    public void setDepaPaymentRatio(Long depaPaymentRatio){
       this.depaPaymentRatio = depaPaymentRatio;
    }
    public void setHighestPrice(Long highestPrice){
       this.highestPrice = highestPrice;
    }
    public void setHospPaymentRatio(Long hospPaymentRatio){
       this.hospPaymentRatio = hospPaymentRatio;
    }
    public void setId(String id){
       String str = (id == null)? null: id.trim();
       this.id = str;
       return;
    }
    public void setLevel1HighestPrice(Long level1HighestPrice){
       this.level1HighestPrice = level1HighestPrice;
    }
    public void setLevel2HighestPrice(Long level2HighestPrice){
       this.level2HighestPrice = level2HighestPrice;
    }
    public void setLevel3HighestPrice(Long level3HighestPrice){
       this.level3HighestPrice = level3HighestPrice;
    }
    public void setListExcludedContent(String listExcludedContent){
       String str = (listExcludedContent == null)? null: listExcludedContent.trim();
       this.listExcludedContent = str;
       return;
    }
    public void setModby(String modby){
       String str = (modby == null)? null: modby.trim();
       this.modby = str;
       return;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setPaymentCategory(String paymentCategory){
       String str = (paymentCategory == null)? null: paymentCategory.trim();
       this.paymentCategory = str;
       return;
    }
    public void setPaymentUnit(String paymentUnit){
       String str = (paymentUnit == null)? null: paymentUnit.trim();
       this.paymentUnit = str;
       return;
    }
    public void setProjectCode(String projectCode){
       String str = (projectCode == null)? null: projectCode.trim();
       this.projectCode = str;
       return;
    }
    public void setProjectName(String projectName){
       String str = (projectName == null)? null: projectName.trim();
       this.projectName = str;
       return;
    }
    public void setRemark(String remark){
       String str = (remark == null)? null: remark.trim();
       this.remark = str;
       return;
    }
    public void setRetirementPrice(Long retirementPrice){
       this.retirementPrice = retirementPrice;
    }
    public String toString(){
       StringBuilder sb = "";
       sb = sb+this.getClass().getSimpleName();
       sb = sb+" [";
       StringBuilder sb1 = sb+"Hash = ";
       sb1 = sb1+this.hashCode();
       sb1 = sb+", id=";
       sb1 = sb1+this.id;
       sb1 = sb+", projectCode=";
       sb1 = sb1+this.projectCode;
       sb1 = sb+", projectName=";
       sb1 = sb1+this.projectName;
       sb1 = sb+", connotation=";
       sb1 = sb1+this.connotation;
       sb1 = sb+", listExcludedContent=";
       sb1 = sb1+this.listExcludedContent;
       sb1 = sb+", paymentUnit=";
       sb1 = sb1+this.paymentUnit;
       sb1 = sb+", paymentCategory=";
       sb1 = sb1+this.paymentCategory;
       sb1 = sb+", highestPrice=";
       sb1 = sb1+this.highestPrice;
       sb1 = sb+", level3HighestPrice=";
       sb1 = sb1+this.level3HighestPrice;
       sb1 = sb+", level2HighestPrice=";
       sb1 = sb1+this.level2HighestPrice;
       sb1 = sb+", level1HighestPrice=";
       sb1 = sb1+this.level1HighestPrice;
       sb1 = sb+", retirementPrice=";
       sb1 = sb1+this.retirementPrice;
       sb1 = sb+", depaPaymentRatio=";
       sb1 = sb1+this.depaPaymentRatio;
       sb1 = sb+", hospPaymentRatio=";
       sb1 = sb1+this.hospPaymentRatio;
       sb1 = sb+", busiPaymentRatio=";
       sb1 = sb1+this.busiPaymentRatio;
       sb1 = sb+", birtPaymentRatio=";
       sb1 = sb1+this.birtPaymentRatio;
       sb1 = sb+", remark=";
       sb1 = sb1+this.remark;
       sb1 = sb+", creator=";
       sb1 = sb1+this.creator;
       sb1 = sb+", createTime=";
       sb1 = sb1+this.createTime;
       sb1 = sb+", modby=";
       sb1 = sb1+this.modby;
       sb1 = sb+", modifyTime=";
       sb1 = sb1+this.modifyTime;
       sb1 = sb+", serialVersionUID=";
       sb1 = sb1+1;
       sb = sb+"]";
       return sb;
    }
}
