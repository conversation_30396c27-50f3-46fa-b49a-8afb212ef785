package com.taikang.fly.check.mybatis.domain.ModelQuota;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelQuota	// class@00025a from classes.dex
{
    private String calculateSqlMould;
    private String createSqlMould;
    private String id;
    private String modelId;
    private String quotaName;

    public void ModelQuota(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelQuota;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelQuota) {
             b = false;
          }else {
             ModelQuota modelQuota = o;
             if (!modelQuota.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = modelQuota.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String modelId = this.getModelId();
                String modelId1 = modelQuota.getModelId();
                if (modelId == null) {
                   if (modelId1 != null) {
                      b = false;
                   }
                }else if(modelId.equals(modelId1)){
                }
                String quotaName = this.getQuotaName();
                String quotaName1 = modelQuota.getQuotaName();
                if (quotaName == null) {
                   if (quotaName1 != null) {
                      b = false;
                   }
                }else if(quotaName.equals(quotaName1)){
                }
                String createSqlMou = this.getCreateSqlMould();
                String createSqlMou1 = modelQuota.getCreateSqlMould();
                if (createSqlMou == null) {
                   if (createSqlMou1 != null) {
                      b = false;
                   }
                }else if(createSqlMou.equals(createSqlMou1)){
                }
                String calculateSql = this.getCalculateSqlMould();
                String calculateSql1 = modelQuota.getCalculateSqlMould();
                if (calculateSql == null) {
                   if (calculateSql1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!calculateSql.equals(calculateSql1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCalculateSqlMould(){
       return this.calculateSqlMould;
    }
    public String getCreateSqlMould(){
       return this.createSqlMould;
    }
    public String getId(){
       return this.id;
    }
    public String getModelId(){
       return this.modelId;
    }
    public String getQuotaName(){
       return this.quotaName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $modelId = this.getModelId();
       int i2 = result * 59;
       i1 = ($modelId == null)? i: $modelId.hashCode();
       result = i2 + i1;
       String $quotaName = this.getQuotaName();
       i2 = result * 59;
       i1 = ($quotaName == null)? i: $quotaName.hashCode();
       result = i2 + i1;
       String $createSqlMould = this.getCreateSqlMould();
       i2 = result * 59;
       i1 = ($createSqlMould == null)? i: $createSqlMould.hashCode();
       result = i2 + i1;
       String $calculateSqlMould = this.getCalculateSqlMould();
       i1 = result * 59;
       if ($calculateSqlMould != null) {
          i = $calculateSqlMould.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCalculateSqlMould(String calculateSqlMould){
       this.calculateSqlMould = calculateSqlMould;
    }
    public void setCreateSqlMould(String createSqlMould){
       this.createSqlMould = createSqlMould;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModelId(String modelId){
       this.modelId = modelId;
    }
    public void setQuotaName(String quotaName){
       this.quotaName = quotaName;
    }
    public String toString(){
       return "ModelQuota\(id="+this.getId()+", modelId="+this.getModelId()+", quotaName="+this.getQuotaName()+", createSqlMould="+this.getCreateSqlMould()+", calculateSqlMould="+this.getCalculateSqlMould()+"\)";
    }
}
