package com.taikang.fly.check.mybatis.domain.ModelWorkflow;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class ModelWorkflow	// class@00025f from classes.dex
{
    private String calculateSql;
    private String createSql;
    private LocalDateTime createTime;
    private String errorDetail;
    private String id;
    private String modelId;
    private String modelName;
    private String modelType;
    private String otherParam;
    private String quotaName;
    private String resultTable;
    private LocalDate settleEndTime;
    private LocalDate settleStartTime;
    private char stage;
    private char status;
    private LocalDateTime updateTime;
    private String userId;

    public void ModelWorkflow(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelWorkflow;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModelWorkflow){
          b = false;
       }else {
          ModelWorkflow modelWorkflo = o;
          if (!modelWorkflo.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = modelWorkflo.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String modelId = this.getModelId();
             String modelId1 = modelWorkflo.getModelId();
             if (modelId == null) {
                if (modelId1 != null) {
                   b = false;
                }
             }else if(modelId.equals(modelId1)){
             }
             String modelName = this.getModelName();
             String modelName1 = modelWorkflo.getModelName();
             if (modelName == null) {
                if (modelName1 != null) {
                   b = false;
                }
             }else if(modelName.equals(modelName1)){
             }
             String modelType = this.getModelType();
             String modelType1 = modelWorkflo.getModelType();
             if (modelType == null) {
                if (modelType1 != null) {
                   b = false;
                }
             }else if(modelType.equals(modelType1)){
             }
             LocalDate settleStartT = this.getSettleStartTime();
             LocalDate settleStartT1 = modelWorkflo.getSettleStartTime();
             if (settleStartT == null) {
                if (settleStartT1 != null) {
                   b = false;
                }
             }else if(settleStartT.equals(settleStartT1)){
             }
             LocalDate settleEndTim = this.getSettleEndTime();
             LocalDate settleEndTim1 = modelWorkflo.getSettleEndTime();
             if (settleEndTim == null) {
                if (settleEndTim1 != null) {
                   b = false;
                }
             }else if(settleEndTim.equals(settleEndTim1)){
             }
             String createSql = this.getCreateSql();
             String createSql1 = modelWorkflo.getCreateSql();
             if (createSql == null) {
                if (createSql1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(createSql.equals(createSql1)){
             }
             String calculateSql = this.getCalculateSql();
             String calculateSql1 = modelWorkflo.getCalculateSql();
             if (calculateSql == null) {
                if (calculateSql1 != null) {
                   b = false;
                }
             }else if(calculateSql.equals(calculateSql1)){
             }
             String otherParam = this.getOtherParam();
             String otherParam1 = modelWorkflo.getOtherParam();
             if (otherParam == null) {
                if (otherParam1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(otherParam.equals(otherParam1)){
             }
             String quotaName = this.getQuotaName();
             String quotaName1 = modelWorkflo.getQuotaName();
             if (quotaName == null) {
                if (quotaName1 != null) {
                   b = false;
                }
             }else if(quotaName.equals(quotaName1)){
             }
             String resultTable = this.getResultTable();
             String resultTable1 = modelWorkflo.getResultTable();
             if (resultTable == null) {
                if (resultTable1 != null) {
                label_011b :
                   b = false;
                }
             }else if(resultTable.equals(resultTable1)){
             }
             if (this.getStatus() != modelWorkflo.getStatus()) {
                b = false;
             }else if(this.getStage() != modelWorkflo.getStage()){
                b = false;
             }else {
                String userId = this.getUserId();
                String userId1 = modelWorkflo.getUserId();
                if (userId == null) {
                   if (userId1 != null) {
                      b = false;
                   }
                }else if(userId.equals(userId1)){
                }
                LocalDateTime createTime = this.getCreateTime();
                LocalDateTime createTime1 = modelWorkflo.getCreateTime();
                if (createTime == null) {
                   if (createTime1 != null) {
                      b = false;
                   }
                }else if(createTime.equals(createTime1)){
                }
                LocalDateTime updateTime = this.getUpdateTime();
                LocalDateTime updateTime1 = modelWorkflo.getUpdateTime();
                if (updateTime == null) {
                   if (updateTime1 != null) {
                      b = false;
                   }
                }else if(updateTime.equals(updateTime1)){
                }
                String errorDetail = this.getErrorDetail();
                String errorDetail1 = modelWorkflo.getErrorDetail();
                if (errorDetail == null) {
                   if (errorDetail1 != null) {
                   label_01a3 :
                      b = false;
                   }
                }else if(errorDetail.equals(errorDetail1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getCalculateSql(){
       return this.calculateSql;
    }
    public String getCreateSql(){
       return this.createSql;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getErrorDetail(){
       return this.errorDetail;
    }
    public String getId(){
       return this.id;
    }
    public String getModelId(){
       return this.modelId;
    }
    public String getModelName(){
       return this.modelName;
    }
    public String getModelType(){
       return this.modelType;
    }
    public String getOtherParam(){
       return this.otherParam;
    }
    public String getQuotaName(){
       return this.quotaName;
    }
    public String getResultTable(){
       return this.resultTable;
    }
    public LocalDate getSettleEndTime(){
       return this.settleEndTime;
    }
    public LocalDate getSettleStartTime(){
       return this.settleStartTime;
    }
    public char getStage(){
       return this.stage;
    }
    public char getStatus(){
       return this.status;
    }
    public LocalDateTime getUpdateTime(){
       return this.updateTime;
    }
    public String getUserId(){
       return this.userId;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $modelId = this.getModelId();
       int i1 = result * 59;
       i = ($modelId == null)? 43: $modelId.hashCode();
       result = i1 + i;
       String $modelName = this.getModelName();
       i1 = result * 59;
       i = ($modelName == null)? 43: $modelName.hashCode();
       result = i1 + i;
       String $modelType = this.getModelType();
       i1 = result * 59;
       i = ($modelType == null)? 43: $modelType.hashCode();
       result = i1 + i;
       LocalDate $settleStartTime = this.getSettleStartTime();
       i1 = result * 59;
       i = ($settleStartTime == null)? 43: $settleStartTime.hashCode();
       result = i1 + i;
       LocalDate settleEndTim = this.getSettleEndTime();
       i1 = result * 59;
       i = (settleEndTim == null)? 43: settleEndTim.hashCode();
       String createSql = this.getCreateSql();
       i1 = (i1 + i) * 59;
       i = (createSql == null)? 43: createSql.hashCode();
       String calculateSql = this.getCalculateSql();
       i1 = (i1 + i) * 59;
       i = (calculateSql == null)? 43: calculateSql.hashCode();
       String otherParam = this.getOtherParam();
       i1 = (i1 + i) * 59;
       i = (otherParam == null)? 43: otherParam.hashCode();
       String quotaName = this.getQuotaName();
       i1 = (i1 + i) * 59;
       i = (quotaName == null)? 43: quotaName.hashCode();
       String resultTable = this.getResultTable();
       i1 = (i1 + i) * 59;
       i = (resultTable == null)? 43: resultTable.hashCode();
       String userId = this.getUserId();
       i1 = (((((i1 + i) * 59) + this.getStatus()) * 59) + this.getStage()) * 59;
       i = (userId == null)? 43: userId.hashCode();
       LocalDateTime createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       LocalDateTime updateTime = this.getUpdateTime();
       i1 = (i1 + i) * 59;
       i = (updateTime == null)? 43: updateTime.hashCode();
       String errorDetail = this.getErrorDetail();
       i1 = (i1 + i) * 59;
       i = (errorDetail == null)? 43: errorDetail.hashCode();
       return (i1 + i);
    }
    public void setCalculateSql(String calculateSql){
       this.calculateSql = calculateSql;
    }
    public void setCreateSql(String createSql){
       this.createSql = createSql;
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setErrorDetail(String errorDetail){
       this.errorDetail = errorDetail;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModelId(String modelId){
       this.modelId = modelId;
    }
    public void setModelName(String modelName){
       this.modelName = modelName;
    }
    public void setModelType(String modelType){
       this.modelType = modelType;
    }
    public void setOtherParam(String otherParam){
       this.otherParam = otherParam;
    }
    public void setQuotaName(String quotaName){
       this.quotaName = quotaName;
    }
    public void setResultTable(String resultTable){
       this.resultTable = resultTable;
    }
    public void setSettleEndTime(LocalDate settleEndTime){
       this.settleEndTime = settleEndTime;
    }
    public void setSettleStartTime(LocalDate settleStartTime){
       this.settleStartTime = settleStartTime;
    }
    public void setStage(char stage){
       this.stage = stage;
    }
    public void setStatus(char status){
       this.status = status;
    }
    public void setUpdateTime(LocalDateTime updateTime){
       this.updateTime = updateTime;
    }
    public void setUserId(String userId){
       this.userId = userId;
    }
    public String toString(){
       return "ModelWorkflow\(id="+this.getId()+", modelId="+this.getModelId()+", modelName="+this.getModelName()+", modelType="+this.getModelType()+", settleStartTime="+this.getSettleStartTime()+", settleEndTime="+this.getSettleEndTime()+", createSql="+this.getCreateSql()+", calculateSql="+this.getCalculateSql()+", otherParam="+this.getOtherParam()+", quotaName="+this.getQuotaName()+", resultTable="+this.getResultTable()+", status="+this.getStatus()+", stage="+this.getStage()+", userId="+this.getUserId()+", createTime="+this.getCreateTime()+", updateTime="+this.getUpdateTime()+", errorDetail="+this.getErrorDetail()+"\)";
    }
}
