package com.taikang.fly.check.mybatis.domain.drg.TkengVolaRslt;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.lang.Double;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class TkengVolaRslt	// class@00027d from classes.dex
{
    private String asocFeedetlId;
    private String asocSetlId;
    private String chkChldTaskBchno;
    private String chkReqCode;
    private String chkRslt;
    private String chkTaskBchno;
    private LocalDateTime crteTime;
    private String crterId;
    private String docNo;
    private String docVolaNatu;
    private String feedetlId;
    private String fixmedinsCode;
    private String fixmedinsName;
    private String insuNo;
    private String link;
    private String mdtrtId;
    private String oprnOprtCode;
    private String oprnOprtName;
    private String poolareaCode;
    private String poolareaName;
    private String ruleCode;
    private String ruleLv1TypeCode;
    private String ruleLv2TypeCode;
    private String ruleName;
    private String ruleNatu;
    private String rulePk;
    private LocalDate setlDate;
    private String setlId;
    private LocalDateTime updtTime;
    private String updterId;
    private Double volaAmt;
    private String volaDiagCode;
    private String volaDiagName;
    private String volaFeedetlCombSn;
    private String volaObj;
    private String volaRsltPk;

    public void TkengVolaRslt(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TkengVolaRslt;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof TkengVolaRslt){
          b = false;
       }else {
          TkengVolaRslt tkengVolaRsl = o;
          if (!tkengVolaRsl.canEqual(this)) {
             b = false;
          }else {
             String volaRsltPk = this.getVolaRsltPk();
             String volaRsltPk1 = tkengVolaRsl.getVolaRsltPk();
             if (volaRsltPk == null) {
                if (volaRsltPk1 != null) {
                   b = false;
                }
             }else if(volaRsltPk.equals(volaRsltPk1)){
             }
             String chkTaskBchno = this.getChkTaskBchno();
             String chkTaskBchno1 = tkengVolaRsl.getChkTaskBchno();
             if (chkTaskBchno == null) {
                if (chkTaskBchno1 != null) {
                   b = false;
                }
             }else if(chkTaskBchno.equals(chkTaskBchno1)){
             }
             String chkChldTaskB = this.getChkChldTaskBchno();
             String chkChldTaskB1 = tkengVolaRsl.getChkChldTaskBchno();
             if (chkChldTaskB == null) {
                if (chkChldTaskB1 != null) {
                   b = false;
                }
             }else if(chkChldTaskB.equals(chkChldTaskB1)){
             }
             String chkReqCode = this.getChkReqCode();
             String chkReqCode1 = tkengVolaRsl.getChkReqCode();
             if (chkReqCode == null) {
                if (chkReqCode1 != null) {
                   b = false;
                }
             }else if(chkReqCode.equals(chkReqCode1)){
             }
             String mdtrtId = this.getMdtrtId();
             String mdtrtId1 = tkengVolaRsl.getMdtrtId();
             if (mdtrtId == null) {
                if (mdtrtId1 != null) {
                   b = false;
                }
             }else if(mdtrtId.equals(mdtrtId1)){
             }
             String setlId = this.getSetlId();
             String setlId1 = tkengVolaRsl.getSetlId();
             if (setlId == null) {
                if (setlId1 != null) {
                label_00a7 :
                   b = false;
                }
             }else if(setlId.equals(setlId1)){
             }
             String feedetlId = this.getFeedetlId();
             String feedetlId1 = tkengVolaRsl.getFeedetlId();
             if (feedetlId == null) {
                if (feedetlId1 != null) {
                   b = false;
                }
             }else if(feedetlId.equals(feedetlId1)){
             }
             LocalDate setlDate = this.getSetlDate();
             LocalDate setlDate1 = tkengVolaRsl.getSetlDate();
             if (setlDate == null) {
                if (setlDate1 != null) {
                   b = false;
                }
             }else if(setlDate.equals(setlDate1)){
             }
             String docNo = this.getDocNo();
             String docNo1 = tkengVolaRsl.getDocNo();
             if (docNo == null) {
                if (docNo1 != null) {
                label_00f3 :
                   b = false;
                }
             }else if(docNo.equals(docNo1)){
             }
             String insuNo = this.getInsuNo();
             String insuNo1 = tkengVolaRsl.getInsuNo();
             if (insuNo == null) {
                if (insuNo1 != null) {
                   b = false;
                }
             }else if(insuNo.equals(insuNo1)){
             }
             String ruleLv1TypeC = this.getRuleLv1TypeCode();
             String ruleLv1TypeC1 = tkengVolaRsl.getRuleLv1TypeCode();
             if (ruleLv1TypeC == null) {
                if (ruleLv1TypeC1 != null) {
                label_0125 :
                   b = false;
                }
             }else if(ruleLv1TypeC.equals(ruleLv1TypeC1)){
             }
             String ruleLv2TypeC = this.getRuleLv2TypeCode();
             String ruleLv2TypeC1 = tkengVolaRsl.getRuleLv2TypeCode();
             if (ruleLv2TypeC == null) {
                if (ruleLv2TypeC1 != null) {
                   b = false;
                }
             }else if(ruleLv2TypeC.equals(ruleLv2TypeC1)){
             }
             String rulePk = this.getRulePk();
             String rulePk1 = tkengVolaRsl.getRulePk();
             if (rulePk == null) {
                if (rulePk1 != null) {
                label_0159 :
                   b = false;
                }
             }else if(rulePk.equals(rulePk1)){
             }
             String ruleCode = this.getRuleCode();
             String ruleCode1 = tkengVolaRsl.getRuleCode();
             if (ruleCode == null) {
                if (ruleCode1 != null) {
                   b = false;
                }
             }else if(ruleCode.equals(ruleCode1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = tkengVolaRsl.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                label_018d :
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleNatu = this.getRuleNatu();
             String ruleNatu1 = tkengVolaRsl.getRuleNatu();
             if (ruleNatu == null) {
                if (ruleNatu1 != null) {
                   b = false;
                }
             }else if(ruleNatu.equals(ruleNatu1)){
             }
             String chkRslt = this.getChkRslt();
             String chkRslt1 = tkengVolaRsl.getChkRslt();
             if (chkRslt == null) {
                if (chkRslt1 != null) {
                label_01c1 :
                   b = false;
                }
             }else if(chkRslt.equals(chkRslt1)){
             }
             Double volaAmt = this.getVolaAmt();
             Double volaAmt1 = tkengVolaRsl.getVolaAmt();
             if (volaAmt == null) {
                if (volaAmt1 != null) {
                   b = false;
                }
             }else if(volaAmt.equals(volaAmt1)){
             }
             String docVolaNatu = this.getDocVolaNatu();
             String docVolaNatu1 = tkengVolaRsl.getDocVolaNatu();
             if (docVolaNatu == null) {
                if (docVolaNatu1 != null) {
                   b = false;
                }
             }else if(docVolaNatu.equals(docVolaNatu1)){
             }
             String volaObj = this.getVolaObj();
             String volaObj1 = tkengVolaRsl.getVolaObj();
             if (volaObj == null) {
                if (volaObj1 != null) {
                label_020b :
                   b = false;
                }
             }else if(volaObj.equals(volaObj1)){
             }
             String volaDiagCode = this.getVolaDiagCode();
             String volaDiagCode1 = tkengVolaRsl.getVolaDiagCode();
             if (volaDiagCode == null) {
                if (volaDiagCode1 != null) {
                   b = false;
                }
             }else if(volaDiagCode.equals(volaDiagCode1)){
             }
             String volaDiagName = this.getVolaDiagName();
             String volaDiagName1 = tkengVolaRsl.getVolaDiagName();
             if (volaDiagName == null) {
                if (volaDiagName1 != null) {
                label_023f :
                   b = false;
                }
             }else if(volaDiagName.equals(volaDiagName1)){
             }
             String volaFeedetlC = this.getVolaFeedetlCombSn();
             String volaFeedetlC1 = tkengVolaRsl.getVolaFeedetlCombSn();
             if (volaFeedetlC == null) {
                if (volaFeedetlC1 != null) {
                   b = false;
                }
             }else if(volaFeedetlC.equals(volaFeedetlC1)){
             }
             String poolareaCode = this.getPoolareaCode();
             String poolareaCode1 = tkengVolaRsl.getPoolareaCode();
             if (poolareaCode == null) {
                if (poolareaCode1 != null) {
                label_0273 :
                   b = false;
                }
             }else if(poolareaCode.equals(poolareaCode1)){
             }
             String poolareaName = this.getPoolareaName();
             String poolareaName1 = tkengVolaRsl.getPoolareaName();
             if (poolareaName == null) {
                if (poolareaName1 != null) {
                   b = false;
                }
             }else if(poolareaName.equals(poolareaName1)){
             }
             String fixmedinsCod = this.getFixmedinsCode();
             String fixmedinsCod1 = tkengVolaRsl.getFixmedinsCode();
             if (fixmedinsCod == null) {
                if (fixmedinsCod1 != null) {
                label_02a7 :
                   b = false;
                }
             }else if(fixmedinsCod.equals(fixmedinsCod1)){
             }
             String fixmedinsNam = this.getFixmedinsName();
             String fixmedinsNam1 = tkengVolaRsl.getFixmedinsName();
             if (fixmedinsNam == null) {
                if (fixmedinsNam1 != null) {
                   b = false;
                }
             }else if(fixmedinsNam.equals(fixmedinsNam1)){
             }
             String asocFeedetlI = this.getAsocFeedetlId();
             String asocFeedetlI1 = tkengVolaRsl.getAsocFeedetlId();
             if (asocFeedetlI == null) {
                if (asocFeedetlI1 != null) {
                label_02d7 :
                   b = false;
                }
             }else if(asocFeedetlI.equals(asocFeedetlI1)){
             }
             String asocSetlId = this.getAsocSetlId();
             String asocSetlId1 = tkengVolaRsl.getAsocSetlId();
             if (asocSetlId == null) {
                if (asocSetlId1 != null) {
                   b = false;
                }
             }else if(asocSetlId.equals(asocSetlId1)){
             }
             String crterId = this.getCrterId();
             String crterId1 = tkengVolaRsl.getCrterId();
             if (crterId == null) {
                if (crterId1 != null) {
                label_0307 :
                   b = false;
                }
             }else if(crterId.equals(crterId1)){
             }
             LocalDateTime crteTime = this.getCrteTime();
             LocalDateTime crteTime1 = tkengVolaRsl.getCrteTime();
             if (crteTime == null) {
                if (crteTime1 != null) {
                   b = false;
                }
             }else if(crteTime.equals(crteTime1)){
             }
             String updterId = this.getUpdterId();
             String updterId1 = tkengVolaRsl.getUpdterId();
             if (updterId == null) {
                if (updterId1 != null) {
                   b = false;
                }
             }else if(updterId.equals(updterId1)){
             }
             LocalDateTime updtTime = this.getUpdtTime();
             LocalDateTime updtTime1 = tkengVolaRsl.getUpdtTime();
             if (updtTime == null) {
                if (updtTime1 != null) {
                   b = false;
                }
             }else if(updtTime.equals(updtTime1)){
             }
             String link = this.getLink();
             String link1 = tkengVolaRsl.getLink();
             if (link == null) {
                if (link1 != null) {
                   b = false;
                }
             }else if(link.equals(link1)){
             }
             String oprnOprtCode = this.getOprnOprtCode();
             String oprnOprtCode1 = tkengVolaRsl.getOprnOprtCode();
             if (oprnOprtCode == null) {
                if (oprnOprtCode1 != null) {
                label_0385 :
                   b = false;
                }
             }else if(oprnOprtCode.equals(oprnOprtCode1)){
             }
             String oprnOprtName = this.getOprnOprtName();
             String oprnOprtName1 = tkengVolaRsl.getOprnOprtName();
             if (oprnOprtName == null) {
                if (oprnOprtName1 != null) {
                   b = false;
                }
             }else if(oprnOprtName.equals(oprnOprtName1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAsocFeedetlId(){
       return this.asocFeedetlId;
    }
    public String getAsocSetlId(){
       return this.asocSetlId;
    }
    public String getChkChldTaskBchno(){
       return this.chkChldTaskBchno;
    }
    public String getChkReqCode(){
       return this.chkReqCode;
    }
    public String getChkRslt(){
       return this.chkRslt;
    }
    public String getChkTaskBchno(){
       return this.chkTaskBchno;
    }
    public LocalDateTime getCrteTime(){
       return this.crteTime;
    }
    public String getCrterId(){
       return this.crterId;
    }
    public String getDocNo(){
       return this.docNo;
    }
    public String getDocVolaNatu(){
       return this.docVolaNatu;
    }
    public String getFeedetlId(){
       return this.feedetlId;
    }
    public String getFixmedinsCode(){
       return this.fixmedinsCode;
    }
    public String getFixmedinsName(){
       return this.fixmedinsName;
    }
    public String getInsuNo(){
       return this.insuNo;
    }
    public String getLink(){
       return this.link;
    }
    public String getMdtrtId(){
       return this.mdtrtId;
    }
    public String getOprnOprtCode(){
       return this.oprnOprtCode;
    }
    public String getOprnOprtName(){
       return this.oprnOprtName;
    }
    public String getPoolareaCode(){
       return this.poolareaCode;
    }
    public String getPoolareaName(){
       return this.poolareaName;
    }
    public String getRuleCode(){
       return this.ruleCode;
    }
    public String getRuleLv1TypeCode(){
       return this.ruleLv1TypeCode;
    }
    public String getRuleLv2TypeCode(){
       return this.ruleLv2TypeCode;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleNatu(){
       return this.ruleNatu;
    }
    public String getRulePk(){
       return this.rulePk;
    }
    public LocalDate getSetlDate(){
       return this.setlDate;
    }
    public String getSetlId(){
       return this.setlId;
    }
    public LocalDateTime getUpdtTime(){
       return this.updtTime;
    }
    public String getUpdterId(){
       return this.updterId;
    }
    public Double getVolaAmt(){
       return this.volaAmt;
    }
    public String getVolaDiagCode(){
       return this.volaDiagCode;
    }
    public String getVolaDiagName(){
       return this.volaDiagName;
    }
    public String getVolaFeedetlCombSn(){
       return this.volaFeedetlCombSn;
    }
    public String getVolaObj(){
       return this.volaObj;
    }
    public String getVolaRsltPk(){
       return this.volaRsltPk;
    }
    public int hashCode(){
       String $volaRsltPk;
       int PRIME = 59;
       int result = 1;
       int i = (($volaRsltPk = this.getVolaRsltPk()) == null)? 43: $volaRsltPk.hashCode();
       result = i + 59;
       String $chkTaskBchno = this.getChkTaskBchno();
       int i1 = result * 59;
       i = ($chkTaskBchno == null)? 43: $chkTaskBchno.hashCode();
       result = i1 + i;
       String $chkChldTaskBchno = this.getChkChldTaskBchno();
       i1 = result * 59;
       i = ($chkChldTaskBchno == null)? 43: $chkChldTaskBchno.hashCode();
       result = i1 + i;
       String $chkReqCode = this.getChkReqCode();
       i1 = result * 59;
       i = ($chkReqCode == null)? 43: $chkReqCode.hashCode();
       result = i1 + i;
       String $mdtrtId = this.getMdtrtId();
       i1 = result * 59;
       i = ($mdtrtId == null)? 43: $mdtrtId.hashCode();
       result = i1 + i;
       String setlId = this.getSetlId();
       i1 = result * 59;
       i = (setlId == null)? 43: setlId.hashCode();
       String feedetlId = this.getFeedetlId();
       i1 = (i1 + i) * 59;
       i = (feedetlId == null)? 43: feedetlId.hashCode();
       LocalDate setlDate = this.getSetlDate();
       i1 = (i1 + i) * 59;
       i = (setlDate == null)? 43: setlDate.hashCode();
       String docNo = this.getDocNo();
       i1 = (i1 + i) * 59;
       i = (docNo == null)? 43: docNo.hashCode();
       String insuNo = this.getInsuNo();
       i1 = (i1 + i) * 59;
       i = (insuNo == null)? 43: insuNo.hashCode();
       String ruleLv1TypeC = this.getRuleLv1TypeCode();
       i1 = (i1 + i) * 59;
       i = (ruleLv1TypeC == null)? 43: ruleLv1TypeC.hashCode();
       String ruleLv2TypeC = this.getRuleLv2TypeCode();
       i1 = (i1 + i) * 59;
       i = (ruleLv2TypeC == null)? 43: ruleLv2TypeC.hashCode();
       String rulePk = this.getRulePk();
       i1 = (i1 + i) * 59;
       i = (rulePk == null)? 43: rulePk.hashCode();
       String ruleCode = this.getRuleCode();
       i1 = (i1 + i) * 59;
       i = (ruleCode == null)? 43: ruleCode.hashCode();
       String ruleName = this.getRuleName();
       i1 = (i1 + i) * 59;
       i = (ruleName == null)? 43: ruleName.hashCode();
       String ruleNatu = this.getRuleNatu();
       i1 = (i1 + i) * 59;
       i = (ruleNatu == null)? 43: ruleNatu.hashCode();
       String chkRslt = this.getChkRslt();
       i1 = (i1 + i) * 59;
       i = (chkRslt == null)? 43: chkRslt.hashCode();
       Double volaAmt = this.getVolaAmt();
       i1 = (i1 + i) * 59;
       i = (volaAmt == null)? 43: volaAmt.hashCode();
       String docVolaNatu = this.getDocVolaNatu();
       i1 = (i1 + i) * 59;
       i = (docVolaNatu == null)? 43: docVolaNatu.hashCode();
       String volaObj = this.getVolaObj();
       i1 = (i1 + i) * 59;
       i = (volaObj == null)? 43: volaObj.hashCode();
       String volaDiagCode = this.getVolaDiagCode();
       i1 = (i1 + i) * 59;
       i = (volaDiagCode == null)? 43: volaDiagCode.hashCode();
       String volaDiagName = this.getVolaDiagName();
       i1 = (i1 + i) * 59;
       i = (volaDiagName == null)? 43: volaDiagName.hashCode();
       String volaFeedetlC = this.getVolaFeedetlCombSn();
       i1 = (i1 + i) * 59;
       i = (volaFeedetlC == null)? 43: volaFeedetlC.hashCode();
       String poolareaCode = this.getPoolareaCode();
       i1 = (i1 + i) * 59;
       i = (poolareaCode == null)? 43: poolareaCode.hashCode();
       String poolareaName = this.getPoolareaName();
       i1 = (i1 + i) * 59;
       i = (poolareaName == null)? 43: poolareaName.hashCode();
       String fixmedinsCod = this.getFixmedinsCode();
       i1 = (i1 + i) * 59;
       i = (fixmedinsCod == null)? 43: fixmedinsCod.hashCode();
       String fixmedinsNam = this.getFixmedinsName();
       i1 = (i1 + i) * 59;
       i = (fixmedinsNam == null)? 43: fixmedinsNam.hashCode();
       String asocFeedetlI = this.getAsocFeedetlId();
       i1 = (i1 + i) * 59;
       i = (asocFeedetlI == null)? 43: asocFeedetlI.hashCode();
       String asocSetlId = this.getAsocSetlId();
       i1 = (i1 + i) * 59;
       i = (asocSetlId == null)? 43: asocSetlId.hashCode();
       String crterId = this.getCrterId();
       i1 = (i1 + i) * 59;
       i = (crterId == null)? 43: crterId.hashCode();
       LocalDateTime crteTime = this.getCrteTime();
       i1 = (i1 + i) * 59;
       i = (crteTime == null)? 43: crteTime.hashCode();
       String updterId = this.getUpdterId();
       i1 = (i1 + i) * 59;
       i = (updterId == null)? 43: updterId.hashCode();
       LocalDateTime updtTime = this.getUpdtTime();
       i1 = (i1 + i) * 59;
       i = (updtTime == null)? 43: updtTime.hashCode();
       String link = this.getLink();
       i1 = (i1 + i) * 59;
       i = (link == null)? 43: link.hashCode();
       String oprnOprtCode = this.getOprnOprtCode();
       i1 = (i1 + i) * 59;
       i = (oprnOprtCode == null)? 43: oprnOprtCode.hashCode();
       String oprnOprtName = this.getOprnOprtName();
       i1 = (i1 + i) * 59;
       i = (oprnOprtName == null)? 43: oprnOprtName.hashCode();
       return (i1 + i);
    }
    public void setAsocFeedetlId(String asocFeedetlId){
       this.asocFeedetlId = asocFeedetlId;
    }
    public void setAsocSetlId(String asocSetlId){
       this.asocSetlId = asocSetlId;
    }
    public void setChkChldTaskBchno(String chkChldTaskBchno){
       this.chkChldTaskBchno = chkChldTaskBchno;
    }
    public void setChkReqCode(String chkReqCode){
       this.chkReqCode = chkReqCode;
    }
    public void setChkRslt(String chkRslt){
       this.chkRslt = chkRslt;
    }
    public void setChkTaskBchno(String chkTaskBchno){
       this.chkTaskBchno = chkTaskBchno;
    }
    public void setCrteTime(LocalDateTime crteTime){
       this.crteTime = crteTime;
    }
    public void setCrterId(String crterId){
       this.crterId = crterId;
    }
    public void setDocNo(String docNo){
       this.docNo = docNo;
    }
    public void setDocVolaNatu(String docVolaNatu){
       this.docVolaNatu = docVolaNatu;
    }
    public void setFeedetlId(String feedetlId){
       this.feedetlId = feedetlId;
    }
    public void setFixmedinsCode(String fixmedinsCode){
       this.fixmedinsCode = fixmedinsCode;
    }
    public void setFixmedinsName(String fixmedinsName){
       this.fixmedinsName = fixmedinsName;
    }
    public void setInsuNo(String insuNo){
       this.insuNo = insuNo;
    }
    public void setLink(String link){
       this.link = link;
    }
    public void setMdtrtId(String mdtrtId){
       this.mdtrtId = mdtrtId;
    }
    public void setOprnOprtCode(String oprnOprtCode){
       this.oprnOprtCode = oprnOprtCode;
    }
    public void setOprnOprtName(String oprnOprtName){
       this.oprnOprtName = oprnOprtName;
    }
    public void setPoolareaCode(String poolareaCode){
       this.poolareaCode = poolareaCode;
    }
    public void setPoolareaName(String poolareaName){
       this.poolareaName = poolareaName;
    }
    public void setRuleCode(String ruleCode){
       this.ruleCode = ruleCode;
    }
    public void setRuleLv1TypeCode(String ruleLv1TypeCode){
       this.ruleLv1TypeCode = ruleLv1TypeCode;
    }
    public void setRuleLv2TypeCode(String ruleLv2TypeCode){
       this.ruleLv2TypeCode = ruleLv2TypeCode;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleNatu(String ruleNatu){
       this.ruleNatu = ruleNatu;
    }
    public void setRulePk(String rulePk){
       this.rulePk = rulePk;
    }
    public void setSetlDate(LocalDate setlDate){
       this.setlDate = setlDate;
    }
    public void setSetlId(String setlId){
       this.setlId = setlId;
    }
    public void setUpdtTime(LocalDateTime updtTime){
       this.updtTime = updtTime;
    }
    public void setUpdterId(String updterId){
       this.updterId = updterId;
    }
    public void setVolaAmt(Double volaAmt){
       this.volaAmt = volaAmt;
    }
    public void setVolaDiagCode(String volaDiagCode){
       this.volaDiagCode = volaDiagCode;
    }
    public void setVolaDiagName(String volaDiagName){
       this.volaDiagName = volaDiagName;
    }
    public void setVolaFeedetlCombSn(String volaFeedetlCombSn){
       this.volaFeedetlCombSn = volaFeedetlCombSn;
    }
    public void setVolaObj(String volaObj){
       this.volaObj = volaObj;
    }
    public void setVolaRsltPk(String volaRsltPk){
       this.volaRsltPk = volaRsltPk;
    }
    public String toString(){
       return "TkengVolaRslt\(volaRsltPk="+this.getVolaRsltPk()+", chkTaskBchno="+this.getChkTaskBchno()+", chkChldTaskBchno="+this.getChkChldTaskBchno()+", chkReqCode="+this.getChkReqCode()+", mdtrtId="+this.getMdtrtId()+", setlId="+this.getSetlId()+", feedetlId="+this.getFeedetlId()+", setlDate="+this.getSetlDate()+", docNo="+this.getDocNo()+", insuNo="+this.getInsuNo()+", ruleLv1TypeCode="+this.getRuleLv1TypeCode()+", ruleLv2TypeCode="+this.getRuleLv2TypeCode()+", rulePk="+this.getRulePk()+", ruleCode="+this.getRuleCode()+", ruleName="+this.getRuleName()+", ruleNatu="+this.getRuleNatu()+", chkRslt="+this.getChkRslt()+", volaAmt="+this.getVolaAmt()+", docVolaNatu="+this.getDocVolaNatu()+", volaObj="+this.getVolaObj()+", volaDiagCode="+this.getVolaDiagCode()+", volaDiagName="+this.getVolaDiagName()+", volaFeedetlCombSn="+this.getVolaFeedetlCombSn()+", poolareaCode="+this.getPoolareaCode()+", poolareaName="+this.getPoolareaName()+", fixmedinsCode="+this.getFixmedinsCode()+", fixmedinsName="+this.getFixmedinsName()+", asocFeedetlId="+this.getAsocFeedetlId()+", asocSetlId="+this.getAsocSetlId()+", crterId="+this.getCrterId()+", crteTime="+this.getCrteTime()+", updterId="+this.getUpdterId()+", updtTime="+this.getUpdtTime()+", link="+this.getLink()+", oprnOprtCode="+this.getOprnOprtCode()+", oprnOprtName="+this.getOprnOprtName()+"\)";
    }
}
