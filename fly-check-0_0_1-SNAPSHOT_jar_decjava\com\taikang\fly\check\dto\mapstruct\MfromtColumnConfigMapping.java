package com.taikang.fly.check.dto.mapstruct.MfromtColumnConfigMapping;
import com.taikang.fly.check.mybatis.domain.MfromtColumnConfig;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigRespDto;
import java.util.List;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigSaveDto;

public interface abstract MfromtColumnConfigMapping	// class@000167 from classes.dex
{

    ColConfigRespDto toColConfigRespDto(MfromtColumnConfig p0);
    List toColConfigRespDtoList(List p0);
    MfromtColumnConfig toEntity(ColConfigSaveDto p0);
}
