"""
医保基础数据模型
"""

from sqlalchemy import Column, String, Text, Numeric, DateTime

from .base import BaseModel


class DrugCatalogue(BaseModel):
    """医保药品目录"""
    
    __tablename__ = "yb_drug_catalogue"
    
    drug_code = Column(String(50), nullable=False, comment="药品编码")
    drug_name = Column(String(200), nullable=False, comment="药品名称")
    drug_type = Column(String(50), comment="药品类型")
    specification = Column(String(200), comment="规格")
    dosage_form = Column(String(100), comment="剂型")
    manufacturer = Column(String(200), comment="生产厂家")
    
    # 医保相关
    medical_insurance_code = Column(String(50), comment="医保编码")
    payment_category = Column(String(20), comment="支付类别")
    limit_price = Column(Numeric(10, 2), comment="限价")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-有效 0-无效")
    effective_date = Column(DateTime, comment="生效日期")
    expiry_date = Column(DateTime, comment="失效日期")


class DiagnosisTreatment(BaseModel):
    """医保诊疗项目目录"""
    
    __tablename__ = "yb_diagnosis_treatment"
    
    item_code = Column(String(50), nullable=False, comment="项目编码")
    item_name = Column(String(200), nullable=False, comment="项目名称")
    item_type = Column(String(50), comment="项目类型")
    unit = Column(String(20), comment="计价单位")
    price = Column(Numeric(10, 2), comment="价格")
    
    # 医保相关
    medical_insurance_code = Column(String(50), comment="医保编码")
    payment_category = Column(String(20), comment="支付类别")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-有效 0-无效")
    effective_date = Column(DateTime, comment="生效日期")
    expiry_date = Column(DateTime, comment="失效日期")


class ConsumablesList(BaseModel):
    """医用耗材目录"""
    
    __tablename__ = "yb_consumables_list"
    
    consumable_code = Column(String(50), nullable=False, comment="耗材编码")
    consumable_name = Column(String(200), nullable=False, comment="耗材名称")
    consumable_type = Column(String(50), comment="耗材类型")
    specification = Column(String(200), comment="规格型号")
    unit = Column(String(20), comment="计价单位")
    manufacturer = Column(String(200), comment="生产厂家")
    
    # 医保相关
    medical_insurance_code = Column(String(50), comment="医保编码")
    payment_category = Column(String(20), comment="支付类别")
    limit_price = Column(Numeric(10, 2), comment="限价")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-有效 0-无效")
    effective_date = Column(DateTime, comment="生效日期")
    expiry_date = Column(DateTime, comment="失效日期")


class DiagnosticEncoding(BaseModel):
    """诊断编码"""
    
    __tablename__ = "yb_diagnostic_encoding"
    
    diagnosis_code = Column(String(20), nullable=False, comment="诊断编码")
    diagnosis_name = Column(String(200), nullable=False, comment="诊断名称")
    category = Column(String(50), comment="分类")
    icd_version = Column(String(20), comment="ICD版本")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-有效 0-无效")


class OperativeEncoding(BaseModel):
    """手术编码"""
    
    __tablename__ = "yb_operative_encoding"
    
    surgery_code = Column(String(20), nullable=False, comment="手术编码")
    surgery_name = Column(String(200), nullable=False, comment="手术名称")
    category = Column(String(50), comment="分类")
    level = Column(String(20), comment="手术级别")
    
    # 状态
    status = Column(String(10), default="1", comment="状态 1-有效 0-无效")
