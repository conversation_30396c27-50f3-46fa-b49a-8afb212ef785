package com.taikang.fly.check.mybatis.domain.DataCleanRule;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class DataCleanRule	// class@000238 from classes.dex
{
    private String cleanRule;
    private LocalDateTime createdTime;
    private String creator;
    private String explain;
    private String fieldName;
    private String id;
    private String modby;
    private LocalDateTime modifyTime;
    private String status;
    private String tableName;

    public void DataCleanRule(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DataCleanRule;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DataCleanRule){
          b = false;
       }else {
          DataCleanRule uDataCleanRu = o;
          if (!uDataCleanRu.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDataCleanRu.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String tableName = this.getTableName();
             String tableName1 = uDataCleanRu.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String fieldName = this.getFieldName();
             String fieldName1 = uDataCleanRu.getFieldName();
             if (fieldName == null) {
                if (fieldName1 != null) {
                   b = false;
                }
             }else if(fieldName.equals(fieldName1)){
             }
             String cleanRule = this.getCleanRule();
             String cleanRule1 = uDataCleanRu.getCleanRule();
             if (cleanRule == null) {
                if (cleanRule1 != null) {
                   b = false;
                }
             }else if(cleanRule.equals(cleanRule1)){
             }
             String explain = this.getExplain();
             String explain1 = uDataCleanRu.getExplain();
             if (explain == null) {
                if (explain1 != null) {
                   b = false;
                }
             }else if(explain.equals(explain1)){
             }
             String status = this.getStatus();
             String status1 = uDataCleanRu.getStatus();
             if (status == null) {
                if (status1 != null) {
                label_00a1 :
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String creator = this.getCreator();
             String creator1 = uDataCleanRu.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createdTime = this.getCreatedTime();
             LocalDateTime createdTime1 = uDataCleanRu.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modby = this.getModby();
             String modby1 = uDataCleanRu.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = uDataCleanRu.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCleanRule(){
       return this.cleanRule;
    }
    public LocalDateTime getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getExplain(){
       return this.explain;
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getStatus(){
       return this.status;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $fieldName = this.getFieldName();
       i2 = result * 59;
       i1 = ($fieldName == null)? i: $fieldName.hashCode();
       result = i2 + i1;
       String $cleanRule = this.getCleanRule();
       i2 = result * 59;
       i1 = ($cleanRule == null)? i: $cleanRule.hashCode();
       result = i2 + i1;
       String $explain = this.getExplain();
       i2 = result * 59;
       i1 = ($explain == null)? i: $explain.hashCode();
       result = i2 + i1;
       String status = this.getStatus();
       i2 = result * 59;
       i1 = (status == null)? i: status.hashCode();
       String creator = this.getCreator();
       i2 = (i2 + i1) * 59;
       i1 = (creator == null)? i: creator.hashCode();
       LocalDateTime createdTime = this.getCreatedTime();
       i2 = (i2 + i1) * 59;
       i1 = (createdTime == null)? i: createdTime.hashCode();
       String modby = this.getModby();
       i2 = (i2 + i1) * 59;
       i1 = (modby == null)? i: modby.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i2 + i1) * 59;
       if (modifyTime != null) {
          i = modifyTime.hashCode();
       }
       return (i1 + i);
    }
    public void setCleanRule(String cleanRule){
       this.cleanRule = cleanRule;
    }
    public void setCreatedTime(LocalDateTime createdTime){
       this.createdTime = createdTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setExplain(String explain){
       this.explain = explain;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "DataCleanRule\(id="+this.getId()+", tableName="+this.getTableName()+", fieldName="+this.getFieldName()+", cleanRule="+this.getCleanRule()+", explain="+this.getExplain()+", status="+this.getStatus()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
