package com.taikang.fly.check.service.YbDiagnosisTreatmentListService;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.ybDiagnosisTreatmentList.YbDiagnosisTreatmentListSearchDto;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.dto.DatasourceInfo;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.DiagnosisTreatmentMapper;
import com.taikang.fly.check.dto.mapstruct.YbDiagnosisTreatmentListMapping;

public class YbDiagnosisTreatmentListService	// class@00030c from classes.dex
{
    private DiagnosisTreatmentMapper diagnosisTreatmentMapper;
    private YbDiagnosisTreatmentListMapping ybDiagnosisTreatmentListMapping;

    public void YbDiagnosisTreatmentListService(){
       super();
    }
    public NativePage queryList(Integer page,Integer size,YbDiagnosisTreatmentListSearchDto ybDiagnosisTreatmentListSearchDto){
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       String oraUserName = ThreadLocalContextHolder.getContext().getUserInfo().getDatasourceInfo().getUsername();
       List ybDiagnosisTreatmentLists = this.diagnosisTreatmentMapper.queryList(ybDiagnosisTreatmentListSearchDto, oraUserName);
       List ybDiagnosisTreatmentListRespDtoList = this.ybDiagnosisTreatmentListMapping.domainsToRespDtoList(ybDiagnosisTreatmentLists);
       NativePage pageDto = new NativePage(ybDiagnosisTreatmentListRespDtoList, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
