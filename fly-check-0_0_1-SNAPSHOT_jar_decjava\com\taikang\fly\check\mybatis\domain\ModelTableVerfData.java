package com.taikang.fly.check.mybatis.domain.ModelTableVerfData;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelTableVerfData	// class@00025b from classes.dex
{
    private String id;
    private char status;
    private String statusDetail;
    private String tableDesc;
    private String tableName;
    private String verfDataId;

    public void ModelTableVerfData(){
       super();
    }
    public void ModelTableVerfData(String id,String verfDataId,String tableName,String tableDesc,char status,String statusDetail){
       super();
       this.id = id;
       this.verfDataId = verfDataId;
       this.tableName = tableName;
       this.tableDesc = tableDesc;
       this.status = status;
       this.statusDetail = statusDetail;
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelTableVerfData;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModelTableVerfData){
          b = false;
       }else {
          ModelTableVerfData modelTableVe = o;
          if (!modelTableVe.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = modelTableVe.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String verfDataId = this.getVerfDataId();
             String verfDataId1 = modelTableVe.getVerfDataId();
             if (verfDataId == null) {
                if (verfDataId1 != null) {
                   b = false;
                }
             }else if(verfDataId.equals(verfDataId1)){
             }
             String tableName = this.getTableName();
             String tableName1 = modelTableVe.getTableName();
             if (tableName == null) {
                if (tableName1 != null) {
                   b = false;
                }
             }else if(tableName.equals(tableName1)){
             }
             String tableDesc = this.getTableDesc();
             String tableDesc1 = modelTableVe.getTableDesc();
             if (tableDesc == null) {
                if (tableDesc1 != null) {
                   b = false;
                }
             }else if(tableDesc.equals(tableDesc1)){
             }
             if (this.getStatus() != modelTableVe.getStatus()) {
                b = false;
             }else {
                String statusDetail = this.getStatusDetail();
                String statusDetail1 = modelTableVe.getStatusDetail();
                if (statusDetail == null) {
                   if (statusDetail1 != null) {
                      b = false;
                   }
                }else if(statusDetail.equals(statusDetail1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getId(){
       return this.id;
    }
    public char getStatus(){
       return this.status;
    }
    public String getStatusDetail(){
       return this.statusDetail;
    }
    public String getTableDesc(){
       return this.tableDesc;
    }
    public String getTableName(){
       return this.tableName;
    }
    public String getVerfDataId(){
       return this.verfDataId;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $verfDataId = this.getVerfDataId();
       int i2 = result * 59;
       i1 = ($verfDataId == null)? i: $verfDataId.hashCode();
       result = i2 + i1;
       String $tableName = this.getTableName();
       i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $tableDesc = this.getTableDesc();
       i2 = result * 59;
       i1 = ($tableDesc == null)? i: $tableDesc.hashCode();
       result = i2 + i1;
       result = (result * 59) + this.getStatus();
       String $statusDetail = this.getStatusDetail();
       i1 = result * 59;
       if ($statusDetail != null) {
          i = $statusDetail.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setStatus(char status){
       this.status = status;
    }
    public void setStatusDetail(String statusDetail){
       this.statusDetail = statusDetail;
    }
    public void setTableDesc(String tableDesc){
       this.tableDesc = tableDesc;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public void setVerfDataId(String verfDataId){
       this.verfDataId = verfDataId;
    }
    public String toString(){
       return "ModelTableVerfData\(id="+this.getId()+", verfDataId="+this.getVerfDataId()+", tableName="+this.getTableName()+", tableDesc="+this.getTableDesc()+", status="+this.getStatus()+", statusDetail="+this.getStatusDetail()+"\)";
    }
}
