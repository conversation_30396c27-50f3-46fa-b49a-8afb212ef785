package org.springframework.boot.loader.jar.JarFile$2;
import java.util.Enumeration;
import org.springframework.boot.loader.jar.JarFile;
import java.util.Iterator;
import java.lang.Object;
import java.util.jar.JarEntry;

class JarFile$2 implements Enumeration	// class@000554 from classes.dex
{
    final JarFile this$0;
    final Iterator val$iterator;

    void JarFile$2(JarFile this$0,Iterator p1){
       this.this$0 = this$0;
       this.val$iterator = p1;
       super();
    }
    public boolean hasMoreElements(){
       return this.val$iterator.hasNext();
    }
    public Object nextElement(){
       return this.nextElement();
    }
    public JarEntry nextElement(){
       return this.val$iterator.next();
    }
}
