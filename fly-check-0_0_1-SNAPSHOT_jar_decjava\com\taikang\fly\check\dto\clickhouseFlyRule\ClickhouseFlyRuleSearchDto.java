package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ClickhouseFlyRuleSearchDto implements Serializable	// class@0000c5 from classes.dex
{
    private String creater;
    private String diagnosisType;
    private String executionDate;
    private String operateTimeEnd;
    private String operateTimeStart;
    private String operator;
    private String region;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String ruleType;
    private String sqlName;
    private String state;
    private String submitState;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseFlyRuleSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseFlyRuleSearchDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseFlyRuleSearchDto){
          b = false;
       }else {
          ClickhouseFlyRuleSearchDto uClickhouseF = o;
          if (!uClickhouseF.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uClickhouseF.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String operator = this.getOperator();
             String operator1 = uClickhouseF.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String operateTimeS = this.getOperateTimeStart();
             String operateTimeS1 = uClickhouseF.getOperateTimeStart();
             if (operateTimeS == null) {
                if (operateTimeS1 != null) {
                   b = false;
                }
             }else if(operateTimeS.equals(operateTimeS1)){
             }
             String operateTimeE = this.getOperateTimeEnd();
             String operateTimeE1 = uClickhouseF.getOperateTimeEnd();
             if (operateTimeE == null) {
                if (operateTimeE1 != null) {
                   b = false;
                }
             }else if(operateTimeE.equals(operateTimeE1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uClickhouseF.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String region = this.getRegion();
             String region1 = uClickhouseF.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String state = this.getState();
             String state1 = uClickhouseF.getState();
             if (state == null) {
                if (state1 != null) {
                label_00bd :
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uClickhouseF.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String submitState = this.getSubmitState();
             String submitState1 = uClickhouseF.getSubmitState();
             if (submitState == null) {
                if (submitState1 != null) {
                label_00ef :
                   b = false;
                }
             }else if(submitState.equals(submitState1)){
             }
             String executionDat = this.getExecutionDate();
             String executionDat1 = uClickhouseF.getExecutionDate();
             if (executionDat == null) {
                if (executionDat1 != null) {
                   b = false;
                }
             }else if(executionDat.equals(executionDat1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uClickhouseF.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uClickhouseF.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                label_0139 :
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uClickhouseF.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uClickhouseF.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                label_0169 :
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uClickhouseF.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String creater = this.getCreater();
             String creater1 = uClickhouseF.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                label_0199 :
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getExecutionDate(){
       return this.executionDate;
    }
    public String getOperateTimeEnd(){
       return this.operateTimeEnd;
    }
    public String getOperateTimeStart(){
       return this.operateTimeStart;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public String getState(){
       return this.state;
    }
    public String getSubmitState(){
       return this.submitState;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $operator = this.getOperator();
       int i1 = result * 59;
       i = ($operator == null)? 43: $operator.hashCode();
       result = i1 + i;
       String $operateTimeStart = this.getOperateTimeStart();
       i1 = result * 59;
       i = ($operateTimeStart == null)? 43: $operateTimeStart.hashCode();
       result = i1 + i;
       String $operateTimeEnd = this.getOperateTimeEnd();
       i1 = result * 59;
       i = ($operateTimeEnd == null)? 43: $operateTimeEnd.hashCode();
       result = i1 + i;
       String $sqlName = this.getSqlName();
       i1 = result * 59;
       i = ($sqlName == null)? 43: $sqlName.hashCode();
       result = i1 + i;
       String region = this.getRegion();
       i1 = result * 59;
       i = (region == null)? 43: region.hashCode();
       String state = this.getState();
       i1 = (i1 + i) * 59;
       i = (state == null)? 43: state.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String submitState = this.getSubmitState();
       i1 = (i1 + i) * 59;
       i = (submitState == null)? 43: submitState.hashCode();
       String executionDat = this.getExecutionDate();
       i1 = (i1 + i) * 59;
       i = (executionDat == null)? 43: executionDat.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       return (i1 + i);
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setExecutionDate(String executionDate){
       this.executionDate = executionDate;
    }
    public void setOperateTimeEnd(String operateTimeEnd){
       this.operateTimeEnd = operateTimeEnd;
    }
    public void setOperateTimeStart(String operateTimeStart){
       this.operateTimeStart = operateTimeStart;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public void setState(String state){
       this.state = state;
    }
    public void setSubmitState(String submitState){
       this.submitState = submitState;
    }
    public String toString(){
       return "ClickhouseFlyRuleSearchDto\(ruleName="+this.getRuleName()+", operator="+this.getOperator()+", operateTimeStart="+this.getOperateTimeStart()+", operateTimeEnd="+this.getOperateTimeEnd()+", sqlName="+this.getSqlName()+", region="+this.getRegion()+", state="+this.getState()+", ruleType="+this.getRuleType()+", submitState="+this.getSubmitState()+", executionDate="+this.getExecutionDate()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", creater="+this.getCreater()+"\)";
    }
}
