package com.taikang.fly.check.dto.machineLearning.ResultDto;
import java.lang.Object;
import java.util.List;
import java.lang.Integer;
import java.lang.String;
import java.lang.StringBuilder;

public class ResultDto	// class@00013d from classes.dex
{
    private List columnName;
    private Integer count;
    private Integer currentPage;
    private List result;
    private Integer size;

    public void ResultDto(){
       super();
    }
    public void ResultDto(List columnName,Integer count,Integer currentPage,Integer size,List result){
       super();
       this.columnName = columnName;
       this.count = count;
       this.currentPage = currentPage;
       this.size = size;
       this.result = result;
    }
    protected boolean canEqual(Object other){
       return other instanceof ResultDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ResultDto) {
             b = false;
          }else {
             ResultDto resultDto = o;
             if (!resultDto.canEqual(this)) {
                b = false;
             }else {
                List columnName = this.getColumnName();
                List columnName1 = resultDto.getColumnName();
                if (columnName == null) {
                   if (columnName1 != null) {
                      b = false;
                   }
                }else if(columnName.equals(columnName1)){
                }
                Integer count = this.getCount();
                Integer count1 = resultDto.getCount();
                if (count == null) {
                   if (count1 != null) {
                      b = false;
                   }
                }else if(count.equals(count1)){
                }
                Integer currentPage = this.getCurrentPage();
                Integer currentPage1 = resultDto.getCurrentPage();
                if (currentPage == null) {
                   if (currentPage1 != null) {
                      b = false;
                   }
                }else if(currentPage.equals(currentPage1)){
                }
                Integer size = this.getSize();
                Integer size1 = resultDto.getSize();
                if (size == null) {
                   if (size1 != null) {
                      b = false;
                   }
                }else if(size.equals(size1)){
                }
                List result = this.getResult();
                List result1 = resultDto.getResult();
                if (result == null) {
                   if (result1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!result.equals(result1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getColumnName(){
       return this.columnName;
    }
    public Integer getCount(){
       return this.count;
    }
    public Integer getCurrentPage(){
       return this.currentPage;
    }
    public List getResult(){
       return this.result;
    }
    public Integer getSize(){
       return this.size;
    }
    public int hashCode(){
       List $columnName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($columnName = this.getColumnName()) == null)? i: $columnName.hashCode();
       result = i1 + 59;
       Integer $count = this.getCount();
       int i2 = result * 59;
       i1 = ($count == null)? i: $count.hashCode();
       result = i2 + i1;
       Integer $currentPage = this.getCurrentPage();
       i2 = result * 59;
       i1 = ($currentPage == null)? i: $currentPage.hashCode();
       result = i2 + i1;
       Integer $size = this.getSize();
       i2 = result * 59;
       i1 = ($size == null)? i: $size.hashCode();
       result = i2 + i1;
       List $result = this.getResult();
       i1 = result * 59;
       if ($result != null) {
          i = $result.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnName(List columnName){
       this.columnName = columnName;
    }
    public void setCount(Integer count){
       this.count = count;
    }
    public void setCurrentPage(Integer currentPage){
       this.currentPage = currentPage;
    }
    public void setResult(List result){
       this.result = result;
    }
    public void setSize(Integer size){
       this.size = size;
    }
    public String toString(){
       return "ResultDto\(columnName="+this.getColumnName()+", count="+this.getCount()+", currentPage="+this.getCurrentPage()+", size="+this.getSize()+", result="+this.getResult()+"\)";
    }
}
