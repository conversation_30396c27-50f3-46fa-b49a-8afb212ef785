package com.taikang.fly.check.dto.mapstruct.DataCleanRuleMapping;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleAddDto;
import com.taikang.fly.check.mybatis.domain.DataCleanRule;
import com.taikang.fly.check.dto.dataCleanRule.DataCleanRuleEditDto;

public interface abstract DataCleanRuleMapping	// class@000146 from classes.dex
{

    DataCleanRule addDto2DataCleanRule(DataCleanRuleAddDto p0);
    DataCleanRule editDto2DataCleanRule(DataCleanRuleEditDto p0);
}
