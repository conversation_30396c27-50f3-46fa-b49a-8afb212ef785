package com.taikang.fly.check.config.WebSSHWebSocketConfig;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import java.lang.Object;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import java.lang.String;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistration;
import org.springframework.web.socket.server.HandshakeInterceptor;
import com.taikang.fly.check.interceptor.WebSocketInterceptor;

public class WebSSHWebSocketConfig implements WebSocketConfigurer	// class@000092 from classes.dex
{
    WebSSHWebSocketHandler webSSHWebSocketHandler;

    public void WebSSHWebSocketConfig(){
       super();
    }
    public void registerWebSocketHandlers(WebSocketHandlerRegistry webSocketHandlerRegistry){
       String[] stringArray = new String[]{"/webssh"};
       HandshakeInterceptor[] handshakeInt = new HandshakeInterceptor[]{new WebSocketInterceptor()};
       stringArray = new String[]{"*"};
       webSocketHandlerRegistry.addHandler(this.webSSHWebSocketHandler, stringArray).addInterceptors(handshakeInt).setAllowedOrigins(stringArray);
    }
}
