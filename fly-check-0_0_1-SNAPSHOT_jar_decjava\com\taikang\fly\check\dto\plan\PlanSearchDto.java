package com.taikang.fly.check.dto.plan.PlanSearchDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanSearchDto	// class@0001a5 from classes.dex
{
    private String createTimeEnd;
    private String createTimeStart;
    private String creator;
    private String missionStatus;
    private String planName;
    private static final long serialVersionUID = 0x1;

    public void PlanSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof PlanSearchDto) {
             b = false;
          }else {
             PlanSearchDto planSearchDt = o;
             if (!planSearchDt.canEqual(this)) {
                b = false;
             }else {
                String planName = this.getPlanName();
                String planName1 = planSearchDt.getPlanName();
                if (planName == null) {
                   if (planName1 != null) {
                      b = false;
                   }
                }else if(planName.equals(planName1)){
                }
                String creator = this.getCreator();
                String creator1 = planSearchDt.getCreator();
                if (creator == null) {
                   if (creator1 != null) {
                      b = false;
                   }
                }else if(creator.equals(creator1)){
                }
                String createTimeSt = this.getCreateTimeStart();
                String createTimeSt1 = planSearchDt.getCreateTimeStart();
                if (createTimeSt == null) {
                   if (createTimeSt1 != null) {
                      b = false;
                   }
                }else if(createTimeSt.equals(createTimeSt1)){
                }
                String createTimeEn = this.getCreateTimeEnd();
                String createTimeEn1 = planSearchDt.getCreateTimeEnd();
                if (createTimeEn == null) {
                   if (createTimeEn1 != null) {
                      b = false;
                   }
                }else if(createTimeEn.equals(createTimeEn1)){
                }
                String missionStatu = this.getMissionStatus();
                String missionStatu1 = planSearchDt.getMissionStatus();
                if (missionStatu == null) {
                   if (missionStatu1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!missionStatu.equals(missionStatu1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCreateTimeEnd(){
       return this.createTimeEnd;
    }
    public String getCreateTimeStart(){
       return this.createTimeStart;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getMissionStatus(){
       return this.missionStatus;
    }
    public String getPlanName(){
       return this.planName;
    }
    public int hashCode(){
       String $planName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($planName = this.getPlanName()) == null)? i: $planName.hashCode();
       result = i1 + 59;
       String $creator = this.getCreator();
       int i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTimeStart = this.getCreateTimeStart();
       i2 = result * 59;
       i1 = ($createTimeStart == null)? i: $createTimeStart.hashCode();
       result = i2 + i1;
       String $createTimeEnd = this.getCreateTimeEnd();
       i2 = result * 59;
       i1 = ($createTimeEnd == null)? i: $createTimeEnd.hashCode();
       result = i2 + i1;
       String $missionStatus = this.getMissionStatus();
       i1 = result * 59;
       if ($missionStatus != null) {
          i = $missionStatus.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCreateTimeEnd(String createTimeEnd){
       this.createTimeEnd = createTimeEnd;
    }
    public void setCreateTimeStart(String createTimeStart){
       this.createTimeStart = createTimeStart;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setMissionStatus(String missionStatus){
       this.missionStatus = missionStatus;
    }
    public void setPlanName(String planName){
       this.planName = planName;
    }
    public String toString(){
       return "PlanSearchDto\(planName="+this.getPlanName()+", creator="+this.getCreator()+", createTimeStart="+this.getCreateTimeStart()+", createTimeEnd="+this.getCreateTimeEnd()+", missionStatus="+this.getMissionStatus()+"\)";
    }
}
