package com.taikang.fly.check.dto.mapstruct.MultiFlyRuleTemplateMappingImpl;
import com.taikang.fly.check.dto.mapstruct.MultiFlyRuleTemplateMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateAddDto;
import com.taikang.fly.check.mybatis.domain.MultiFlyRuleTemplate;
import java.lang.String;
import java.util.Date;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateRespDto;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZoneId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateUpdateDto;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateEditDto;

public class MultiFlyRuleTemplateMappingImpl implements MultiFlyRuleTemplateMapping	// class@00016e from classes.dex
{

    public void MultiFlyRuleTemplateMappingImpl(){
       super();
    }
    public MultiFlyRuleTemplate addDtoToDomain(MultiFlyRuleTemplateAddDto addDto){
       MultiFlyRuleTemplate multiFlyRule;
       if (addDto == null) {
          multiFlyRule = null;
       }else {
          multiFlyRule = new MultiFlyRuleTemplate();
          multiFlyRule.setRuleLevel(addDto.getRuleLevel());
          multiFlyRule.setRuleCategory1(addDto.getRuleCategory1());
          multiFlyRule.setRuleCategory2(addDto.getRuleCategory2());
          multiFlyRule.setDiagnosisType(addDto.getDiagnosisType());
          multiFlyRule.setRuleName(addDto.getRuleName());
          multiFlyRule.setRuleDescribe(addDto.getRuleDescribe());
          multiFlyRule.setPs(addDto.getPs());
          multiFlyRule.setSqlTemplate(addDto.getSqlTemplate());
          multiFlyRule.setSqlExample(addDto.getSqlExample());
          multiFlyRule.setParameter(addDto.getParameter());
          multiFlyRule.setTemplateType(addDto.getTemplateType());
          multiFlyRule.setTemplateName(addDto.getTemplateName());
          multiFlyRule.setCreatedTime(new Date());
          multiFlyRule.setOperateTime(new Date());
       }
       return multiFlyRule;
    }
    public MultiFlyRuleTemplateRespDto domainToInfoDto(MultiFlyRuleTemplate domain){
       MultiFlyRuleTemplateRespDto multiFlyRule;
       if (domain == null) {
          multiFlyRule = null;
       }else {
          multiFlyRule = new MultiFlyRuleTemplateRespDto();
          multiFlyRule.setRuleName(domain.getRuleName());
          multiFlyRule.setRuleLevel(domain.getRuleLevel());
          multiFlyRule.setRuleCategory1(domain.getRuleCategory1());
          multiFlyRule.setRuleCategory2(domain.getRuleCategory2());
          multiFlyRule.setDiagnosisType(domain.getDiagnosisType());
          multiFlyRule.setRuleDescribe(domain.getRuleDescribe());
          multiFlyRule.setPs(domain.getPs());
          multiFlyRule.setSqlTemplate(domain.getSqlTemplate());
          multiFlyRule.setSqlExample(domain.getSqlExample());
          multiFlyRule.setParameter(domain.getParameter());
          multiFlyRule.setCreater(domain.getCreater());
          multiFlyRule.setOperator(domain.getOperator());
          if (domain.getOperateTime() != null) {
             multiFlyRule.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             multiFlyRule.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          multiFlyRule.setId(domain.getId());
          multiFlyRule.setRemoved(domain.getRemoved());
          multiFlyRule.setTemplateType(domain.getTemplateType());
          multiFlyRule.setTemplateName(domain.getTemplateName());
       }
       return multiFlyRule;
    }
    public MultiFlyRuleTemplateUpdateDto domainToUpdateDto(MultiFlyRuleTemplate domain){
       MultiFlyRuleTemplateUpdateDto multiFlyRule;
       if (domain == null) {
          multiFlyRule = null;
       }else {
          multiFlyRule = new MultiFlyRuleTemplateUpdateDto();
          multiFlyRule.setId(domain.getId());
          multiFlyRule.setRuleName(domain.getRuleName());
          multiFlyRule.setSqlTemplate(domain.getSqlTemplate());
          multiFlyRule.setSqlExample(domain.getSqlExample());
       }
       return multiFlyRule;
    }
    public MultiFlyRuleTemplateAddDto domainToaddDto(MultiFlyRuleTemplate flyRuleTemplate){
       MultiFlyRuleTemplateAddDto multiFlyRule;
       if (flyRuleTemplate == null) {
          multiFlyRule = null;
       }else {
          multiFlyRule = new MultiFlyRuleTemplateAddDto();
          multiFlyRule.setRuleName(flyRuleTemplate.getRuleName());
          multiFlyRule.setRuleLevel(flyRuleTemplate.getRuleLevel());
          multiFlyRule.setRuleCategory1(flyRuleTemplate.getRuleCategory1());
          multiFlyRule.setRuleCategory2(flyRuleTemplate.getRuleCategory2());
          multiFlyRule.setDiagnosisType(flyRuleTemplate.getDiagnosisType());
          multiFlyRule.setRuleDescribe(flyRuleTemplate.getRuleDescribe());
          multiFlyRule.setPs(flyRuleTemplate.getPs());
          multiFlyRule.setSqlTemplate(flyRuleTemplate.getSqlTemplate());
          multiFlyRule.setSqlExample(flyRuleTemplate.getSqlExample());
          multiFlyRule.setParameter(flyRuleTemplate.getParameter());
          multiFlyRule.setTemplateType(flyRuleTemplate.getTemplateType());
          multiFlyRule.setTemplateName(flyRuleTemplate.getTemplateName());
       }
       return multiFlyRule;
    }
    public MultiFlyRuleTemplate dtoToDomain(MultiFlyRuleTemplateRespDto flyRuleTemplateRespDto){
       MultiFlyRuleTemplate multiFlyRule;
       if (flyRuleTemplateRespDto == null) {
          multiFlyRule = null;
       }else {
          multiFlyRule = new MultiFlyRuleTemplate();
          multiFlyRule.setId(flyRuleTemplateRespDto.getId());
          multiFlyRule.setRuleLevel(flyRuleTemplateRespDto.getRuleLevel());
          multiFlyRule.setRuleCategory1(flyRuleTemplateRespDto.getRuleCategory1());
          multiFlyRule.setRuleCategory2(flyRuleTemplateRespDto.getRuleCategory2());
          multiFlyRule.setDiagnosisType(flyRuleTemplateRespDto.getDiagnosisType());
          multiFlyRule.setRuleName(flyRuleTemplateRespDto.getRuleName());
          multiFlyRule.setRuleDescribe(flyRuleTemplateRespDto.getRuleDescribe());
          multiFlyRule.setPs(flyRuleTemplateRespDto.getPs());
          multiFlyRule.setSqlTemplate(flyRuleTemplateRespDto.getSqlTemplate());
          multiFlyRule.setSqlExample(flyRuleTemplateRespDto.getSqlExample());
          multiFlyRule.setParameter(flyRuleTemplateRespDto.getParameter());
          multiFlyRule.setCreater(flyRuleTemplateRespDto.getCreater());
          multiFlyRule.setOperator(flyRuleTemplateRespDto.getOperator());
          multiFlyRule.setRemoved(flyRuleTemplateRespDto.getRemoved());
          multiFlyRule.setTemplateType(flyRuleTemplateRespDto.getTemplateType());
          multiFlyRule.setTemplateName(flyRuleTemplateRespDto.getTemplateName());
          multiFlyRule.setCreatedTime(new Date());
          multiFlyRule.setOperateTime(new Date());
       }
       return multiFlyRule;
    }
    public List dtoToDomains(List flyRuleTemplateRespDtos){
       List list;
       if (flyRuleTemplateRespDtos == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplateRespDtos.size());
          Iterator iterator = flyRuleTemplateRespDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.dtoToDomain(iterator.next()));
          }
       }
       return list;
    }
    public MultiFlyRuleTemplate editDtoToDomain(MultiFlyRuleTemplateEditDto editDto,MultiFlyRuleTemplate domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleName(editDto.getRuleName());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setPs(editDto.getPs());
          domain.setSqlTemplate(editDto.getSqlTemplate());
          domain.setSqlExample(editDto.getSqlExample());
          domain.setParameter(editDto.getParameter());
          domain.setTemplateType(editDto.getTemplateType());
          domain.setTemplateName(editDto.getTemplateName());
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public List entityToDtos(List flyRuleTemplate){
       List list;
       if (flyRuleTemplate == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplate.size());
          Iterator iterator = flyRuleTemplate.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoDto(iterator.next()));
          }
       }
       return list;
    }
    public List entityToUpdateDtos(List flyRuleTemplate){
       List list;
       if (flyRuleTemplate == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplate.size());
          Iterator iterator = flyRuleTemplate.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToUpdateDto(iterator.next()));
          }
       }
       return list;
    }
    public MultiFlyRuleTemplate updateDtoToDomain(MultiFlyRuleTemplateUpdateDto addDto){
       MultiFlyRuleTemplate multiFlyRule;
       if (addDto == null) {
          multiFlyRule = null;
       }else {
          multiFlyRule = new MultiFlyRuleTemplate();
          multiFlyRule.setId(addDto.getId());
          multiFlyRule.setRuleName(addDto.getRuleName());
          multiFlyRule.setSqlTemplate(addDto.getSqlTemplate());
          multiFlyRule.setSqlExample(addDto.getSqlExample());
       }
       return multiFlyRule;
    }
}
