package com.taikang.fly.check.mybatis.domain.RoleMenu;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class RoleMenu	// class@000269 from classes.dex
{
    private Date createTime;
    private String creator;
    private String id;
    private String isValid;
    private String menuCode;
    private String modby;
    private Date modifyTime;
    private String moduleCode;
    private String roleCode;
    private String signature;

    public void RoleMenu(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleMenu;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof RoleMenu){
          b = false;
       }else {
          RoleMenu roleMenu = o;
          if (!roleMenu.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = roleMenu.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = roleMenu.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String menuCode = this.getMenuCode();
             String menuCode1 = roleMenu.getMenuCode();
             if (menuCode == null) {
                if (menuCode1 != null) {
                   b = false;
                }
             }else if(menuCode.equals(menuCode1)){
             }
             String creator = this.getCreator();
             String creator1 = roleMenu.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = roleMenu.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_0087 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = roleMenu.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = roleMenu.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String moduleCode = this.getModuleCode();
             String moduleCode1 = roleMenu.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String signature = this.getSignature();
             String signature1 = roleMenu.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00e5 :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = roleMenu.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getMenuCode(){
       return this.menuCode;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $roleCode = this.getRoleCode();
       int i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $menuCode = this.getMenuCode();
       i2 = result * 59;
       i1 = ($menuCode == null)? i: $menuCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String moduleCode = this.getModuleCode();
       i2 = (i2 + i1) * 59;
       i1 = (moduleCode == null)? i: moduleCode.hashCode();
       String signature = this.getSignature();
       i2 = (i2 + i1) * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String isValid = this.getIsValid();
       i1 = (i2 + i1) * 59;
       if (isValid != null) {
          i = isValid.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setMenuCode(String menuCode){
       this.menuCode = menuCode;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public String toString(){
       return "RoleMenu\(id="+this.getId()+", roleCode="+this.getRoleCode()+", menuCode="+this.getMenuCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", moduleCode="+this.getModuleCode()+", signature="+this.getSignature()+", isValid="+this.getIsValid()+"\)";
    }
}
