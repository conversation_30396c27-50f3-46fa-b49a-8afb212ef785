package com.taikang.fly.check.dto.mapstruct.FlyRuleTemplateMappingImpl;
import com.taikang.fly.check.dto.mapstruct.FlyRuleTemplateMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRuleTemplate;
import java.lang.String;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.util.Date;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateRespDto;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZoneId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.taikang.fly.check.utils.SequenceGenerator;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateEditDto;

public class FlyRuleTemplateMappingImpl implements FlyRuleTemplateMapping	// class@00015b from classes.dex
{

    public void FlyRuleTemplateMappingImpl(){
       super();
    }
    public FlyRuleTemplate addDtoToDomain(FlyRuleTemplateAddDto addDto){
       FlyRuleTemplate uFlyRuleTemp;
       if (addDto == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplate();
          uFlyRuleTemp.setRuleLevel(addDto.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(addDto.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(addDto.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(addDto.getDiagnosisType());
          uFlyRuleTemp.setRuleName(addDto.getRuleName());
          uFlyRuleTemp.setRuleDescribe(addDto.getRuleDescribe());
          uFlyRuleTemp.setPs(addDto.getPs());
          uFlyRuleTemp.setSqlTemplate(addDto.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(addDto.getSqlExample());
          uFlyRuleTemp.setParameter(addDto.getParameter());
          uFlyRuleTemp.setPolicyBasis(addDto.getPolicyBasis());
          uFlyRuleTemp.setCreater(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRuleTemp.setCreatedTime(new Date());
          uFlyRuleTemp.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRuleTemp.setOperateTime(new Date());
       }
       return uFlyRuleTemp;
    }
    public FlyRuleTemplateRespDto domainToInfoDto(FlyRuleTemplate domain){
       FlyRuleTemplateRespDto uFlyRuleTemp;
       if (domain == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplateRespDto();
          uFlyRuleTemp.setRuleName(domain.getRuleName());
          uFlyRuleTemp.setRuleLevel(domain.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(domain.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(domain.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(domain.getDiagnosisType());
          uFlyRuleTemp.setRuleDescribe(domain.getRuleDescribe());
          uFlyRuleTemp.setPs(domain.getPs());
          uFlyRuleTemp.setSqlTemplate(domain.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(domain.getSqlExample());
          uFlyRuleTemp.setParameter(domain.getParameter());
          uFlyRuleTemp.setCreater(domain.getCreater());
          uFlyRuleTemp.setOperator(domain.getOperator());
          if (domain.getOperateTime() != null) {
             uFlyRuleTemp.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             uFlyRuleTemp.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          uFlyRuleTemp.setId(domain.getId());
          uFlyRuleTemp.setRemoved(domain.getRemoved());
          uFlyRuleTemp.setPolicyBasis(domain.getPolicyBasis());
          uFlyRuleTemp.setRuleType(domain.getRuleType());
          uFlyRuleTemp.setIsSpecial(domain.getIsSpecial());
          uFlyRuleTemp.setRuleSuitTimeStart(domain.getRuleSuitTimeStart());
          uFlyRuleTemp.setRuleSuitTimeEnd(domain.getRuleSuitTimeEnd());
          uFlyRuleTemp.setRuleDimension(domain.getRuleDimension());
       }
       return uFlyRuleTemp;
    }
    public FlyRuleTemplateAddDto domainToaddDto(FlyRuleTemplate flyRuleTemplate){
       FlyRuleTemplateAddDto uFlyRuleTemp;
       if (flyRuleTemplate == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplateAddDto();
          uFlyRuleTemp.setRuleName(flyRuleTemplate.getRuleName());
          uFlyRuleTemp.setRuleLevel(flyRuleTemplate.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(flyRuleTemplate.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(flyRuleTemplate.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(flyRuleTemplate.getDiagnosisType());
          uFlyRuleTemp.setRuleDescribe(flyRuleTemplate.getRuleDescribe());
          uFlyRuleTemp.setPs(flyRuleTemplate.getPs());
          uFlyRuleTemp.setSqlTemplate(flyRuleTemplate.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(flyRuleTemplate.getSqlExample());
          uFlyRuleTemp.setParameter(flyRuleTemplate.getParameter());
          uFlyRuleTemp.setPolicyBasis(flyRuleTemplate.getPolicyBasis());
       }
       return uFlyRuleTemp;
    }
    public FlyRuleTemplate dtoToDomain(FlyRuleTemplateRespDto flyRuleTemplateRespDto){
       FlyRuleTemplate uFlyRuleTemp;
       if (flyRuleTemplateRespDto == null) {
          uFlyRuleTemp = null;
       }else {
          uFlyRuleTemp = new FlyRuleTemplate();
          uFlyRuleTemp.setRuleLevel(flyRuleTemplateRespDto.getRuleLevel());
          uFlyRuleTemp.setRuleCategory1(flyRuleTemplateRespDto.getRuleCategory1());
          uFlyRuleTemp.setRuleCategory2(flyRuleTemplateRespDto.getRuleCategory2());
          uFlyRuleTemp.setDiagnosisType(flyRuleTemplateRespDto.getDiagnosisType());
          uFlyRuleTemp.setRuleName(flyRuleTemplateRespDto.getRuleName());
          uFlyRuleTemp.setRuleDescribe(flyRuleTemplateRespDto.getRuleDescribe());
          uFlyRuleTemp.setPs(flyRuleTemplateRespDto.getPs());
          uFlyRuleTemp.setSqlTemplate(flyRuleTemplateRespDto.getSqlTemplate());
          uFlyRuleTemp.setSqlExample(flyRuleTemplateRespDto.getSqlExample());
          uFlyRuleTemp.setParameter(flyRuleTemplateRespDto.getParameter());
          uFlyRuleTemp.setCreater(flyRuleTemplateRespDto.getCreater());
          uFlyRuleTemp.setOperator(flyRuleTemplateRespDto.getOperator());
          uFlyRuleTemp.setPolicyBasis(flyRuleTemplateRespDto.getPolicyBasis());
          uFlyRuleTemp.setRuleType(flyRuleTemplateRespDto.getRuleType());
          uFlyRuleTemp.setIsSpecial(flyRuleTemplateRespDto.getIsSpecial());
          uFlyRuleTemp.setRuleSuitTimeStart(flyRuleTemplateRespDto.getRuleSuitTimeStart());
          uFlyRuleTemp.setRuleSuitTimeEnd(flyRuleTemplateRespDto.getRuleSuitTimeEnd());
          uFlyRuleTemp.setRuleDimension(flyRuleTemplateRespDto.getRuleDimension());
          uFlyRuleTemp.setCreatedTime(new Date());
          uFlyRuleTemp.setId(SequenceGenerator.getId());
          uFlyRuleTemp.setRemoved("1");
          uFlyRuleTemp.setOperateTime(new Date());
       }
       return uFlyRuleTemp;
    }
    public List dtoToDomains(List flyRuleTemplateRespDtos){
       List list;
       if (flyRuleTemplateRespDtos == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplateRespDtos.size());
          Iterator iterator = flyRuleTemplateRespDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.dtoToDomain(iterator.next()));
          }
       }
       return list;
    }
    public FlyRuleTemplate editDtoToDomain(FlyRuleTemplateEditDto editDto,FlyRuleTemplate domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleName(editDto.getRuleName());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setPs(editDto.getPs());
          domain.setSqlTemplate(editDto.getSqlTemplate());
          domain.setSqlExample(editDto.getSqlExample());
          domain.setParameter(editDto.getParameter());
          domain.setPolicyBasis(editDto.getPolicyBasis());
          domain.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public List entityToDtos(List flyRuleTemplate){
       List list;
       if (flyRuleTemplate == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleTemplate.size());
          Iterator iterator = flyRuleTemplate.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoDto(iterator.next()));
          }
       }
       return list;
    }
}
